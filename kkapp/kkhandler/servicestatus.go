package kkhandler

import (
	"net/http"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/kkmiddleware"
	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/pkg/log"
)

// GetServiceStatus retrieve "keywords:v1:search:json" from meta redis
func GetServiceStatus(w http.ResponseWriter, r *http.Request) {
	response := model.MakeOk()

	val := r.Context().Value(kkmiddleware.ServiceStatusContextKey)
	ss, ok := val.(*model.ServiceStatus)

	if !ok {
		log.Warn("v3GetServiceStatusHandler: type assertion").Interface("ServiceStatus", val).Send()
	} else {
		response.Data = ss
	}
	kkapp.App.Render.JSON(w, http.StatusOK, response)
}
