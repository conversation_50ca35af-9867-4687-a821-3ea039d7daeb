package kkhandler

import (
	"testing"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/wrapper/meta"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/KKTV/kktv-api-v3/pkg/clock"
	"github.com/KKTV/kktv-api-v3/pkg/key"
	modelsearch "github.com/KKTV/kktv-api-v3/pkg/model/search"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

type SitemapTestSuite struct {
	suite.Suite
	mockCtrl *gomock.Controller

	mockTitleRepo     *meta.MockTitleRepository
	mockTitlelistRepo *meta.MockTitlelistRepository
	mockCacheWriter   *cache.MockCacher
	mockClock         *clock.MockClock
	handler           *sitemapHandler
}

func TestSitemapTestSuite(t *testing.T) {
	suite.Run(t, new(SitemapTestSuite))
}

func (suite *SitemapTestSuite) SetupTest() {
	suite.mockCtrl = gomock.NewController(suite.T())
	suite.mockTitleRepo = meta.NewMockTitleRepository(suite.mockCtrl)
	suite.mockTitlelistRepo = meta.NewMockTitlelistRepository(suite.mockCtrl)
	suite.mockCacheWriter = cache.NewMockCacher(suite.mockCtrl)
	suite.mockClock = clock.NewMockClock(suite.mockCtrl)
	suite.handler = &sitemapHandler{
		titleRepo:       suite.mockTitleRepo,
		titlelistRepo:   suite.mockTitlelistRepo,
		metaCacheWriter: suite.mockCacheWriter,
		clock:           suite.mockClock,
	}
}

func (suite *SitemapTestSuite) TearDownTest() {
	suite.mockCtrl.Finish()
}

func (suite *SitemapTestSuite) TestGetFigure() {
	sampleTitles := []*modelsearch.Title{
		{
			Figures: []string{"演員A", "演員B"},
		},
		{
			Figures: []string{"演員B", "演員C"}, // 重複的演員
		},
	}

	testCases := []struct {
		name           string
		given          func()
		expectedResult []string
		expectedError  error
	}{
		{
			name: "successfully WHEN cache hit",
			given: func() {
				suite.mockCacheWriter.EXPECT().
					Once(key.GetMetaDataOnListedTitles(), gomock.Any(), ttlListedTitlesViewsCache, gomock.Any()).
					DoAndReturn(func(key string, v interface{}, ttl time.Duration, f func() (interface{}, error)) error {
						titles := v.(*[]*modelsearch.Title)
						*titles = sampleTitles
						return nil
					})
			},
			expectedResult: []string{"演員A", "演員B", "演員C"},
			expectedError:  nil,
		},
		{
			name: "successfully WHEN cache miss and get from repository",
			given: func() {
				suite.mockCacheWriter.EXPECT().
					Once(key.GetMetaDataOnListedTitles(), gomock.Any(), ttlListedTitlesViewsCache, gomock.Any()).
					DoAndReturn(func(key string, v interface{}, ttl time.Duration, f func() (interface{}, error)) error {
						result, err := f()
						if err != nil {
							return err
						}
						titles := v.(*[]*modelsearch.Title)
						*titles = result.([]*modelsearch.Title)
						return nil
					})

				suite.mockTitleRepo.EXPECT().
					ListAllOnListedTitles().
					Return(sampleTitles, nil)
			},
			expectedResult: []string{"演員A", "演員B", "演員C"},
			expectedError:  nil,
		},
		{
			name: "got error WHEN cache operation failed",
			given: func() {
				suite.mockCacheWriter.EXPECT().
					Once(key.GetMetaDataOnListedTitles(), gomock.Any(), ttlListedTitlesViewsCache, gomock.Any()).
					Return(assert.AnError)
			},
			expectedResult: nil,
			expectedError:  assert.AnError,
		},
		{
			name: "got error WHEN repository operation failed",
			given: func() {
				suite.mockCacheWriter.EXPECT().
					Once(key.GetMetaDataOnListedTitles(), gomock.Any(), ttlListedTitlesViewsCache, gomock.Any()).
					DoAndReturn(func(key string, v interface{}, ttl time.Duration, f func() (interface{}, error)) error {
						_, err := f()
						return err
					})

				suite.mockTitleRepo.EXPECT().
					ListAllOnListedTitles().
					Return(nil, assert.AnError)
			},
			expectedResult: nil,
			expectedError:  assert.AnError,
		},
		{
			name: "got empty result WHEN no titles in cache or repository",
			given: func() {
				suite.mockCacheWriter.EXPECT().
					Once(key.GetMetaDataOnListedTitles(), gomock.Any(), ttlListedTitlesViewsCache, gomock.Any()).
					DoAndReturn(func(key string, v interface{}, ttl time.Duration, f func() (interface{}, error)) error {
						titles := v.(*[]*modelsearch.Title)
						*titles = []*modelsearch.Title{}
						return nil
					})
			},
			expectedResult: nil,
			expectedError:  nil,
		},
	}

	for _, tc := range testCases {
		suite.Run(tc.name, func() {
			tc.given()

			result, err := suite.handler.getFigure()

			if tc.expectedError != nil {
				suite.Assert().ErrorIs(err, tc.expectedError)
			} else {
				suite.Assert().NoError(err)
				suite.Assert().ElementsMatch(tc.expectedResult, result)
			}
		})
	}
}

func (suite *SitemapTestSuite) TestGetCollections() {
	sampleTitles := []*modelsearch.Title{
		{
			Country:       "Korea",
			Themes:        []string{"愛情", "喜劇"},
			Tags:          []string{"浪漫", "校園"},
			Genres:        []string{"劇情", "愛情"},
			ContentAgents: []string{"製作單位A", "製作單位B"},
		},
		{
			Country:       "Japan",
			Themes:        []string{"喜劇", "懸疑"}, // duplicated themes
			Tags:          []string{"校園", "推理"}, // duplicated tags
			Genres:        []string{"愛情", "懸疑"},
			ContentAgents: []string{"製作單位B", "製作單位C"}, // duplicated agents
		},
		{
			Country:       "Test", // not defined
			Themes:        []string{"不使用"},
			Tags:          []string{"不使用"},
			Genres:        []string{"不使用"},
			ContentAgents: []string{"不使用"},
		},
	}

	testCases := []struct {
		name          string
		given         func()
		expected      *collectionResult
		expectedError error
	}{
		{
			name: "successfully WHEN cache hit",
			given: func() {
				suite.mockCacheWriter.EXPECT().
					Once(key.GetMetaDataOnListedTitles(), gomock.Any(), ttlListedTitlesViewsCache, gomock.Any()).
					DoAndReturn(func(key string, v interface{}, ttl time.Duration, f func() (interface{}, error)) error {
						titles := v.(*[]*modelsearch.Title)
						*titles = sampleTitles
						return nil
					})
			},
			expected: &collectionResult{
				Countries:     []string{"Korea", "韓國", "Japan", "日本"},
				Themes:        []string{"愛情", "喜劇", "懸疑"},
				Tags:          []string{"浪漫", "校園", "推理"},
				Genres:        []string{"劇情", "愛情", "懸疑"},
				ContentAgents: []string{"製作單位A", "製作單位B", "製作單位C"},
			},
			expectedError: nil,
		},
		{
			name: "successfully WHEN cache miss and get from repository",
			given: func() {
				suite.mockCacheWriter.EXPECT().
					Once(key.GetMetaDataOnListedTitles(), gomock.Any(), ttlListedTitlesViewsCache, gomock.Any()).
					DoAndReturn(func(key string, v interface{}, ttl time.Duration, f func() (interface{}, error)) error {
						result, err := f()
						if err != nil {
							return err
						}
						titles := v.(*[]*modelsearch.Title)
						*titles = result.([]*modelsearch.Title)
						return nil
					})

				suite.mockTitleRepo.EXPECT().
					ListAllOnListedTitles().
					Return(sampleTitles, nil)
			},
			expected: &collectionResult{
				Countries:     []string{"Korea", "韓國", "Japan", "日本"},
				Themes:        []string{"愛情", "喜劇", "懸疑"},
				Tags:          []string{"浪漫", "校園", "推理"},
				Genres:        []string{"劇情", "愛情", "懸疑"},
				ContentAgents: []string{"製作單位A", "製作單位B", "製作單位C"},
			},
			expectedError: nil,
		},
		{
			name: "got error WHEN cache operation failed",
			given: func() {
				suite.mockCacheWriter.EXPECT().
					Once(key.GetMetaDataOnListedTitles(), gomock.Any(), ttlListedTitlesViewsCache, gomock.Any()).
					Return(assert.AnError)
			},
			expected:      nil,
			expectedError: assert.AnError,
		},
		{
			name: "got empty result WHEN no titles in cache or repository",
			given: func() {
				suite.mockCacheWriter.EXPECT().
					Once(key.GetMetaDataOnListedTitles(), gomock.Any(), ttlListedTitlesViewsCache, gomock.Any()).
					DoAndReturn(func(key string, v interface{}, ttl time.Duration, f func() (interface{}, error)) error {
						titles := v.(*[]*modelsearch.Title)
						*titles = []*modelsearch.Title{}
						return nil
					})
			},
			expected:      &collectionResult{},
			expectedError: nil,
		},
	}

	for _, tc := range testCases {
		suite.Run(tc.name, func() {
			tc.given()

			result, err := suite.handler.getCollections()

			if tc.expectedError != nil {
				suite.Assert().ErrorIs(err, tc.expectedError)
			} else {
				suite.Assert().NoError(err)
				suite.Assert().ElementsMatch(tc.expected.Countries, result.Countries)
				suite.Assert().ElementsMatch(tc.expected.Themes, result.Themes)
				suite.Assert().ElementsMatch(tc.expected.Tags, result.Tags)
				suite.Assert().ElementsMatch(tc.expected.Genres, result.Genres)
				suite.Assert().ElementsMatch(tc.expected.ContentAgents, result.ContentAgents)
			}
		})
	}
}

func (suite *SitemapTestSuite) TestGetTitleListIDs() {
	sampleTitleLists := []*dbmeta.TitleList{
		{Meta: &dbmeta.TitleListMeta{ShareId: "share1"}},
		{Meta: &dbmeta.TitleListMeta{ShareId: "share2"}},
		{Meta: &dbmeta.TitleListMeta{ShareId: "share3"}},
	}
	now := time.Now()
	suite.mockClock.EXPECT().Now().Return(now).AnyTimes()

	testCases := []struct {
		name           string
		given          func()
		expectedResult []string
		expectedError  error
	}{
		{
			name: "successfully get share IDs from repository",
			given: func() {
				suite.mockTitlelistRepo.EXPECT().
					ListOnlyTitlesList(now).
					Return(sampleTitleLists, nil)
			},
			expectedResult: []string{"share1", "share2", "share3"},
			expectedError:  nil,
		},
		{
			name: "got empty result WHEN no title lists",
			given: func() {
				suite.mockTitlelistRepo.EXPECT().
					ListOnlyTitlesList(now).
					Return([]*dbmeta.TitleList{}, nil)
			},
			expectedResult: nil,
			expectedError:  nil,
		},
	}

	for _, tc := range testCases {
		suite.Run(tc.name, func() {
			tc.given()

			result, err := suite.handler.getTitleListIDs()

			if tc.expectedError != nil {
				suite.Assert().ErrorIs(err, tc.expectedError)
			} else {
				suite.Assert().NoError(err)
				suite.Assert().ElementsMatch(tc.expectedResult, result)
			}
		})
	}
}
