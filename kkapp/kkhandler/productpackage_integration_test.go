//go:build legacy

package kkhandler

import (
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
)

func TestListProductPackage_IntegrationTest(t *testing.T) {
	// test list android
	{
		r, err := http.NewRequest("GET", "/v3/product_packages?platform=android", nil)

		if err != nil {
			t.Fatal(err)
		}

		w := httptest.NewRecorder()
		handler := http.HandlerFunc(ListProductPackage)

		handler.ServeHTTP(w, r)

		if status := w.Code; status != http.StatusOK {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, http.StatusOK)
		}

		if !strings.Contains(w.Body.String(), "OK") || !strings.Contains(w.Body.String(), "kktv.android") {
			t.Errorf("not contains keywords")
		}

	}

	// test list web
	{
		r, err := http.NewRequest("GET", "/v3/product_packages?platform=web", nil)

		if err != nil {
			t.<PERSON>al(err)
		}

		w := httptest.NewRecorder()
		handler := http.HandlerFunc(ListProductPackage)

		handler.ServeHTTP(w, r)

		if status := w.Code; status != http.StatusOK {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, http.StatusOK)
		}

		if !strings.Contains(w.Body.String(), "OK") || !strings.Contains(w.Body.String(), "creditcard.duration1month") {
			t.Errorf("not contains keywords")
		}

	}
}
