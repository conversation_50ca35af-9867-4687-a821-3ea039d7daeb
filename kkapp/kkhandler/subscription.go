package kkhandler

import (
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"time"

	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/auditing"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/wrapper"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/amplitude"
	"github.com/KKTV/kktv-api-v3/kkapp/kkpayment"
	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/pkg/billing"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/KKTV/kktv-api-v3/pkg/key"
	plog "github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/model/cacheuser"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"

	"github.com/duke-git/lancet/v2/slice"
	"github.com/jmoiron/sqlx"
)

var AllowCancelationPaymentType = []string{
	"credit_card",
	"telecom",
}

const (
	ERR_UNAUTHORIZED         = "Unauthorized"
	ERR_PAYMENT_NOT_FOUND    = "User never paid"
	ERR_INVALID_PAYMENT_TYPE = "Disallowed payment type of subscription cancelation"
)

const (
	unsubscribeReasonTTL = time.Minute * 5
)

type cancelSubscription struct {
	Reason string `json:"reason"`
}

func Unsubscribe(w http.ResponseWriter, r *http.Request) {
	var err error
	var tx *sqlx.Tx

	response := model.MakeOk()

	var data cancelSubscription
	jsdecoder := json.NewDecoder(r.Body)
	err = jsdecoder.Decode(&data)
	if err != nil {
		return
	}

	defer func() {
		if err != nil {
			if tx != nil {
				tx.Rollback()
			}
			response.Status.Type = "FAIL"
			httpStatus := http.StatusInternalServerError
			response.Status.Subtype = "Internal Server Error"
			response.Status.Message = err.Error()
			if slice.Contain([]string{ERR_PAYMENT_NOT_FOUND, ERR_INVALID_PAYMENT_TYPE}, err.Error()) {
				httpStatus = http.StatusBadRequest
				response.Status.Subtype = "Bad Request"
			} else if err.Error() == ERR_UNAUTHORIZED {
				httpStatus = http.StatusUnauthorized
				response.Status.Subtype = ERR_UNAUTHORIZED
			}
			kkapp.App.Render.JSON(w, httpStatus, response)
			return
		}
		kkapp.App.Render.JSON(w, http.StatusOK, response)
	}()

	jwtUser, ok := r.Context().Value("user").(model.JwtUser)
	if !ok || jwtUser.IsGuest() {
		err = errors.New(ERR_UNAUTHORIZED)
		return
	}

	userID := jwtUser.Sub

	plog.Info("v3 unsubscribe: params").
		Str("user_id", userID).
		Str("reason", data.Reason).Send()

	db := kkapp.App.DbUser.Master()

	var paymentInfo dbuser.PaymentInfo
	// Get payment info by user ID
	if err = db.Get(&paymentInfo, `SELECT user_id, email, phone, payment_type,
			credit_card_6no, credit_card_4no, credit_card_token_value,
			credit_card_token_term, iap_receipt_data, iap_receipt_data_hash,
    	iap_latest_expires_date, iap_latest_transaction_id, caring_code,
			recipient, recipient_address, carrier_type, carrier_value,
			telecom_mp_id, tstar_order_id, tstar_contract_id, mod_subscriber_id,
			mod_subscriber_area, iab_receipt_data, iab_order_id,
			iab_latest_order_id, iab_latest_expires_date,
			created_at, updated_at, family_id
		FROM payment_info
		WHERE user_id = $1`, userID); err != nil {

		plog.Error("v3 unsubscribe: failed to get payment_info").Err(err).Send()

		if errors.Is(err, sql.ErrNoRows) {
			err = errors.New(ERR_PAYMENT_NOT_FOUND)
		}
		return
	}

	// Check if the payment type is allowed for subscription cancelation
	if !slice.Contain(AllowCancelationPaymentType, paymentInfo.PaymentType.String) {
		err = errors.New(ERR_INVALID_PAYMENT_TYPE)
		plog.Error("v3 unsubscribe: invalid payment type").
			Str("payment_type", paymentInfo.PaymentType.String).Err(err).Send()
		return
	}

	var user *dbuser.User
	user, err = getUser(db, userID)
	if err != nil {
		plog.Error("v3 unsubscribe: failed to get user").Err(err).Send()
		return
	}

	keepUnsubscriptionForSubscriptionChangedWebhook(userID, data)

	if tx, err = db.Beginx(); err != nil {
		plog.Error("v3 unsubscribe: failed to begin transaction").Err(err).Send()
		return
	}

	isLegacyKKTVProduct := true
	var canceledContractResp *billing.CustomerContractData
	// get billing contract if payment type is credit_card
	if paymentInfo.PaymentType.String == "credit_card" {
		// init billing api client
		billingClient := kkapp.App.BillingClient

		// get billing contract to check if user need to bind with billing customer or not
		resp, err := billingClient.GetCustomerContract(userID)
		if err != nil {
			if err.Error() != "customer not found" {
				plog.Error("v3 unsubscribe: failed to get billing contract").Err(err).Send()
				return
			}
		} else { // billing customer exists
			contractResp, err := resp.ToCustomerContractData()
			if err != nil {
				plog.Error("v3 unsubscribe: failed to parse billing contract").Err(err).Send()
				return
			}
			if contractResp.Contract.PaymentType() == "credit_card" && contractResp.Contract.IsInSubscription() {
				// cancel billing subscription
				resp, err = billingClient.CancelSubscription(userID)
				if err != nil {
					plog.Error("v3 unsubscribe: failed to cancel billing subscription").Err(err).Send()
					return
				}

				canceledContractResp, err = resp.ToCustomerContractData()
				if err != nil {
					plog.Error("v3 unsubscribe: failed to parse billing subscription_cancel response").Err(err).Send()
					return
				}
				isLegacyKKTVProduct = canceledContractResp == nil
			}
		}
	}

	// if no billing subscription canceled, check v3 subscription
	if isLegacyKKTVProduct {
		// Cancel valuable orders by user ID
		_, err = tx.Exec(`UPDATE orders SET status = 'cancel', canceled_at = NOW() WHERE user_id = $1 AND status IS NULL AND price > 0::money`, userID)
		if err != nil {
			plog.Error("v3 unsubscribe: failed to cancel unfulfilled orders").Err(err).Send()
			return
		}
	}

	var paymentInfoSnapshot []byte
	paymentInfoSnapshot, err = json.Marshal(paymentInfo)
	if err != nil {
		plog.Error("v3 unsubscribe: failed to marshal payment_info").Err(err).Send()
		return
	}

	// Insert into unsubscribe table
	_, err = tx.Exec(`INSERT INTO unsubscribe (user_id, reason, payment_type, payment_info_snapshot) VALUES ($1, $2, $3, $4)`,
		userID, data.Reason, paymentInfo.PaymentType, string(paymentInfoSnapshot))
	if err != nil {
		plog.Error("v3 unsubscribe: failed to insert unsubscribe").Err(err).Send()
		return
	}

	// Update user table by user ID
	_, err = tx.Exec(`UPDATE users SET updated_at = NOW(), auto_renew = $1 WHERE id = $2`, false, userID)
	if err != nil {
		plog.Error("v3 unsubscribe: failed to update user's auto_renew").Err(err).Send()
		return
	}

	// Commit the transaction
	err = tx.Commit()
	if err != nil {
		plog.Error("v3 unsubscribe: failed to commit transaction").Err(err).Send()
		return
	}

	// telecom
	if paymentInfo.PaymentType.String == "telecom" {
		go func() {
			if err = kkpayment.TelecomUnsubscribe(userID, data.Reason); err != nil {
				plog.Error("v3 unsubscribe: failed to send sonet unsubscribe request").Err(err).Send()
			}
		}()
	}

	if isLegacyKKTVProduct { // Audit logs will be created during the Billing SubscriptionChangedWebhook, so we don't need to create it here
		auditLogBuilder := auditing.NewLogBuilder().ByUser().ModifierID(userID).
			TargetUpdated("user", userID).
			DetailDiff(dbuser.AuditLogDifference{Field: "auto_renew", Old: user.AutoRenew, New: false}).
			Note(fmt.Sprintf("user unsubscribed, reason: %s", data.Reason))
		// TODO with order's audit log
		auditLogRepo := auditing.NewRepository(db)
		logs := auditLogBuilder.Build()
		if err := auditLogRepo.Insert(logs...); err != nil {
			plog.Warn("v3 unsubscribe: failed to insert audit log").Err(err).Interface("audit_logs", logs).Send()
		}
		//if is Legacy that will not send new properties to amplitude
		event, _ := amplitude.NewAccountTransactionCancelled(userID, paymentInfo.PaymentType.String, data.Reason)
		go event.Send()

	}

	expiredAt := user.ExpiredAt.Time.Unix()
	if canceledContractResp != nil {
		productName := canceledContractResp.Contract.CurrentTransaction.Product.Name
		productId := canceledContractResp.Contract.CurrentTransaction.Product.Identifier
		productTitle := ""
		productPackage := new(dbuser.ProductPackage)
		hasSubscribedProduct := canceledContractResp.Contract.SubscribedProduct.Recurring && canceledContractResp.Contract.SubscriptionStatus.Is == "ongoing"
		if hasSubscribedProduct {
			productId = canceledContractResp.Contract.SubscribedProduct.Identifier
			productName = canceledContractResp.Contract.SubscribedProduct.Name
		}
		err := db.Get(&productPackage, `SELECT * FROM product_packages WHERE billing_product_ids ? $1`, productId)
		if err == nil {
			productTitle = productPackage.PackageName()
		}
		event, _ := amplitude.NewCreditCardAccountTransactionCancelled(paymentInfo.PaymentType.String, data.Reason, productId, productTitle, productName, user)
		go event.Send()

		if ematicSrv := wrapper.NewEmaticService(); ematicSrv.IsClientExist() {
			go ematicSrv.Cancel(user.Email.String, user.ExpiredAt.Time)
		}

		expiredAt = canceledContractResp.Contract.LastExpiredAt
	}

	response.Data = map[string]interface{}{
		"expired_at": expiredAt - 86400,
	}
}

func getUser(db *sqlx.DB, userID string) (*dbuser.User, error) {
	var user = new(dbuser.User)
	if err := db.Get(user,
		`SELECT
			u.id, u.email, u.phone, u.gender, u.birthday,
			u.expired_at,u.membership,
 			u.created_at, u.role, u.auto_renew, u.type, u.created_by, u.kkid
		FROM users u
		WHERE u.id = $1`, userID); err != nil {
		return nil, err
	}
	return user, nil
}

func keepUnsubscriptionForSubscriptionChangedWebhook(userID string, data cancelSubscription) {
	cacheWriter := cache.New(kkapp.App.RedisUser.Master())
	if err := cacheWriter.Set(key.UserUnsubscribe(userID), &cacheuser.Unsubscription{
		Reason:    data.Reason,
		CreatedAt: time.Now().Unix(),
	}, unsubscribeReasonTTL); err != nil {
		plog.Warn("v3 unsubscribe: failed to write unsubscribe reason into redis").
			Str("user_id", userID).Interface("data", data).Err(err).Send()
	}
}
