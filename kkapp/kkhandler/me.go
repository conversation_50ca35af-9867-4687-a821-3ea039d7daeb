package kkhandler

import (
	"errors"
	"log"
	"net/http"
	"net/url"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/kktverror"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/permission"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/wrapper"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/wrapper/middleware"
	zlog "github.com/KKTV/kktv-api-v3/pkg/log"
	modelmw "github.com/KKTV/kktv-api-v3/pkg/model/middleware"
)

// GetMe get users/me info
func GetMe(w http.ResponseWriter, r *http.Request) {
	response := model.MakeOk()
	user := r.Context().Value("user").(model.JwtUser)

	defer func() {
		kkapp.App.Render.JSON(w, http.StatusOK, response)
	}()

	if user.IsGuest() {
		response.Data = model.GuestInfo
		return
	}

	db := kkapp.App.DbUser.Slave()
	me, err := model.NewUserInfo(user.Sub, db)

	// spew.Dump(me)
	if err != nil {
		log.Println("[ERROR]", err)
		response.Data = nil
		return
	}

	if user.Iat != 0 {
		me.LastSignedIn = user.Iat
	}

	response.Data = me
}

// DeleteMe delete users/me
func DeleteMe(w http.ResponseWriter, r *http.Request) {
	var err error

	response := model.MakeOk()
	accessUser := r.Context().Value(middleware.KeyAccessUser).(modelmw.AccessUser)
	reason := r.URL.Query().Get("reason")

	userID := accessUser.UserID
	reason, _ = url.QueryUnescape(reason)

	zlog.Info("v3DeleteUser: DeleteMe").
		Str("user_id", userID).
		Str("reason", reason).Send()

	defer func() {
		if err != nil {
			zlog.Error("v3DeleteUser: DeleteMe").Err(err).Str("userID", userID).Send()
			response.Status.Type = "Fail"
			response.Status.Message = err.Error()
			statusCode := http.StatusBadRequest

			switch err.Error() {
			case "Unauthorized":
				statusCode = http.StatusUnauthorized
				response.Status.Subtype = "401.1"
			case "VIP user not allowed":
				statusCode = http.StatusForbidden
				response.Status.Subtype = "403.1"
			case "User already deleted":
				response.Status.Subtype = "400.1"
			case "Reason can not be empty":
				response.Status.Subtype = "400.3"
			case "Internal server error":
				response.Status.Subtype = "500.1"
				statusCode = http.StatusInternalServerError
			}

			kkapp.App.Render.JSON(w, statusCode, response)
		} else {
			kkapp.App.Render.JSON(w, http.StatusOK, response)
		}
	}()

	if !accessUser.IsMember() || userID == "" {
		err = errors.New("Unauthorized")
		return
	}

	if reason == "" {
		err = errors.New("Reason can not be empty")
		return
	}

	db := kkapp.App.DbUser.Master()

	me, err := model.NewUserInfo(userID, db)
	if err != nil {
		return
	}

	userService := wrapper.NewUserService(db.Unsafe())
	if user, getUserErr := userService.GetByID(userID); getUserErr != nil {
		err = errors.New("Unauthorized")
		return
	} else if user != nil {
		req := permission.RequestDeleteUser(user)
		if grantErr := kkapp.App.PermissionService.Grant(req); kktverror.IsInternalErr(grantErr) {
			zlog.Error("unknown error").Err(grantErr).Interface("req", req).Send()
			err = errors.New("Internal server error")
			return
		} else if errors.Is(grantErr, kktverror.ErrResourceNotFound) {
			err = errors.New("User already deleted")
			return
		} else if errors.Is(grantErr, kktverror.ErrResourceAccessDenied) {
			err = errors.New("VIP user not allowed")
			return
		}
	}

	if err = me.Delete(reason); err != nil {
		return
	}
}
