package kkhandler

import (
	"encoding/json"
	"net/http"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/datastore/helper"
	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kkapp/model/kkcontent"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/feature"
	plog "github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/render"
)

var (
	browseCountryKey = "browse:v2:country:json"
	browseGenreKey   = "browse:v2:genre:json"
	hotSearchKey     = "meta:title-hot-search:v1:json"
	chartKey         = "meta:title-chart:v1:json"
	expireSoonKey    = "meta:title-expire-soon:v1:json"
	browseCacheKey   = "browse:v3" // cache at redis key "cacher:browse:v3:json"
)

type collection struct {
	Title          string `json:"title"`
	Subtitle       string `json:"subtitle,omitempty"`
	CollectionID   string `json:"collection_id"`
	CollectionName string `json:"collection_name"`
	CollectionType string `json:"collection_type"`
	ID             string `json:"id"`
	Image          string `json:"image"`
}

func cookBrowse() (data map[string]interface{}, err error) {
	pool := kkapp.App.RedisMeta.Slave()
	data = make(map[string]interface{})
	var dataBytes []byte
	var collectionCountry, collectionGenre []collection
	keys := []interface{}{hotSearchKey, chartKey, expireSoonKey}
	reply := pool.Cmd("MGET", keys...)

	if reply.Err != nil {
		plog.Error("v3 browse: failed to redis MGET").Err(reply.Err).Interface("cmd", keys).Send()
		return data, reply.Err
	}

	arrReply, err := reply.Array()
	if err != nil {
		plog.Error("v3 browse: failed to reply to Array response").Err(err).Interface("reply", reply).Send()
		return data, err
	}

	// help func for Titles
	cookTitles := func(key string, data map[string]interface{}, dataBytes []byte) {
		var err error
		var titleIDs []string
		var titleDetails model.CollectionTitles

		if err := json.Unmarshal(dataBytes, &titleIDs); err != nil {
			plog.Error("v3 browse: failed to json unmarshal").Err(err).Bytes("dataBytes", dataBytes).Send()
			return
		}
		titleDetails, err = model.NewTitleDetails(titleIDs)
		if err != nil {
			plog.Error("v3 browse: failed to model.NewTitleDetails").Err(err).Strs("titleIDs", titleIDs).Send()
			return
		}
		data[key] = titleDetails.FilterNotExpired()
	}

	dataBytes, _ = arrReply[0].Bytes()
	cookTitles("hot_searches", data, dataBytes)

	dataBytes, _ = arrReply[1].Bytes()
	cookTitles("charts", data, dataBytes)

	dataBytes, _ = arrReply[2].Bytes()
	cookTitles("expire_soon", data, dataBytes)

	// merge two collection into one
	if len(collectionGenre) > 0 {
		collectionCountry = append(collectionCountry, collectionGenre...)
	}

	return data, nil
}

// GetBrowse handler for api v3
func GetBrowse(w http.ResponseWriter, r *http.Request) {
	var (
		err       error
		dataBytes []byte
	)
	data := make(map[string]interface{})

	cacher, _ := helper.NewCacher(browseCacheKey)
	dataBytes, err = cacher.Get()
	if err != nil {
		// no cache
		data, err = cookBrowse()
		if err == nil {
			dataBytes, _ = json.Marshal(data)
			_ = cacher.SetExpire(dataBytes, 300) // 5 minute cache
		}
	} else {
		_ = json.Unmarshal(dataBytes, &data)
	}
	// browses pass through feature toggle should not to be cached
	data["collections"] = getBrowses(r)
	response := model.MakeOk()
	response.Data = data
	render.JSONOk(w, response)
}

func getBrowses(r *http.Request) []*kkcontent.RedisCollection {
	browse, _ := kkcontent.NewBrowse()
	col := browse.Platform("tv")
	dataCollections := col.GetCollections()
	var filtered []*kkcontent.RedisCollection
	canBrowse := canBrowseFunc(r)
	for _, c := range dataCollections {
		if canBrowse(c) {
			filtered = append(filtered, c)
		}
	}
	return filtered
}

func canBrowseFunc(r *http.Request) func(*kkcontent.RedisCollection) bool {
	featureSrv := feature.NewService()
	supportBrowseEntryProtect, err := featureSrv.HasFlag(feature.FlagSupportBrowseEntryProtect, r)
	if err != nil {
		plog.Warn("v3 browse: fail to get feature flag").Str("flag", feature.FlagSupportBrowseEntryProtect.String()).
			Err(err).Send()
	}
	return func(rc *kkcontent.RedisCollection) bool {
		if rc.EntryType == "protected" && !supportBrowseEntryProtect {
			return false
		}
		return true
	}
}
