package kkhandler

import (
	"log"
	"net/http"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/go-zoo/bone"
)

// GetTitleRedis handler for api benchmark
func GetTitleRedis(w http.ResponseWriter, r *http.Request) {
	var err error
	var title *model.UserSingleTitleDetail
	var titleid string
	titleid = bone.GetValue(r, "titleid")
	title, err = model.NewUserSingleTitleDetail("", titleid)

	if err != nil {
		log.Println("[ERROR] get title detail", err)
	}
	if title != nil && title.Available {
		// validated
		response := model.MakeOk()
		// clear the subtitle information
		// ths subtitle URL should not expose outside
		for idx := range title.Series {
			for innerIdx := range title.Series[idx].Episodes {
				title.Series[idx].Episodes[innerIdx].SubtitleMap = nil
			}
		}
		response.Data = title
		kkapp.App.Render.JSON(w, http.StatusOK, response)
	} else {
		response := model.MakeOk()
		response.Status.Type = "NotFound"
		response.Status.Subtype = "TitleNotFound"
		kkapp.App.Render.JSON(w, http.StatusNotFound, response)
	}

}

// GetTitleDB handler for api benchmark
func GetTitleDB(w http.ResponseWriter, r *http.Request) {
	var err error
	var title *model.UserSingleTitleDetail
	var titleid string
	titleid = bone.GetValue(r, "titleid")
	title, err = model.NewUserSingleTitleDetailDB("", titleid)

	if err != nil {
		log.Println("[ERROR] get title detail", err)
	}
	if title != nil && title.Available {
		// validated
		response := model.MakeOk()
		// clear the subtitle information
		// ths subtitle URL should not expose outside
		for idx := range title.Series {
			for innerIdx := range title.Series[idx].Episodes {
				title.Series[idx].Episodes[innerIdx].SubtitleMap = nil
			}
		}
		response.Data = title
		kkapp.App.Render.JSON(w, http.StatusOK, response)
	} else {
		response := model.MakeOk()
		response.Status.Type = "NotFound"
		response.Status.Subtype = "TitleNotFound"
		kkapp.App.Render.JSON(w, http.StatusNotFound, response)
	}

}
