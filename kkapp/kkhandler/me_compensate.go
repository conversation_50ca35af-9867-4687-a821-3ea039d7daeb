package kkhandler

import (
	"encoding/json"
	"errors"
	"log"
	"net/http"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/davecgh/go-spew/spew"
)

var (
	sqlCompensate = map[string]string{
		"upsert": `INSERT INTO compensate_users (
						user_id,
						user_name,
						gender,
						phone,
						email
					) VALUES (
						:user_id,
						:user_name,
						:gender,
						:phone,
						:email
					) ON CONFLICT (user_id) DO UPDATE SET
						user_name = :user_name,
						gender = :gender,
						phone = :phone,
						email = :email
				    WHERE compensate_users.user_id = :user_id;
		`,
	}
)

// Compensate struct for compensate users
type Compensate struct {
	UserID   string `db:"user_id"`
	UserName string `db:"user_name"`
	Gender   string `db:"gender"`
	Phone    string `db:"phone"`
	Email    string `db:"email"`
}

// PostPayload struct for payload
type PostPayload struct {
	UserName string `json:"username"`
	Gender   string `json:"gender"`
	Phone    string `json:"phone"`
	Email    string `json:"email"`
}

// PostMeCompensate add user to compensate table
func PostMeCompensate(w http.ResponseWriter, r *http.Request) {
	var err error
	var p PostPayload

	response := model.MakeOk()

	defer func() {
		if err != nil {
			log.Println("[ERROR]", err)
			response.Status.Type = "FAIL"
			response.Status.Message = err.Error()
			kkapp.App.Render.JSON(w, http.StatusBadRequest, response)
		} else {
			kkapp.App.Render.JSON(w, http.StatusOK, response)
		}
	}()

	user := r.Context().Value("user").(model.JwtUser)

	if user.IsGuest() {
		err = errors.New("guest user not allow")
		return
	}

	jsdecoder := json.NewDecoder(r.Body)
	if err = jsdecoder.Decode(&p); err != nil {
		return
	}

	db := kkapp.App.DbUser.Master()
	spew.Dump(p)

	spew.Dump(user)

	c := new(Compensate)
	c.UserID = user.Sub
	c.UserName = p.UserName
	c.Gender = p.Gender
	c.Phone = p.Phone
	c.Email = p.Email

	_, err = db.NamedExec(sqlCompensate["upsert"], c)
	if err != nil {
		log.Printf("\n[ERROR] - Save Compensate user failed. Error:%s\n", err.Error())
	}
}
