INSERT INTO public.users(id, email, "role", "type", expired_at, media_source, kkid) VALUES
('test-case-1-user-a', 'test-case-1-user-a', 'expired', 'general', '2022-01-01', '{"kkbox":{"sub":"test-case-1-user-a","status":"","territory":"TW","identifier":"test-case-1-user-a","kkid":"test-case-1-user-a"}}', 'test-case-1-user-a'),
('test-case-1-user-b', 'test-case-1-user-b', 'premium', 'general', '2030-01-01', '{"kkbox":{"sub":"test-case-1-user-b","status":"","territory":"TW","identifier":"test-case-1-user-b","kkid":"test-case-1-user-b"}}', 'test-case-1-user-b'),
('test-case-2-user-a', 'test-case-2-user-a', 'premium', 'general', '2030-01-01', '{"kkbox":{"sub":"test-case-2-user-a","status":"","territory":"TW","identifier":"test-case-2-user-a","kkid":"test-case-2-user-a"}}', 'test-case-2-user-a'),
('test-case-2-user-b', 'test-case-2-user-b', 'expired', 'general', '2022-01-01', '{"kkbox":{"sub":"test-case-2-user-b","status":"","territory":"TW","identifier":"test-case-2-user-b","kkid":"test-case-2-user-b"}}', 'test-case-2-user-b'),
('test-case-3-user-a', 'test-case-3-user-a', 'premium', 'general', '2030-01-01', '{"kkbox":{"sub":"test-case-3-user-a","status":"","territory":"TW","identifier":"test-case-3-user-a","kkid":"test-case-3-user-a"}}', 'test-case-3-user-a'),
('test-case-3-user-b', 'test-case-3-user-b', 'premium', 'general', '2030-01-01', '{"kkbox":{"sub":"test-case-3-user-b","status":"","territory":"TW","identifier":"test-case-3-user-b","kkid":"test-case-3-user-b"}}', 'test-case-3-user-b'),
('test-case-4-user-a', 'test-case-4-user-a', 'expired', 'general', '2022-01-01', '{"kkbox":{"sub":"test-case-4-user-a","status":"","territory":"TW","identifier":"test-case-4-user-a","kkid":"test-case-4-user-a"}}', 'test-case-4-user-a'),
('test-case-4-user-b', 'test-case-4-user-b', 'expired', 'general', '2022-01-01', '{"kkbox":{"sub":"test-case-4-user-b","status":"","territory":"TW","identifier":"test-case-4-user-b","kkid":"test-case-4-user-b"}}', 'test-case-4-user-b'),
('test-case-5-user-a', 'test-case-5-user-a', 'premium', 'general', '2030-01-01', '{"kkbox":{"sub":"test-case-5-user-a","status":"","territory":"TW","identifier":"test-case-5-user-a","kkid":"test-case-5-user-a"}}', 'test-case-5-user-a'),
('test-case-5-user-b', 'test-case-5-user-b', 'expired', 'general', '2022-01-01', '{"kkbox":{"sub":"test-case-5-user-b","status":"","territory":"TW","identifier":"test-case-5-user-b","kkid":"test-case-5-user-b"}}', 'test-case-5-user-b'),
('test-case-5-user-c', 'test-case-5-user-c', 'expired', 'general', '2022-01-01', '{"kkbox":{"sub":"test-case-5-user-c","status":"","territory":"TW","identifier":"test-case-5-user-c","kkid":"test-case-5-user-c"}}', 'test-case-5-user-c'),
('test-case-6-user-a', 'test-case-6-user-a', 'premium', 'general', '2030-01-01', '{"kkbox":{"sub":"test-case-6-user-a","status":"","territory":"TW","identifier":"test-case-6-user-a","kkid":"test-case-6-user-a"}}', 'test-case-6-user-a'),
('test-case-6-user-b', 'test-case-6-user-b', 'expired', 'general', '2022-01-01', '{"kkbox":{"sub":"test-case-6-user-b","status":"","territory":"TW","identifier":"test-case-6-user-b","kkid":"test-case-6-user-b"}}', 'test-case-6-user-b'),
('test-case-6-user-c', 'test-case-6-user-c', 'expired', 'general', '2022-01-01', '{"kkbox":{"sub":"test-case-6-user-c","status":"","territory":"TW","identifier":"test-case-6-user-c","kkid":"test-case-6-user-c"}}', 'test-case-6-user-c');
INSERT INTO public.payment_info(user_id, email, payment_type, family_id) VALUES
('test-case-1-user-a', 'test-case-1-user-a', 'credit_card', null),
('test-case-1-user-b', 'test-case-1-user-b', 'credit_card', null),
('test-case-2-user-a', 'test-case-2-user-a', 'credit_card', null),
('test-case-2-user-b', 'test-case-2-user-b', 'credit_card', null),
('test-case-3-user-a', 'test-case-3-user-a', 'credit_card', null),
('test-case-3-user-b', 'test-case-3-user-b', 'credit_card', null),
('test-case-4-user-a', 'test-case-4-user-a', 'credit_card', null),
('test-case-4-user-b', 'test-case-4-user-b', 'credit_card', null),
('test-case-5-user-a', 'test-case-5-user-a', 'credit_card', null),
('test-case-5-user-b', 'test-case-5-user-b', 'credit_card', null),
('test-case-5-user-c', 'test-case-5-user-c', 'credit_card', 'test-case-5-user-a'),
('test-case-6-user-a', 'test-case-6-user-a', 'credit_card', 'test-case-6-user-c'),
('test-case-6-user-b', 'test-case-6-user-b', 'credit_card', null),
('test-case-6-user-c', 'test-case-6-user-c', 'credit_card', null);
INSERT INTO public.products(id, "name", price, duration, payment_type, item_name, item_unit) VALUES
(999, 'test', 100, '1 mon', 'credit_card', 'test', '月');
INSERT INTO public.orders(id, user_id, product_id, payment_type, "start_date", "end_date", order_date) VALUES
('test-case-1', 'test-case-1-user-a', 999, 'credit_card', '2022-01-01 00:00:00', '2022-02-01 00:00:00', '2022-01-01 00:00:00'),
('test-case-2', 'test-case-2-user-b', 999, 'credit_card', '2022-01-01 00:00:00', '2022-02-01 00:00:00', '2022-01-01 00:00:00'),
('test-case-3', 'test-case-3-user-b', 999, 'credit_card', '2022-01-01 00:00:00', '2022-02-01 00:00:00', '2022-01-01 00:00:00'),
('test-case-4', 'test-case-4-user-b', 999, 'credit_card', '2022-01-01 00:00:00', '2022-02-01 00:00:00', '2022-01-01 00:00:00'),
('test-case-5', 'test-case-5-user-a', 999, 'credit_card', '2022-01-01 00:00:00', '2022-02-01 00:00:00', '2022-01-01 00:00:00'),
('test-case-6', 'test-case-6-user-a', 999, 'credit_card', '2022-01-01 00:00:00', '2022-02-01 00:00:00', '2022-01-01 00:00:00');
