package kkauth

import (
	"database/sql"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"log"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/amplitude"
	"github.com/KKTV/kktv-api-v3/kkapp/coldstart"
	billingutils "github.com/KKTV/kktv-api-v3/kkapp/kkpayment/billing"
	"github.com/KKTV/kktv-api-v3/kkapp/kkutil"
	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kkapp/model/kkuser"
	"github.com/KKTV/kktv-api-v3/pkg/billing"
	zlog "github.com/KKTV/kktv-api-v3/pkg/log"
	decider "github.com/KKTV/kktv-api-v3/pkg/payment"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/lib/pq"
	"gopkg.in/guregu/null.v3"
)

const (
	// same with kkapp/kkid/kkid.go
	kkidHost       = "https://kkid.kkbox.com"
	kkidClientID   = "********************************"
	kkidPrivateKey = `**********************************************************************************************************************************************************************************************************************************`
)

// KKIDProfile for kkid https://kkid.kkbox.com/auth/v2/userinfo
type KKIDProfile struct {
	Iss        string `json:"iss"`
	Sub        string `json:"sub"`
	Aud        string `json:"aud"`
	Exp        int64  `json:"exp"`
	Iat        int64  `json:"iat"`
	FamilyName string `json:"family_name"`
	GivenName  string `json:"given_name"`
	Name       string `json:"name"`
	Nickname   string `json:"nickname"`
	AvatarURI  struct {
		Original  string `json:"original"`
		Tn200x200 string `json:"tn200x200"`
		Tn400x400 string `json:"tn400x400"`
		Tn600x600 string `json:"tn600x600"`
	} `json:"avatar_uri"`
	Email                 string `json:"email"`
	EmailVerified         bool   `json:"email_verified"`
	Phone                 string `json:"phone"`
	PhoneVerified         bool   `json:"phone_verified"`
	ConEmail              string `json:"con_email"`
	ConPhoneTerritoryCode string `json:"con_phone_territory_code"`
	ConPhoneCountryCode   string `json:"con_phone_country_code"`
	ConPhone              string `json:"con_phone"`
	Birthday              string `json:"birthday"`
	Gender                string `json:"gender"`
	UpdatedAt             int64  `json:"updated_at"`
	Territory             struct {
		Declared string `json:"declared"`
		Register string `json:"register"`
	} `json:"territory"`
}

// KKIDToken for kkid token https://kkid.kkbox.com/auth/v1/tokeninfo
type KKIDToken struct {
	Iss           string `json:"iss"`
	Sub           string `json:"sub"`
	ClientID      string `json:"client_id"`
	Scope         string `json:"scope"`
	Exp           int64  `json:"exp"`
	Iat           int64  `json:"iat"`
	AccountStatus string `json:"account_status"`
	PrimeStatus   string `json:"prime_status"`
	ExpiringSoon  bool   `json:"expiring_soon"`
	IsPrime       bool   `json:"is_prime"`
	Territory     string `json:"territory"`
	Username      string `json:"username"`
}

// KKIDAccessToken
type KKIDAccessToken struct {
	Aud string `json:"aud"`
	Exp int64  `json:"exp"`
	Jti string `json:"jti"`
	Iat int64  `json:"iat"`
	Iss string `json:"iss"`
	Sub string `json:"sub"`
}

type kkidprofile struct {
	Profile KKIDProfile
	Token   KKIDToken
}

// GetKKID handler kkid callback
func GetKKID(w http.ResponseWriter, r *http.Request) {
	var err error

	response := model.MakeOk()
	param := model.GetParams(r)

	if param.Code == "" {
		resp := model.ErrorNotFound("code")
		kkapp.App.Render.JSON(w, resp.Code, resp)
		return
	}

	log.Println("[INFO] code", param.Code)

	token := getKKIDAccessToken(param.Code)
	log.Println("[INFO] token", token)

	if token.AccessToken == "" {
		log.Println("[ERROR]", err)
		resp := model.ErrorAuthorized("code")
		kkapp.App.Render.JSON(w, resp.Code, resp)
		return
	}
	// get user profile
	profile := getKKIDProfile(url.QueryEscape(token.AccessToken))
	if profile.Profile.Sub == "" || profile.Profile.Name == "" {
		log.Println("[ERROR]", err)
		resp := model.ErrorBadRequest("Invalid Token or Account")
		kkapp.App.Render.JSON(w, resp.Code, resp)
		return
	}
	param.KKID = profile.Profile.Sub
	//// kkboxInfo
	//param.Status = profile.AccountStatus
	param.Username = profile.Profile.Name

	if profile.Profile.Territory.Register != "" {
		param.Territory = profile.Profile.Territory.Register
	}

	//param.IsPrime = profile.IsPrime
	if strings.Contains(profile.Profile.Name, "@") {
		// email
		param.Email = null.String{NullString: sql.NullString{String: profile.Profile.Name, Valid: true}}
	} else {
		// phone
		if strings.HasPrefix(profile.Profile.Name, "886") {
			param.Phone = null.String{NullString: sql.NullString{String: fmt.Sprintf("+886%s", strings.TrimPrefix(profile.Profile.Name, "886")), Valid: true}}
		} else {
			param.Phone = null.String{NullString: sql.NullString{String: profile.Profile.Name, Valid: true}}
		}
	}
	if profile.Profile.PhoneVerified {
		param.KKIDVerifiedPhone = null.String{NullString: sql.NullString{String: profile.Profile.Phone, Valid: true}}
	}

	// lookup kkbox sub
	kkidResp, err := kkapp.App.KKIDOauth.IntraSubKkid(profile.Profile.Sub)
	if err != nil || kkidResp == nil {
		log.Println("[ERROR] kkid lookup fail", err)
		resp := model.ErrorBadRequest("Invalid Token or Account")
		kkapp.App.Render.JSON(w, resp.Code, resp)
		return
	}

	if kkidResp != nil && kkidResp.MSNO != "" {
		param.Sub = kkidResp.MSNO
	}

	// SrcToken expiredAt RefreshToken would store at db table refresh_token.provide
	param.SrcToken = token.AccessToken
	param.ExpiredAt = time.Now().Truncate(time.Second).Add(time.Second * time.Duration(token.ExpiresIn)).Unix()
	param.RefreshToken = token.RefreshToken
	param.AppID = profile.Profile.Aud
	userID, err := model.NewUserAuthForKKID(param)

	if err != nil {
		log.Println("[ERROR]", err)
		kkapp.App.Render.JSON(w, http.StatusOK, model.ErrorServer(err.Error()))
		return
	}

	db := kkapp.App.DbUser.Master()
	var me model.UserInfo
	if param.Subtype == "SIGNIN" {
		// existed user
		slavedb := kkapp.App.DbUser.Slave()
		me, err = model.NewUserInfo(userID, slavedb)
		// kkbox id check if needed
		if err == nil {
			// amplitude sent in KKBOXIDCheck if changed
			me, err = me.KKBOXIDCheck()
		}

		event, _ := amplitude.NewAccountLogin(me.Id, "kkid", r)
		if me.KkboxSub.Valid {
			event.KKSub = me.KkboxSub.String
		}

		if me.KkidIdentifier != "" {
			event.KKIdentifier = me.KkidIdentifier
		}
		go event.Send()

		// signin edm event
		// if me.Email.Valid {
		// 	go kkapp.App.Ematic.Signin(me.Email.String)
		// }

	} else {
		// new user
		me, err = model.NewUserInfo(userID, db)
		me.IsNew = true

		// kkbox id check if needed
		if err == nil {
			// amplitude sent in KKBOXIDCheck if changed
			me, err = me.KKBOXIDCheck()
		}

		// sign up event
		event, _ := amplitude.NewAccountSignup(me.Id, me.Role, "", "", "", "", r)
		if me.KkboxSub.Valid {
			event.KKSub = me.KkboxSub.String
		}

		if me.KkidIdentifier != "" {
			event.KKIdentifier = me.KkidIdentifier
		}

		go event.Send()

		// signup edm event
		// if me.Email.Valid {
		// 	go kkapp.App.Ematic.Signup(me.Email.String, time.Unix(me.ExpiredAt, 0).Format(ematicagent.EmaticDateFormat))
		// }
	}

	if err != nil {
		log.Println("[ERROR]", err)
		response.Data = nil
		kkapp.App.Render.JSON(w, http.StatusOK, response)
		return
	}

	// 當使用者 kkid 帳號為手機號碼時向 billing service 進行綁定 (目前用途供亞太用戶綁定 billing service)
	// 1. profile.name 是 電話 或者
	// 2. profile.name 是 email 但是有做電話驗證，可以用電話登入
	if !strings.Contains(profile.Profile.Name, "@") || profile.Profile.PhoneVerified {
		phone := ""
		if !strings.Contains(profile.Profile.Name, "@") {
			phone = profile.Profile.Name
		} else {
			phone = profile.Profile.Phone
		}

		detail, result := decider.Decide(map[string]string{
			"action":            "bind",
			"bind_with_columns": "phone",
			"identifier":        userID,
			"phone":             phone,
		})
		zlog.Info("payment decider: auth kkid ask for binding billing customer").
			Str("identifier", userID).
			Str("phone", phone).
			Interface("detail", detail).
			Int32("result", result).
			Send()

		if result == decider.REQUEST_APPROVED {

			log.Println("[INFO]", fmt.Sprintf("Binding user with billing service customer, userID: %s, kkid: %s, phone: %s", userID, param.KKID, phone))
			billingClient := kkapp.App.BillingClient

			// 向 billing 進行綁定
			_, err = billingClient.UpdateCustomer(map[string]string{
				"phone": phone,
			}, userID)
			if err != nil {
				// 對 billing service 綁定流程發生失敗時不 return，避免中斷登入流程
				log.Println("[ERROR] Binding user with billing service customer failed", err)

			} else { // 綁定成功
				// 綁定後向 billing 取得當下用戶 contract 狀態
				resp, err := billingClient.GetCustomerContract(userID)
				if err != nil {
					log.Println("[ERROR] Get billing contract failed: ", err)
				} else {
					data, err := resp.ToCustomerContractData()
					if err != nil {
						log.Println("[ERROR] Parse billing contract failed: ", err)
					}
					// 如果向 billing 取得的用戶狀態是 vip 且效期大於目前用戶的效期，則更新用戶的 payment_info 狀態
					respPaymentType := data.Contract.CurrentTransaction.Product.PaymentType
					if data.Contract.IsActive() && data.Contract.LastExpiredAt > me.ExpiredAt &&
						respPaymentType != "" {

						// 更新用戶付款資訊前，驗證使用者在 KKTV 不是 VIP 會員
						if slice.Contain(billing.TelecomPaymentTypes, respPaymentType) &&
							(me.Role == "expired" ||
								me.Role == "freetrial" ||
								me.Type == "kkbox" ||
								me.PaymentInfo["type"] == respPaymentType) {
							err = billingutils.UpsertUserPaymentByBillingContract(data.Contract, me.Id)
							if err != nil {
								log.Println("[ERROR] Upsert bapi user payment failed: ", err)
							}
						} else {
							log.Println("[ERROR] 無法將 billing 付款資訊更新至用戶，該用戶已經是 VIP, User ID: ", me.Id)
						}
					}
				}
			}
		} else {
			zlog.Error("payment decider: auth kkid not approved to bind billing customer").
				Interface("detail", detail).
				Int32("result", result).
				Send()
		}
	}

	// auth info
	var authinfo *model.AuthInfo

	if param.Subtype == "SIGNIN" {
		// signin, not first time
		// if from same SrcToken, and not expired don't need to generate it again
		tokenHash := model.GenTokenHash(param.SrcToken, param.SourceIp, param.UserAgent)
		log.Println("[INFO] token_hash", tokenHash)
		// maybe expired, err will be sql: no rows in result set
		authinfo, _ = model.NewAuthInfoFromTokenHash(userID, tokenHash)
	}

	if authinfo == nil || authinfo.Token == "" {
		// signup, or expired let's generate the authinfo
		param.Role = me.Role
		param.Type = me.Type
		authinfo, err = model.NewAuthInfo(me.Id, param)
	}

	if err != nil {
		log.Println("[ERROR]", err)
		response.Data = nil
		kkapp.App.Render.JSON(w, http.StatusOK, response)
		return
	}

	deviceID := r.Header.Get("X-Device-ID")
	if deviceID != "" {
		go coldstart.MigrateDevicePreferenceToUser(deviceID, me.Id)
	}

	me.Auth = authinfo
	response.Data = me
	response.Status.Subtype = param.Subtype
	kkapp.App.Render.JSON(w, http.StatusOK, response)
}

// GetBindKKID handler to bind (link) current account to kkid
func GetBindKKID(w http.ResponseWriter, r *http.Request) {
	var err error
	var userID string
	var me model.UserInfo
	response := model.MakeOk()
	param := model.GetParams(r)

	// response
	defer func() {
		if err != nil {
			log.Println("[ERROR]", err)
			response.Status.Type = "FAIL"
			response.Status.Message = err.Error()

			switch err.Error() {
			case "guest not allow":
				response.Status.Message = "Invalid Request: Invalid Token or Account"
				response.Status.Subtype = "GuestNotAllow"
				kkapp.App.Render.JSON(w, http.StatusBadRequest, response)

			case "Internal Server Error: KK ID binding":
				response.Status.Subtype = "INTERNAL SERVER ERROR"
				kkapp.App.Render.JSON(w, http.StatusInternalServerError, response)

			case "Not Found: code":
				response.Status.Subtype = "NOT FOUND"
				kkapp.App.Render.JSON(w, http.StatusNotFound, response)

			case "Unauthorized: code":
				response.Status.Subtype = "UNAUTHORIZED"
				kkapp.App.Render.JSON(w, http.StatusUnauthorized, response)

			case "Invalid Request: Invalid Token or Account":
				response.Status.Subtype = "BAD REQUEST"
				kkapp.App.Render.JSON(w, http.StatusBadRequest, response)

			case "Invalid Request: KK ID territory not support":
				response.Status.Subtype = "KKIDTerritoryNotSupport"
				kkapp.App.Render.JSON(w, http.StatusBadRequest, response)

			case "Invalid Request: KK ID already exists":
				response.Status.Subtype = "KKIDExists"
				kkapp.App.Render.JSON(w, http.StatusBadRequest, response)

			case "Invalid Request: KK ID is bound with another KKTV Account":
				response.Status.Subtype = "KKIDAlreadyBound"
				kkapp.App.Render.JSON(w, http.StatusBadRequest, response)

			case "Invalid Request: KK ID and current user are Vip Account":
				response.Status.Subtype = "KKIDAlreadyVIP"
				kkapp.App.Render.JSON(w, http.StatusBadRequest, response)

			default:
				response.Status.Subtype = "OtherError"
				kkapp.App.Render.JSON(w, http.StatusBadRequest, response)
			}

		} else {
			log.Println("[INFO] response")
			kkapp.App.Render.JSON(w, http.StatusOK, response)
		}
	}()

	// check signed in user
	user := r.Context().Value("user").(model.JwtUser)
	if user.IsGuest() {
		err = errors.New("guest not allow")
		return
	}

	// userID
	log.Println(user.Sub)
	userID = user.Sub

	// current user info
	me, _ = model.NewSimpleUserInfo(user.Sub)
	if me.KkboxSub.Valid {
		// this user alredy binded kkbox id
		err = errors.New("Internal Server Error: KK ID binding")
		return
	}

	// check code from kkbox oauth
	if param.Code == "" {
		err = errors.New("Not Found: code")
		return
	}

	if user.Iat != 0 {
		me.LastSignedIn = user.Iat
	}

	token := getKKIDAccessToken(param.Code)
	log.Println("[INFO] token", token)
	if token.AccessToken == "" {
		err = errors.New("Unauthorized: code")
		return
	}
	// get user profile
	profile := getKKIDProfile(url.QueryEscape(token.AccessToken))
	if profile.Profile.Sub == "" || profile.Profile.Name == "" {
		err = errors.New("Invalid Request: Invalid Token or Account")
		return
	}

	param.KKID = profile.Profile.Sub
	param.Username = profile.Profile.Name

	if profile.Profile.Territory.Register != "TW" {
		err = errors.New("Invalid Request: KK ID territory not support")
		return
	}

	// kkboxInfo
	if profile.Profile.Territory.Register != "" {
		param.Territory = profile.Profile.Territory.Register
	}
	//param.IsPrime = profile.IsPrime
	if profile.Profile.Email != "" {
		param.Email = null.String{NullString: sql.NullString{String: profile.Profile.Email, Valid: true}}
	}

	if profile.Profile.PhoneVerified && profile.Profile.Phone != "" {
		if strings.HasPrefix(profile.Profile.Phone, "886") {
			param.Phone = null.String{NullString: sql.NullString{String: fmt.Sprintf("+886%s", strings.TrimPrefix(profile.Profile.Phone, "886")), Valid: true}}
		} else {
			param.Phone = null.String{NullString: sql.NullString{String: profile.Profile.Phone, Valid: true}}
		}
	}

	// lookup kkbox sub
	kkidResp, err := kkapp.App.KKIDOauth.IntraSubKkid(profile.Profile.Sub)
	if err != nil || kkidResp == nil || kkidResp.MSNO == "" {
		log.Println("[ERROR] kkid lookup fail", err)
		err = errors.New("Invalid Request: Invalid Token or Account")
		return
	}

	param.Sub = kkidResp.MSNO
	// SrcToken expiredAt RefreshToken would store at db table refresh_token.provide
	param.SrcToken = token.AccessToken
	param.ExpiredAt = time.Now().Truncate(time.Second).Add(time.Second * time.Duration(token.ExpiresIn)).Unix()
	param.RefreshToken = token.RefreshToken
	param.AppID = profile.Profile.Aud

	param.MediaSource = make(map[string]interface{})

	var kkUser model.UserInfo

	// check if there had one same kkbox sub at DB
	kkUser, _ = model.GetUserBySub(param.Sub)

	if kkUser.Id != "" && kkUser.Id == userID {
		// current user had already bindedq, same user
		err = errors.New("Invalid Request: KK ID already exists")
		return
	}

	if kkUser.Id != "" {

		if kkUser.Role == model.UserRolePremium && me.Role == model.UserRolePremium {
			// current account is vip and the existed kkbox account also a vip
			err = errors.New("Invalid Request: KK ID and current user are Vip Account")
			return
		}

		// KK ID already bound to another KKTV account
		// error MUST return
		if kkUser.OriginProvider.Valid && kkUser.OriginProvider.String == "kkbox" {
			// the same kkbox sub had already bound with another kkbox signup account
			err = errors.New("Invalid Request: KK ID already exists")
		} else {
			err = errors.New("Invalid Request: KK ID is bound with another KKTV Account")
		}

		// hide exist kkid user email or phone
		if kkUser.Email.Valid {
			kkUser.Email = null.NewString(kkutil.BlurEmailPhone(kkUser.Email.String), true)
		}
		if kkUser.Phone.Valid {
			kkUser.Phone = null.NewString(kkutil.BlurEmailPhone(kkUser.Phone.String), true)
		}

		// the kkbox account bind on another user
		data := make(map[string]interface{})
		exist := make(map[string]interface{})  // fill with 'user', 'favorite_titles', 'watch_history'
		signin := make(map[string]interface{}) // fill with current signin user 'user', 'favorite_titles', 'watch_history'

		// fetch kkUser last signed in time
		sdb := kkapp.App.DbUser.Slave()
		noOpsErr := sdb.Get(&kkUser.LastSignedIn, sqlkkbox["lastsignedin"], kkUser.Id)
		if noOpsErr != nil {
			log.Println("[ERROR]", noOpsErr)
		}

		exist["user"] = kkUser
		signin["user"] = me

		// exist kkbox user
		{
			// fetch the exist user favorite titles
			var noOpsErr error
			favorite, noOpsErr := kkuser.NewFavorite(kkUser.Id)
			if noOpsErr != nil {
				log.Println("[ERROR]", noOpsErr)
			} else {
				favorite.Load()
				titleDetails := []*model.TitleDetail{}
				titleDetails, noOpsErr = model.NewTitleDetails(favorite.Titles)
				if noOpsErr != nil {
					log.Println("[ERROR]", noOpsErr)
				}
				exist["favorite_titles"] = titleDetails
			}
			// fetch the exist user watch_history
			ws, noOpsErr := kkuser.NewWatchHistory(kkUser.Id)
			if noOpsErr != nil {
				log.Println("[ERROR]", noOpsErr)
			} else {
				ws.Load()
				titleDetails := []*model.UserTitleDetail{}
				titleDetails, noOpsErr = model.NewUserTitleDetails(kkUser.Id, ws.Titles)
				if noOpsErr != nil {
					log.Println("[ERROR]", noOpsErr)
				}
				exist["watch_history"] = titleDetails
			}

		}

		// current signin user
		{
			// fetch the exist user favorite titles
			var noOpsErr error
			favorite, noOpsErr := kkuser.NewFavorite(me.Id)
			if noOpsErr != nil {
				log.Println("[ERROR]", err)
			} else {
				favorite.Load()
				titleDetails := []*model.TitleDetail{}
				titleDetails, noOpsErr = model.NewTitleDetails(favorite.Titles)
				if noOpsErr != nil {
					log.Println("[ERROR]", noOpsErr)
				}
				signin["favorite_titles"] = titleDetails
			}
			// fetch the exist user watch_history
			ws, noOpsErr := kkuser.NewWatchHistory(me.Id)
			if noOpsErr != nil {
				log.Println("[ERROR]", noOpsErr)
			} else {
				ws.Load()
				titleDetails := []*model.UserTitleDetail{}
				titleDetails, noOpsErr = model.NewUserTitleDetails(me.Id, ws.Titles)
				if noOpsErr != nil {
					log.Println("[ERROR]", noOpsErr)
				}
				signin["watch_history"] = titleDetails
			}

		}

		// we need remember the exist kkbox user id
		// for account merge to verify
		kkboxUserIDKey := fmt.Sprintf(kkboxUserIDFmt, userID)
		conn := kkapp.App.RedisUser.Master()
		// cache 1200 seconds (60 * 20  minutes)
		conn.Cmd("SETEX", kkboxUserIDKey, 1200, kkUser.Id)
		data["exist"] = exist   // exist kkbox account
		data["signin"] = signin // current signin user
		response.Data = data
		return
	}

	// ok, there is no exist kkbox sub existed, now we could bind this user to kkbox sub data
	ms := make(map[string]interface{})
	ms["identifier"] = param.Username
	ms["sub"] = param.Sub
	ms["kkid"] = param.KKID
	// 用戶有在 kkid 做電話驗證，所以也可以使用電話登入 kkid 帳號
	// ( 目前做過電話驗證的 kkid 帳號等於綁定電話，無法用該電話再另外註冊新的 kkid 帳號，也無法移除電話跟帳號的綁定 )
	if profile.Profile.PhoneVerified {
		ms["kkid_verified_phone"] = profile.Profile.Phone
	}
	ms["status"] = param.Status
	ms["territory"] = param.Territory

	kkboxMediaSource, _ := json.Marshal(ms)

	db := kkapp.App.DbUser.Master()
	_, err = db.Exec(sqlkkbox["update"], kkboxMediaSource, param.KKID, userID)
	if err != nil {
		log.Println("[ERROR] KK ID binding", err)
		err = errors.New("Internal Server Error: KK ID binding")
		return
	}

	// Append userinfo in response
	me, err = model.NewUserInfo(user.Sub, db)
	if err != nil {
		response.Data = nil
		return
	}

	if user.Iat != 0 {
		me.LastSignedIn = user.Iat
	}

	// kkbox id check if needed
	if me.KkboxSub.Valid {
		me, err = me.KKBOXIDCheck()
	}
	response.Data = me

}

// PostRevokeKKID handler to revoke kkid user login
func PostRevokeKKID(w http.ResponseWriter, r *http.Request) {
	var err error
	var accessToken KKIDAccessToken
	response := model.MakeOk()

	// response
	defer func() {
		if err != nil {
			log.Println("[ERROR]", err)
			response.Status.Type = "FAIL"
			response.Status.Message = err.Error()
			kkapp.App.Render.JSON(w, http.StatusBadRequest, response)
		} else {
			log.Println("[INFO] response")
			kkapp.App.Render.JSON(w, http.StatusOK, response)
		}
	}()

	r.ParseForm()
	tokens := r.PostForm["access_token"]
	log.Println(len(tokens))
	if len(tokens) < 1 || (len(tokens) == 1 && !strings.Contains(tokens[0], ".")) {
		err = errors.New("token fail")
		return
	}

	// had token and dot
	token := tokens[0]
	rawPayload, err := base64.RawURLEncoding.DecodeString(strings.Split(token, ".")[1])
	if err != nil {
		return
	}
	err = json.Unmarshal(rawPayload, &accessToken)
	if err != nil {
		return
	}

	log.Println(accessToken.Sub)
	revokeUser, _ := model.GetUserByKKIDSub(accessToken.Sub)
	log.Println("[INFO] revoke user", revokeUser, err)

	if revokeUser.Id == "" {
		err = errors.New("user not found")
		return
	}

	// found user
	db := kkapp.App.DbUser.Master()

	_, err = db.Exec(sqlmerge["expireRefreshTokens"], pq.Array([]string{revokeUser.Id}))
	if err != nil {
		return
	}

	_, err = db.Exec(sqlmerge["expireTokens"], pq.Array([]string{revokeUser.Id}))
	if err != nil {
		return
	}
}

func getKKIDAccessToken(code string) (token kktoken) {

	payload := url.Values{
		"grant_type": {"authorization_code"},
		"code":       {code},
	}

	req, _ := http.NewRequest("POST", fmt.Sprintf("%s/auth/v2/token", kkidHost), strings.NewReader(payload.Encode()))

	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.SetBasicAuth(kkapp.App.KKIDOauth.ClientID, kkapp.App.KKIDOauth.Password)

	res, err := KKCurl.Do(req)
	if err != nil {
		return
	}
	defer res.Body.Close()

	body, err := ioutil.ReadAll(res.Body)
	if err != nil {
		return
	}

	json.Unmarshal(body, &token)
	return
}

func getKKIDProfile(token string) (profile kkidprofile) {
	// userinfo
	meURL := fmt.Sprintf("%s/auth/v2/userinfo?access_token=%s", kkidHost, token)
	log.Println(meURL)
	{
		resp, err := KKCurl.Get(meURL)
		if err != nil {
			log.Println("[ERROR] get kkid profile ", err)
			return
		}
		defer resp.Body.Close()
		body, _ := ioutil.ReadAll(resp.Body)
		log.Println(string(body))
		json.Unmarshal(body, &profile.Profile)
	}

	// token info
	// current kkid not return format according to it's documentation
	//tokenURL := fmt.Sprintf("%s/auth/v1/tokeninfo?access_token=%s", kkidHost, token)
	//log.Println(tokenURL)
	//{
	//resp, err := KKCurl.Get(tokenURL)
	//if err != nil {
	//log.Println("[ERROR] get kkid token ", err)
	//return
	//}
	//defer resp.Body.Close()
	//body, _ := ioutil.ReadAll(resp.Body)
	//log.Println(string(body))
	//json.Unmarshal(body, &profile.Token)
	//}

	return
}
