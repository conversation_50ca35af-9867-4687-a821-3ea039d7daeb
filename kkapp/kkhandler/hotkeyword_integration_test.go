package kkhandler

import (
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
)

func TestGetHotkeyWord_IntegrationTest(t *testing.T) {
	r, err := http.NewRequest("GET", "/v3/hot_keywords", nil)

	if err != nil {
		t.Fatal(err)
	}

	w := httptest.NewRecorder()
	handler := http.HandlerFunc(GetHotkeyWord)

	handler.ServeHTTP(w, r)

	if status := w.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v",
			status, http.StatusOK)
	}

	if !strings.Contains(w.Body.String(), "OK") || !strings.Contains(w.Body.String(), "keywords") {
		t.<PERSON>rf("not contains keywords")
	}
}
