package kkhandler

import (
	"math/rand"
	"time"

	cache "gopkg.in/pmylund/go-cache.v2"
)

// handler global
var (
	// Create a cache with a default expiration time of 20 minutes, and which
	// purges expired items every 80 minutes
	// if the lambda instance not last longer than purge time, would create at start
	// via Warm.... func
	MemCache = cache.New(20*time.Minute, 80*time.Minute)
)

func init() {
	rand.Seed(time.Now().Unix())
}
