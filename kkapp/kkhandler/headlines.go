package kkhandler

import (
	"encoding/json"
	"log"
	"net/http"
	"strings"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/kktverror"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/permission"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/request"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/wrapper/middleware"
	zlog "github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	modelmw "github.com/KKTV/kktv-api-v3/pkg/model/middleware"
	"github.com/duke-git/lancet/v2/slice"
)

var (
	headlineKey       = "headlines:v3:json"
	headlineNoLiveKey = "headlinesNoLive:v3:json"
)

// headLines struct only use at this handler
type headLines struct {
	Headlines             []*dbmeta.DbHeadline `json:"headlines"`
	AmplitudeEventContext map[string]string    `json:"amplitude_event_context,omitempty"`
	ExpiredAt             int64                `json:"expiredAt"`
}

// tlHeadline struct only use at this handler
type tlHeadline struct {
	Headlines             []*dbmeta.TitleList `json:"headlines"`
	AmplitudeEventContext map[string]string   `json:"amplitude_event_context,omitempty"`
	ExpiredAt             int64               `json:"expiredAt"`
}

func getTitleListHeadlines(noLive bool) (hl tlHeadline) {
	pool := kkapp.App.RedisMeta.Slave()

	amplitude := make(map[string]string)
	amplitude["showed_mechanism"] = "fixed"
	amplitude["source"] = "human"

	key := headlineKey
	if noLive {
		key = headlineNoLiveKey
	}
	jsonBytes, _ := pool.Cmd("GET", key).Bytes()
	err := json.Unmarshal(jsonBytes, &hl)

	if err != nil {
		log.Println("[ERROR]", err)
	} else {
		now := time.Now().Unix()
		if now < hl.ExpiredAt {
			// not expired yet, use it
			return
		}
	}

	// the cache not validate or expired
	titleList := []*dbmeta.TitleList{}
	result, err := dbmeta.GetPublishHeadlines()
	if err != nil {
		log.Println("[ERROR]", err)
		return
	}

	for idx, _ := range result {
		result[idx].Clean()
		if noLive {
			if result[idx].TitleType.Valid && result[idx].TitleType.String != "live" {
				titleList = append(titleList, result[idx])
			}
		} else {
			titleList = append(titleList, result[idx])
		}
	}

	hl.Headlines = titleList
	hl.AmplitudeEventContext = amplitude
	// expired after 120 seconds
	hl.ExpiredAt = time.Now().Add(120 * time.Second).Unix()
	// write to redis as cache
	{
		pool := kkapp.App.RedisMeta.Master()
		jsonBytes, _ := json.Marshal(hl)
		pool.Cmd("SET", key, jsonBytes)
		log.Println("[INFO] set new headlines to redis")
	}
	return
}

// GetHeadlines cache at "headlines:v0:json"
func GetHeadlines(w http.ResponseWriter, r *http.Request) {
	response := model.MakeOk()
	deviceType := request.GetDeviceTypeFromUserAgent(r)
	membership := dbuser.NonMember
	var (
		hl tlHeadline
	)

	// always render JSON response
	defer func() {
		kkapp.App.Render.JSON(w, http.StatusOK, response)
	}()

	access, ok := r.Context().Value(middleware.KeyAccessUser).(modelmw.AccessUser)
	if ok {
		membership = access.Memberships
	}

	// 2019/11/12 Workaround: For MOD client, not returning live title related headlines
	// after MOD client supports live titles, all functions and redis key with noLive param should be restored
	noLive := false
	userAgent := r.Header.Get("User-Agent")
	parsedUserAgent := strings.Split(userAgent, "/")[0]
	if parsedUserAgent == "com.kktv.kktv.stb" {
		noLive = true
	}
	// End of Workaround

	hl = getTitleListHeadlines(noLive)

	// process roles visibility
	headlines := hl.Headlines
	var canDisplayHeadlines = make([]*dbmeta.TitleList, 0)
	for _, tl := range headlines {
		if err := kkapp.App.PermissionService.Grant(permission.RequestHeadlineLink(tl, membership)); kktverror.IsInternalErr(err) {
			zlog.Warn("getHeadline: permissionService internal error").Err(err).Send()
			continue
		} else if err != nil {
			continue
		}

		needPlatforms := tl.Meta.Platforms
		usersDeviceType := deviceType.String()
		if len(needPlatforms) > 0 && !slice.Contain(needPlatforms, usersDeviceType) {
			continue
		}
		canDisplayHeadlines = append(canDisplayHeadlines, tl)
	}

	// clean meta field
	for i := 0; i < len(canDisplayHeadlines); i++ {
		canDisplayHeadlines[i].Meta = nil
	}
	hl.Headlines = canDisplayHeadlines

	if len(hl.Headlines) > 0 {
		log.Println("[INFO] successful got headline from titlelist")
		response.Data = hl
	}
}
