package kkhandler

import (
	"encoding/json"
	"log"
	"math/rand"
	"net/http"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/datastore/helper"
	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
)

var (
	cacheHighlightKey = "publish:hl" // meta redis cache key "cacher:publish:hl:json"
)

// Highlight api response
type Highlight struct {
	ID                    string               `json:"id"`
	BackgroundFillerColor string               `json:"background_filler_color"`
	BackgroundImageURL    string               `json:"background_image_url"`
	Items                 []*model.TitleDetail `json:"items"`
	Title                 string               `json:"title"`
	Type                  string               `json:"type"`
	// "type": "api.list.highlight_view"

}

// GetHighlight api
func GetHighlight(w http.ResponseWriter, r *http.Request) {
	var err error
	var hl Highlight
	var titleLists []*dbmeta.TitleList

	response := model.MakeOk()

	defer func() {
		kkapp.App.Render.JSON(w, http.StatusOK, response)
	}()

	// fetch publish highlight from cache
	var dataBytes []byte
	cacher, _ := helper.NewCacher(dbmeta.CacheHighlightKey)
	dataBytes, err = cacher.Get()
	if err == nil {
		// get cache
		log.Println("[INFO] got highligh cache")
		err = json.Unmarshal(dataBytes, &titleLists)
	} else {
		// no cache
		titleLists, err = dbmeta.GetPublishHighlight()
		if err != nil {
			log.Println("[ERROR] fetch publish highlight", err)
		}
		if len(titleLists) > 0 {
			// cache the result for 5 minute (300 seconds)
			dataBytes, err = json.Marshal(titleLists)
			if err == nil {
				cacher.SetExpire(dataBytes, 300)
			}
		}
	}

	if len(titleLists) > 0 {
		// random pick
		var pickedList *dbmeta.TitleList
		var titles model.CollectionTitles

		pickedList = titleLists[rand.Intn(len(titleLists))]

		hl.Title = pickedList.Caption
		if pickedList.DominantColor.Valid {
			hl.BackgroundFillerColor = pickedList.DominantColor.String
		}

		hl.BackgroundImageURL = pickedList.Image
		hl.Type = "api.list.highlight_view"

		titles, err = model.NewTitleDetails(pickedList.Meta.TitleID)
		if err != nil {
			log.Println("[ERROR] get title detail", err, pickedList.Meta.TitleID)
		}
		hl.Items = titles.FilterNotExpired()
		hl.ID = pickedList.Meta.ShareId
		if hl.Items == nil {
			hl.Items = model.CollectionTitles{}
		}

		log.Println("[INFO] successful get highlight via titlelist")
		response.Data = hl
		return
	}
}
