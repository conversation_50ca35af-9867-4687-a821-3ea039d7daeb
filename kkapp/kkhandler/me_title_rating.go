package kkhandler

import (
	"encoding/json"
	"log"
	"net/http"
	"strconv"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kkapp/model/kkuser"
)

type rating struct {
	TitleID string `json:"title_id,omitempty"`
	Rating  int    `json:"rating,omitempty"`
}

// PostMeTitleRating title_rating
func PostMeTitleRating(w http.ResponseWriter, r *http.Request) {
	var err error
	var userRating *kkuser.UserRating
	var userid string
	response := model.MakeOk()
	user := r.Context().Value("user").(model.JwtUser)

	var rated rating

	defer func() {
		kkapp.App.Render.JSON(w, http.StatusOK, response)
	}()

	if user.IsGuest() {
		response.Data = model.GuestInfo
		return
	}

	userid = user.Sub

	decoder := json.NewDecoder(r.Body)
	if err = decoder.Decode(&rated); err != nil {
		log.Println("[ERROR]", err)
		response.Status.Type = "Error"
		response.Status.Message = "json body error"
		return
	}

	if _, err = strconv.Atoi(rated.TitleID); err != nil {
		log.Println("[ERROR]", err)
		response.Status.Type = "Error"
		response.Status.Message = "wrong titleid"
		return
	}

	if userRating, err = kkuser.NewUserRating(userid); err != nil {
		log.Println("[ERROR]", err)
		response.Status.Type = "Error"
		response.Status.Message = err.Error()
		return
	}
	// async
	go userRating.Rate(rated.TitleID, rated.Rating)
	return
}
