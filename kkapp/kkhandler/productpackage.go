package kkhandler

import (
	"log"
	"net/http"
	"sort"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/model"
)

// ListProductPackage api handler
func ListProductPackage(w http.ResponseWriter, r *http.Request) {
	response := model.MakeOk()
	urlValue := r.URL.Query()
	platform := urlValue.Get("platform")
	log.Println(platform)

	pp := model.NewProductPackage()

	// list origin packages by "platform" and order by "sort"
	pkgs := pp.ListByPlatform(platform)

	// list billing packages by "platform" and order by "sort"
	billingPkgs := pp.ListBillingPkgByPlatform(platform)
	pkgs = append(pkgs, billingPkgs...)

	// sort by "sort" field of origin packages and billing packages
	sort.Slice(pkgs, func(i, j int) bool {
		return pkgs[i].Sort < pkgs[j].Sort
	})

	// fill origin products into display packages
	groupby := pkgs.Regroup()

	// fill billing products into display packages
	groupby.MapBillingProducts()

	response.Data = map[string]interface{}{
		"product_packages": groupby,
	}
	kkapp.App.Render.JSON(w, http.StatusOK, response)
}
