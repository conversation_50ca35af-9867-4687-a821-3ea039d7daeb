package kkhandler

import (
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/wrapper/meta"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/KKTV/kktv-api-v3/pkg/clock"
	"github.com/KKTV/kktv-api-v3/pkg/elasticsearch"
	"github.com/KKTV/kktv-api-v3/pkg/key"
	plog "github.com/KKTV/kktv-api-v3/pkg/log"
	modelsearch "github.com/KKTV/kktv-api-v3/pkg/model/search"
	"github.com/KKTV/kktv-api-v3/pkg/render"
	"github.com/go-zoo/bone"
)

var (
	sqlSiteMap = map[string]string{
		"titleIds": `select distinct id from meta_title where meta #>> '{available}' = 'true';`,
	}
	countryMap = map[string]string{
		"韓國":   "Korea",
		"日本":   "Japan",
		"中國":   "China",
		"台灣":   "Taiwan",
		"泰國":   "Thailand",
		"香港":   "Hongkong",
		"新加坡":  "Singapore",
		"美國":   "America",
		"西班牙":  "Spain",
		"法國":   "France",
		"加拿大":  "Canada",
		"瑞士":   "Switzerland",
		"阿根廷":  "Argentina",
		"德國":   "Germany",
		"澳洲":   "Australia",
		"巴西":   "Brazil",
		"智利":   "Chile",
		"英格蘭":  "England",
		"芬蘭":   "Finland",
		"伊朗":   "Iran",
		"以色列":  "Israel",
		"義大利":  "Italy",
		"秘魯":   "Peru",
		"菲律賓":  "Philippines",
		"俄羅斯":  "Russia",
		"馬來西亞": "Malaysia",
		"丹麥":   "Denmark",
		"歐美":   "Western",
		"其它":   "Other",
	}

	countryMapEN = reverseMap(countryMap)
)

const ttlListedTitlesViewsCache = 1 * time.Hour

func reverseMap(m map[string]string) (nm map[string]string) {
	nm = make(map[string]string)
	for k, v := range m {
		nm[v] = k
	}
	return
}

func GetSitemap(w http.ResponseWriter, r *http.Request) {
	var err error
	data := make(map[string]interface{})
	response := model.MakeOk()
	siteMapType := bone.GetValue(r, "type")

	defer func() {
		if err != nil {
			plog.Error("v3GetSitemap fail").Err(err).Send()
		}
		render.JSON(w, http.StatusOK, response)
	}()

	es := elasticsearch.NewClient(kkapp.App.SearchHost)
	metaCache := cache.New(kkapp.App.RedisMeta.Master())
	metaDBReader := kkapp.App.DbMeta.Slave()
	h := &sitemapHandler{
		titleRepo:       meta.NewTitleRepository(metaDBReader, metaCache, es),
		titlelistRepo:   meta.NewTitlelistRepository(metaDBReader, metaCache),
		metaCacheWriter: metaCache,
		clock:           clock.New(),
	}

	switch siteMapType {
	case "titles":
		data["titles"], err = getTitleIds()
		if err != nil {
			err = fmt.Errorf("getTitleIds: %w", err)
		}
	case "figure":
		data["figure"], err = h.getFigure()
		if err != nil {
			err = fmt.Errorf("getFigure: %w", err)
		}
	case "title_list":
		data["title_list"], err = h.getTitleListIDs()
		if err != nil {
			err = fmt.Errorf("getTitleListIDs: %w", err)
		}
	case "collection":
		collections, err := h.getCollections()
		if err == nil {
			data["countries"] = collections.Countries
			data["themes"] = collections.Themes
			data["tags"] = collections.Tags
			data["genre"] = collections.Genres
			data["content_agent"] = collections.ContentAgents
		} else {
			err = fmt.Errorf("getCollections: %w", err)
		}
	default:
	}

	response.Data = data
}

type sitemapHandler struct {
	titleRepo       meta.TitleRepository
	clock           clock.Clock
	titlelistRepo   meta.TitlelistRepository
	metaCacheWriter cache.Cacher
}

func getTitleIds() ([]string, error) {
	var titleIds []string
	db := kkapp.App.DbMeta.Slave()
	err := db.Select(&titleIds, sqlSiteMap["titleIds"])
	if err != nil {
		log.Println("[ERROR]", err)
		return nil, err
	}
	return titleIds, nil
}

func (sh *sitemapHandler) getFigure() ([]string, error) {
	onListedTitles, err := sh.listAllOnListedTitles()
	if err != nil {
		return nil, fmt.Errorf("titleRepo: %w", err)
	}

	var allFigure []string
	var isFigureExists = make(map[string]bool)

	for _, m := range onListedTitles {
		for _, f := range m.Figures {
			if _, exists := isFigureExists[f]; !exists {
				allFigure = append(allFigure, f)
				isFigureExists[f] = true
			}
		}
	}
	return allFigure, nil
}

func (sh *sitemapHandler) listAllOnListedTitles() ([]*modelsearch.Title, error) {
	var titles []*modelsearch.Title
	cacheKey := key.GetMetaDataOnListedTitles()

	err := sh.metaCacheWriter.Once(cacheKey, &titles, ttlListedTitlesViewsCache,
		func() (interface{}, error) {
			return sh.titleRepo.ListAllOnListedTitles()
		},
	)
	return titles, err
}

func (sh *sitemapHandler) getTitleListIDs() ([]string, error) {
	records, err := sh.titlelistRepo.ListOnlyTitlesList(sh.clock.Now())
	if err != nil {
		return nil, fmt.Errorf("titlelistRepo: %w", err)
	}

	var shareIDs []string
	for _, record := range records {
		if record.Meta.ShareId != "" {
			shareIDs = append(shareIDs, record.Meta.ShareId)
		}
	}
	return shareIDs, nil
}

type collectionResult struct {
	Countries     []string
	Themes        []string
	Tags          []string
	Genres        []string
	ContentAgents []string
}

func (sh *sitemapHandler) getCollections() (*collectionResult, error) {
	onListedTitles, err := sh.listAllOnListedTitles()
	if err != nil {
		return nil, fmt.Errorf("titleRepo: %w", err)
	}

	result := &collectionResult{}
	exists := make(map[string]map[string]bool)

	for _, k := range []string{"country", "theme", "tag", "genre", "content_agent"} {
		exists[k] = make(map[string]bool)
	}

	collectUnique := func(items []string, category string) []string {
		var vals []string
		for _, item := range items {
			if item != "不使用" && !exists[category][item] {
				vals = append(vals, item)
				exists[category][item] = true
			}
		}
		return vals
	}

	for _, m := range onListedTitles {
		// Country as string
		if _, hasCountry := countryMapEN[m.Country]; hasCountry {
			if _, cached := exists["country"][m.Country]; !cached {
				result.Countries = append(result.Countries, m.Country)
				exists["country"][m.Country] = true

				result.Countries = append(result.Countries, countryMapEN[m.Country])
			}
		}

		// Themes, Tags, Genres as string slice
		result.Themes = append(result.Themes, collectUnique(m.Themes, "theme")...)
		result.Tags = append(result.Tags, collectUnique(m.Tags, "tag")...)
		result.Genres = append(result.Genres, collectUnique(m.Genres, "genre")...)
		result.ContentAgents = append(result.ContentAgents, collectUnique(m.ContentAgents, "content_agent")...)
	}

	return result, nil
}
