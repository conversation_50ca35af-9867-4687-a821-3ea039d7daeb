package kkhandler

import (
	"fmt"
	"log"
	"net/http"
	"strings"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3"
	cache "gopkg.in/pmylund/go-cache.v2"
)

var (
	defaultHotSplashImages = []string{
		"https://image.kktv.com.tw/promotion/201806/HotSplash_6-1.jpg",
		"https://image.kktv.com.tw/promotion/201806/HotSplash_6-10.jpg",
	}
)

func listHotSplash() (images []string) {
	var bucket *string
	var host, path string
	key := fmt.Sprintf("v3:%s:promotion:json", kkapp.App.S3ImageHost)

	svc := s3.New(session.New(), &aws.Config{Region: aws.String("ap-northeast-1")})

	bucket = aws.String(kkapp.App.S3ImageBucket)
	host = kkapp.App.S3ImageHost
	// the folder format YYYYMM
	path = time.Now().Format("promotion/200601")
	log.Println("Fetch the latest HotSplash jpegs", *bucket, path)

	params := &s3.ListObjectsInput{
		Bucket: bucket,
		Prefix: aws.String(path),
	}

	resp, err := svc.ListObjects(params)

	if err != nil {
		log.Println("Failed to list buckets", err)
		return
	}
	for _, key := range resp.Contents {
		// fmt.Println(*key.Key)
		if strings.HasSuffix(*key.Key, "jpg") {
			images = append(images, fmt.Sprintf("%s/%s", host, *key.Key))
		}
	}

	log.Println("IMAGES", images)

	if len(images) == 0 {
		return defaultHotSplashImages
	}

	MemCache.Set(key, images, cache.DefaultExpiration)
	return images
}

// WarmPromotion api pre warm
func WarmPromotion() {
	log.Println("Prewarm /v3/promotions at the beginning")
	listHotSplash()
}

// GetPromotion api
// we place the monthly Splash images at S3, the file name must ends with .jpg
// prod
// s3://kktv-prod-images/promotion/YYYYMM
// test
// s3://kktv-test-images/promotion/YYYYMM
// example:
// s3://kktv-test-images/promotion/201806/HotSplash_6-1.jpg
func GetPromotion(w http.ResponseWriter, r *http.Request) {
	// var getimages []string
	// key := fmt.Sprintf("v3:%s:promotion:json", kkapp.App.S3ImageHost)

	// log.Println(key)
	// images, found := MemCache.Get(key)

	// if !found {
	// 	getimages = listHotSplash()
	// } else {
	// 	getimages = images.([]string)
	// }

	response := model.MakeOk()

	// data := make(map[string]interface{})

	// data["url"] = getimages[rand.Intn(len(getimages))]

	// response.Data = data

	kkapp.App.Render.JSON(w, http.StatusOK, response)
}
