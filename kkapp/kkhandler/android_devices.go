package kkhandler

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/url"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3"
)

var (
	s3AndroidDevicesPathFmt = "s3://kktv-%s-api/v0/android/devices"
)

// GetAndroidDevice api for android devices file on S3
func GetAndroidDevice(w http.ResponseWriter, r *http.Request) {
	var err error
	var fileURL *url.URL
	var filename string
	response := model.MakeOk()

	defer func() {
		if err != nil {
			log.Println("[ERROR]", err)
			response.Status.Type = "Error"
			response.Status.Subtype = ""
			response.Status.Message = err.Error()
			kkapp.App.Render.JSON(w, http.StatusUnauthorized, response)
			return
		}
		kkapp.App.Render.JSON(w, http.StatusOK, response)
	}()

	switch kkapp.App.Env {
	case "prod":
		filename = fmt.Sprintf(s3AndroidDevicesPathFmt, "prod")
		fileURL, _ = url.Parse(filename)
	case "test", "stag", "stage":
		fallthrough
	default:
		filename = fmt.Sprintf(s3AndroidDevicesPathFmt, "test")
		fileURL, _ = url.Parse(filename)
	}

	start := time.Now()
	svc := s3.New(session.New(), &aws.Config{Region: aws.String("ap-northeast-1")})
	params := &s3.GetObjectInput{
		Bucket: aws.String(fileURL.Hostname()),
		Key:    aws.String(fileURL.Path),
	}
	results, err := svc.GetObject(params)
	if err != nil {
		return
	}

	defer results.Body.Close()

	buf := bytes.NewBuffer(nil)
	if _, err = io.Copy(buf, results.Body); err != nil {
		return
	}

	log.Printf("got %v in %v\n", fileURL, time.Now().Sub(start))

	response.Data = json.RawMessage(string(buf.Bytes()))
}
