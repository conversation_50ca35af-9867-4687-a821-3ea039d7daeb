package kkmiddleware

import (
	"encoding/json"
	"net"
	"net/http"

	"github.com/KKTV/kktv-api-v3/kkapp/geoinfo"
)

var (
	thirdPartyBlockedResp = map[string]interface{}{
		"data": nil,
		"status": map[string]string{
			"type":    "FAIL",
			"subtype": "FORBIDDEN",
			"message": "Forbidden: IP-Restriction",
		},
	}

	thirdPartyBlockedByte, _ = json.Marshal(thirdPartyBlockedResp)
)

// ThirdPartyIPFilter check IP in thirdParty IP white list or not
func ThirdPartyIPFilter(thirdPartyIPList []*net.IPNet) func(next http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		fn := func(w http.ResponseWriter, r *http.Request) {

			ipaddr := geoinfo.RealIP(r)
			ip := net.ParseIP(ipaddr)

			for i := range thirdPartyIPList {
				if thirdPartyIPList[i].Contains(ip) {
					// In thirdParty IP White list
					next.ServeHTTP(w, r)
					return
				}
			}

			w.<PERSON><PERSON>().<PERSON>("Content-Type", "application/json")
			w.Write<PERSON>ead<PERSON>(http.StatusForbidden) // 403
			w.Write(thirdPartyBlockedByte)
		}
		return http.HandlerFunc(fn)
	}
}
