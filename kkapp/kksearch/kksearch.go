// kksearch provide the search to elasticsearch = 7.x
// include http handler all sit in this package
// TODO
// 1. ICU tokenlizer
// 2. ReCreateIndex, prepare for alias

package kksearch

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"log"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
	"github.com/KKTV/kktv-api-v3/pkg/key"
	"github.com/KKTV/kktv-api-v3/pkg/model/cachemeta"
)

var (
	// _index -> title
	// _type -> _doc

	curl         = &http.Client{Timeout: time.Duration(time.Second * 8)}
	indexName    = "title"
	titleMapping = []byte(`{
	"dynamic": false,
    "properties": {
        "id": {
            "type": "keyword"
        },
        "title": {
            "type": "text",
                "fields": {
                "keyword": {
                    "type": "keyword",
                    "ignore_above": 256
                }
            }
        },
        "figure": {
            "type": "text",
            "fields": {
                "keyword": {
                    "type": "keyword",
                    "ignore_above": 256
                }
            }
        },
        "title_type": {
            "type": "keyword"
        },
        "country": {
            "type": "keyword"
        },
        "content_agent": {
            "type": "keyword"
        },
        "content_provider": {
            "type": "keyword"
        },
        "release_year": {
            "type": "long"
        },
        "user_rating_count": {
            "type": "long"
        },
        "user_rating": {
            "type": "float"
        },
        "genre": {
            "type": "keyword"
        },
        "tag": {
            "type": "keyword"
        },
        "theme": {
            "type": "keyword"
        },
        "sortid": {
            "type": "keyword"
        },
        "rating": {
            "type": "long"
        },
        "suggest": {
            "type": "completion",
            "analyzer": "simple",
            "preserve_separators": true,
            "preserve_position_increments": true,
            "max_input_length": 50
        },
        "plans": {
          "type": "nested",
          "properties": {
            "plan_id": { "type": "keyword" },
            "plan_name": { "type": "keyword" }
          }
        },
		"is_ending": {
			"type": "boolean"
		}
    }
}`)
	planLustTitleMap = make(map[string]bool)
	onceSrv          sync.Once
)

// ESResponse struct for elasticsearch response
type ESResponse struct {
	Shards struct {
		Failed     int `json:"failed"`
		Skipped    int `json:"skipped"`
		Successful int `json:"successful"`
		Total      int `json:"total"`
	} `json:"_shards"`
	Hits struct {
		Hits []struct {
			ID     string  `json:"_id"`
			Index  string  `json:"_index"`
			Score  float32 `json:"_score"`
			Source Title   `json:"_source"`
			Type   string  `json:"_type"`
		} `json:"hits"`
		MaxScore float32 `json:"max_score"`
		Total    struct {
			Relation string `json:"relation"`
			Value    int64  `json:"value"`
		} `json:"total"`
	} `json:"hits"`
	Aggregations struct {
		Country struct {
			Buckets                 []Bucket `json:"buckets"`
			DocCountErrorUpperBound int      `json:"doc_count_error_upper_bound"`
			SumOtherDocCount        int      `json:"sum_other_doc_count"`
		} `json:"country"`
		Theme struct {
			Buckets                 []Bucket `json:"buckets"`
			DocCountErrorUpperBound int      `json:"doc_count_error_upper_bound"`
			SumOtherDocCount        int      `json:"sum_other_doc_count"`
		} `json:"theme"`
		Genre struct {
			Buckets                 []Bucket `json:"buckets"`
			DocCountErrorUpperBound int      `json:"doc_count_error_upper_bound"`
			SumOtherDocCount        int      `json:"sum_other_doc_count"`
		} `json:"genre"`
		Rating struct {
			Buckets                 []Bucket `json:"buckets"`
			DocCountErrorUpperBound int      `json:"doc_count_error_upper_bound"`
			SumOtherDocCount        int      `json:"sum_other_doc_count"`
		} `json:"rating"`
	} `json:"aggregations"`
	Suggest struct {
		Suggest []struct {
			Length  int64 `json:"length"`
			Offset  int64 `json:"offset"`
			Options []struct {
				ID     string  `json:"_id"`
				Index  string  `json:"_index"`
				Score  float32 `json:"_score"`
				Source Title   `json:"_source"`
				Type   string  `json:"_type"`
				Text   string  `json:"text"`
			} `json:"options"`
			Text string `json:"text"`
		} `json:"suggest"`
	} `json:"suggest"`
	TimedOut bool  `json:"timed_out"`
	Took     int64 `json:"took"`
}

type Bucket struct {
	DocCount int    `json:"doc_count"`
	Key      string `json:"key"`
}

// Title struct for elasticsearch
type Title struct {
	ID              string   `json:"id"`
	Title           []string `json:"title"`
	Figure          []string `json:"figure"`
	TitleType       string   `json:"title_type"`
	Country         string   `json:"country"`
	ReleaseYear     int64    `json:"release_year"`
	Rating          int64    `json:"rating"`
	IsEnding        bool     `json:"is_ending"`
	UserRatingCount int64    `json:"user_rating_count"`
	UserRating      float64  `json:"user_rating"`
	ContentAgent    []string `json:"content_agent"`
	ContentProvider []string `json:"content_provider"`
	Theme           []string `json:"theme"`
	Genre           []string `json:"genre"`
	Tag             []string `json:"tag"`
	Plans           []Plan   `json:"plans"`
	SortID          string   `json:"sortid"`
	Suggest         struct {
		Input []string `json:"input"`
	} `json:"suggest"`
}

type Plan struct {
	PlanID   string `json:"plan_id"`
	PlanName string `json:"plan_name"`
}

// QueryItem compose query
type QueryItem map[string]map[string]interface{}

// TitleQuery compose query
type TitleQuery struct {
	Must   []QueryItem `json:"must,omitempty"`
	Filter []QueryItem `json:"filter,omitempty"`
}

// BoolQuery compose bool query
type BoolQuery struct {
	Bool  TitleQuery `json:"bool,omitempty"`
	Match QueryItem  `json:"match,omitempty"`
}

// IndexQuery struct to query elasticsearch
type IndexQuery struct {
	From  int                  `json:"from"`
	Size  int                  `json:"size"`
	Sort  []QueryItem          `json:"sort,omitempty"`
	Query BoolQuery            `json:"query,omitempty"`
	Aggs  map[string]QueryItem `json:"aggs,omitempty"`
}

// NewIndexQuery new a IndexQuery struct instance
func NewIndexQuery() (indexQuery *IndexQuery, err error) {
	if kkapp.App == nil {
		err = errors.New("kkapp.App not initialize")
		return nil, err
	}
	indexQuery = new(IndexQuery)
	return indexQuery, nil
}

// NewQueryItem for query json dictionary
func NewQueryItem(field string, value interface{}) (item QueryItem) {
	item = make(map[string]map[string]interface{})
	item[field] = map[string]interface{}{"query": value}
	return item
}

// NewMatchItem for filter json dictionary
func NewMatchItem(field string, value interface{}) (item QueryItem) {
	item = make(map[string]map[string]interface{})
	item["match"] = map[string]interface{}{field: value}
	return item
}

// NewFilterItem for filter json dictionary
func NewFilterItem(field string, value interface{}) (item QueryItem) {
	item = make(map[string]map[string]interface{})
	item["term"] = map[string]interface{}{field: value}
	return item
}

// NewNotFilterItem for query excluded field
//
//	{
//		"bool": {
//			"must_not": {
//				"terms": {
//					"theme": [
//						"無修正動畫"
//					]
//				}
//			}
//		}
//	}
func NewNotFilterItem(field string, value interface{}) (item QueryItem) {
	item = make(map[string]map[string]interface{})
	mustNot := make(map[string]interface{})
	mustNot["terms"] = map[string]interface{}{field: value}
	item["bool"] = map[string]interface{}{"must_not": mustNot}
	return item
}

// NewFilterItem for query nested field
//
//	{
//		"nested": {
//			"path": "plans",
//			"query": {
//				"bool": {
//					"must": [
//						{
//							"match": {
//								"plans.plan_name": "lust"
//							}
//						}
//					]
//				}
//			}
//		}
//	}
func NewNestedFilterItem(field string, elem string, value interface{}) (item QueryItem) {
	item = make(map[string]map[string]interface{})
	must := make(map[string]map[string]interface{})
	query := make(map[string]map[string]interface{})
	subField := fmt.Sprintf("%s.%s", field, elem)
	must["match"] = map[string]interface{}{subField: value}
	query["bool"] = map[string]interface{}{"must": must}
	item["nested"] = map[string]interface{}{"path": field, "query": query}
	return item
}

// NewRangeLessThanEqualItem for filter json dictionary
func NewRangeLessThanEqualItem(field string, value interface{}) (item QueryItem) {
	item = make(map[string]map[string]interface{})
	lte := make(map[string]interface{})
	lte["lte"] = value
	item["range"] = map[string]interface{}{field: lte}
	return item
}

// NewAggregateItem for aggregate json dictionary
func NewAggregateItem(field string, value interface{}) (item QueryItem) {
	item = make(map[string]map[string]interface{})
	// elasticsearch aggregate default size => 10, we need more
	item["terms"] = map[string]interface{}{field: value, "size": 50}
	return item
}

// NewSortItem for sort json dictionary
func NewSortItem(field string, order interface{}) (item QueryItem) {
	item = make(map[string]map[string]interface{})
	item[field] = map[string]interface{}{"order": order}
	return item
}

// Suggest for search suggestion
func (index *IndexQuery) Suggest(keyword string) (result []string, err error) {
	var searchResp ESResponse
	payLoadFmt := `{"suggest":{"suggest":{"text":"%s","completion":{"field":"suggest","size":5,"skip_duplicates": true}}}}`
	queryBody := fmt.Sprintf(payLoadFmt, keyword)
	queryURL := fmt.Sprintf("%s/%s/_search", kkapp.App.SearchHost, indexName)
	body, err := sendESRequest("POST", queryURL, []byte(queryBody))
	result = []string{}

	if err != nil {
		log.Println("[ERROR]", string(body))
		return
	}

	err = json.Unmarshal(body, &searchResp)
	if len(searchResp.Suggest.Suggest) > 0 {
		for _, item := range searchResp.Suggest.Suggest[0].Options {
			result = append(result, item.Text)
		}
	}
	return
}

// searchTitle for search title
func searchTitle(keyword string, hideLustContent bool) (result []string, err error) {
	var searchResp ESResponse
	payLoadFmt := `{"size":50,"query":{"bool":{"must":{"match":{"title":{"query":"%s"}}}%s}}}`
	queryBody := fmt.Sprintf(payLoadFmt, keyword, appendMustNot(hideLustContent))
	queryURL := fmt.Sprintf("%s/%s/_search", kkapp.App.SearchHost, indexName)
	body, err := sendESRequest("POST", queryURL, []byte(queryBody))

	if err != nil {
		log.Println("[ERROR]", string(body))
		return
	}

	err = json.Unmarshal(body, &searchResp)
	if len(searchResp.Hits.Hits) > 0 {
		for _, item := range searchResp.Hits.Hits {
			result = append(result, item.ID)
		}
	}
	return
}

// searchFigure for search figure
func searchFigure(keyword string, hideLustContent bool) (result []string, err error) {
	var searchResp ESResponse
	payLoadFmt := `{"size":50,"query":{"bool":{"must":{"query_string":{"query":"figure:\"%s\""}}%s}}}`
	queryBody := fmt.Sprintf(payLoadFmt, keyword, appendMustNot(hideLustContent))
	queryURL := fmt.Sprintf("%s/%s/_search", kkapp.App.SearchHost, indexName)
	body, err := sendESRequest("POST", queryURL, []byte(queryBody))
	resultMap := make(map[string]string)

	if err != nil {
		log.Println("[ERROR]", string(body))
		return
	}

	err = json.Unmarshal(body, &searchResp)
	if len(searchResp.Hits.Hits) > 0 {
		for _, item := range searchResp.Hits.Hits {
			for _, row := range item.Source.Figure {
				if strings.Contains(row, keyword) && resultMap[row] == "" {
					// not in result yet
					result = append(result, row)
					resultMap[row] = row
				}
			}
		}
	}
	return
}

func sendESRequest(method string, url string, payLoad []byte) (body []byte, err error) {
	r, err := http.NewRequest(method, url, bytes.NewBuffer(payLoad))
	r.Header.Set("Content-Type", "application/json")
	resp, err := curl.Do(r)
	if err != nil {
		log.Println("[ERROR]", err)
		return
	}
	body, err = ioutil.ReadAll(resp.Body)

	if err != nil {
		log.Println("[ERROR]", err)
		return
	}

	if kkapp.App.Debug {
		log.Println("[DEBUG] es request", url, method, string(payLoad))
		log.Println("[DEBUG] es response status", resp.StatusCode)
		log.Println("[DEBUG] es response body", string(body))
	}

	if resp.StatusCode != http.StatusOK && resp.StatusCode != http.StatusCreated {
		err = errors.New(string(body))
	}
	return body, err
}

func sendESBulk(url string, payLoad []byte) (body []byte, err error) {
	r, err := http.NewRequest("POST", url, bytes.NewBuffer(payLoad))
	r.Header.Set("Content-Type", "application/x-ndjson")
	resp, err := curl.Do(r)
	if err != nil {
		log.Println("[ERROR]", err)
		return
	}
	body, err = ioutil.ReadAll(resp.Body)

	if err != nil {
		log.Println("[ERROR]", err)
		return
	}

	if kkapp.App.Debug {
		log.Println("[DEBUG] es request", url, string(payLoad))
		log.Println("[DEBUG] es response status", resp.StatusCode)
		log.Println("[DEBUG] es response body", string(body))
	}

	if resp.StatusCode != http.StatusOK && resp.StatusCode != http.StatusCreated {
		err = errors.New(string(body))
	}
	return body, err
}

// SetupES we need one title to set up the very first field mapping
func SetupES(titlemeta *dbmeta.TitleMeta) (err error) {
	var _index, indexURL, mappingURL, titleURL string
	var jsBytes []byte
	var needAddAlias bool

	if _index == "" {
		_index = indexName
	}

	defer func() {
		if err != nil {
			log.Println("[ERROR]", err)
		}
	}()

	// test index alias exist
	indexURL = fmt.Sprintf("%s/%s", kkapp.App.SearchHost, indexName)
	_, err = sendESRequest("GET", indexURL, []byte("{}"))
	if err != nil && strings.Contains(err.Error(), "404") {
		_index = fmt.Sprintf("%s_%s", indexName, time.Now().Format("20060102"))
		needAddAlias = true
	}

	// create index and type with empty payload
	log.Println("[INFO] create empty index", _index)
	indexURL = fmt.Sprintf("%s/%s", kkapp.App.SearchHost, _index)
	_, err = sendESRequest("PUT", indexURL, []byte("{}"))
	if err != nil {
		// not return, could be already exist
		log.Println("[ERROR]", err)
	}
	// create mapping
	log.Println("[INFO] create index mapping", _index)
	mappingURL = fmt.Sprintf("%s/%s/_mapping", kkapp.App.SearchHost, _index)
	_, err = sendESRequest("PUT", mappingURL, titleMapping)
	if err != nil {
		return
	}

	// update the very first title
	title := ToESTitle(titlemeta)
	jsBytes, _ = json.Marshal(title)
	titleURL = fmt.Sprintf("%s/%s/_doc/%s", kkapp.App.SearchHost, _index, title.ID)
	_, err = sendESRequest("POST", titleURL, jsBytes)

	// setup alias
	if needAddAlias {
		aliasURL := fmt.Sprintf("%s/_aliases", kkapp.App.SearchHost)
		payLoadFmt := `{"actions":[{"add":{"index":"%s", "alias": "%s"}}]}`
		payLoad := []byte(fmt.Sprintf(payLoadFmt, _index, indexName))
		_, err = sendESRequest("POST", aliasURL, payLoad)
	}

	return
}

func buildBulkTitle(_index, action string, title Title) (lines [][]byte) {
	actionMap := make(map[string]map[string]string)
	doc := make(map[string]string)

	if _index == "" {
		_index = indexName
	}

	doc["_index"] = _index
	doc["_id"] = title.ID
	actionMap[action] = doc
	actionBytes, _ := json.Marshal(actionMap)

	lines = append(lines, actionBytes)

	if action != "delete" {
		jsBytes, _ := json.Marshal(title)
		log.Println(string(jsBytes))
		lines = append(lines, jsBytes)
	}

	return lines
}

// updateCollection collection additional information
func updateCollection(estitles []Title) (err error) {
	collections := make(map[string]*CollectAddition)
	for _, item := range estitles {
		if item.Country != "" {
			if countryZH, ok := countryMapEN[item.Country]; ok {
				col, _ := NewCollectAddition("country", countryZH)
				keyName := fmt.Sprintf("%s:%s", col.CollectionType, col.CollectionName)
				collections[keyName] = col
			}
		}

		for _, row := range item.Genre {
			col, _ := NewCollectAddition("genre", row)
			keyName := fmt.Sprintf("%s:%s", col.CollectionType, col.CollectionName)
			collections[keyName] = col
		}

		for _, row := range item.Theme {
			col, _ := NewCollectAddition("theme", row)
			keyName := fmt.Sprintf("%s:%s", col.CollectionType, col.CollectionName)
			collections[keyName] = col
		}

		for _, row := range item.Figure {
			col, _ := NewCollectAddition("figure", row)
			keyName := fmt.Sprintf("%s:%s", col.CollectionType, col.CollectionName)
			collections[keyName] = col
		}

		for _, row := range item.Tag {
			col, _ := NewCollectAddition("tag", row)
			keyName := fmt.Sprintf("%s:%s", col.CollectionType, col.CollectionName)
			collections[keyName] = col
		}

		for _, row := range item.ContentAgent {
			col, _ := NewCollectAddition("content_agent", row)
			keyName := fmt.Sprintf("%s:%s", col.CollectionType, col.CollectionName)
			collections[keyName] = col
		}

		for _, row := range item.ContentProvider {
			col, _ := NewCollectAddition("content_provider", row)
			keyName := fmt.Sprintf("%s:%s", col.CollectionType, col.CollectionName)
			collections[keyName] = col
		}
	}

	log.Println("[INFO] update collection additional info")
	for _, v := range collections {
		if !v.Exist() {
			err = v.Write()
			if err != nil {
				break
			}
		}
	}
	return err
}

// SyncAllTitle synchronize all titles meta to elasticsearch
// and write additional collection info
func SyncAllTitle() (err error) {
	titles, err := dbmeta.LoadAllTitles()
	batchSize := 100
	batches := [][]*dbmeta.TitleMeta{}

	if err != nil {
		log.Println("[ERROR]", err)
		return
	}
	if len(titles) == 0 {
		return
	}

	// try to setup elasticsearch and ignore error
	SetupES(titles[0])

	// batch bulk
	for batchSize < len(titles) {
		titles, batches = titles[batchSize:], append(batches, titles[0:batchSize:batchSize])
	}
	batches = append(batches, titles)

	var estitles []Title

	for _, batch := range batches {
		var bulk [][]byte
		for _, item := range batch {
			esTitle := ToESTitle(item)
			if item.IsValidated {
				// IsValidated mean able to index
				bulk = append(bulk, buildBulkTitle(indexName, "index", esTitle)...)
				estitles = append(estitles, esTitle)
			} else {
				bulk = append(bulk, buildBulkTitle(indexName, "delete", esTitle)...)
			}
		}
		if len(bulk) > 0 {
			// append last line break
			bulk = append(bulk, []byte("\n"))
			bulkURL := fmt.Sprintf("%s/%s/_bulk", kkapp.App.SearchHost, indexName)
			sendESBulk(bulkURL, bytes.Join(bulk, []byte("\n")))
		}
	}

	// update collection additional infomation
	updateCollection(estitles)
	return
}

// SyncTitles synchonize specfic titles to elasticsearch
func SyncTitles(titleIDs []string) (err error) {
	titles, err := dbmeta.NewTitleMeta(titleIDs)
	if err != nil {
		log.Println("[ERROR]", err)
		return err
	}

	var estitles []Title

	var bulk [][]byte
	for _, item := range titles {
		item.LoadSeries()
		item.Validate()

		esTitle := ToESTitle(item)
		if item.IsValidated {
			// IsValidated mean able to index
			bulk = append(bulk, buildBulkTitle(indexName, "index", esTitle)...)
			estitles = append(estitles, esTitle)
		} else {
			bulk = append(bulk, buildBulkTitle(indexName, "delete", esTitle)...)
		}
	}
	if len(bulk) > 0 {
		// append last line break
		bulk = append(bulk, []byte("\n"))
		bulkURL := fmt.Sprintf("%s/%s/_bulk", kkapp.App.SearchHost, indexName)
		sendESBulk(bulkURL, bytes.Join(bulk, []byte("\n")))
	}

	// update collection additional infomation
	// updateCollection(estitles)
	return
}

// ToESTitles prepare the title data to feed elasticsearch
func ToESTitles(titlemetas []*dbmeta.TitleMeta) (titles []Title) {
	for _, item := range titlemetas {
		titles = append(titles, ToESTitle(item))
	}
	return
}

// ToESTitle prepare the title data to feed elasticsearch
func ToESTitle(titlemeta *dbmeta.TitleMeta) (title Title) {
	var suggestTitle []string
	var suggestFigure []string
	var IsDrama, IsChildren bool

	title.ID = titlemeta.ID
	title.TitleType = titlemeta.TitleType
	title.ReleaseYear = titlemeta.ReleaseYear
	title.UserRating = titlemeta.UserRating
	title.UserRatingCount = titlemeta.UserRatingCount
	if titlemeta.Rating > 0 {
		title.Rating = titlemeta.Rating
	}

	if titlemeta.Country != nil {
		title.Country = titlemeta.Country.CollectionName
	}

	for _, row := range titlemeta.ContentAgents {
		// should only one
		title.ContentAgent = append(title.ContentAgent, row.CollectionName)
	}

	for _, row := range titlemeta.ContentProviders {
		title.ContentProvider = append(title.ContentProvider, row.CollectionName)
	}

	for _, row := range titlemeta.Themes {
		title.Theme = append(title.Theme, row.CollectionName)
	}

	for _, row := range titlemeta.Genres {
		title.Genre = append(title.Genre, row.CollectionName)
		if row.CollectionName == "戲劇" {
			IsDrama = true
		}

		if row.CollectionName == "親子" {
			IsChildren = true
		}
	}

	for _, row := range titlemeta.Tags {
		title.Tag = append(title.Tag, row.CollectionName)
	}

	// inject collection tags, reference
	//  https://docs.google.com/spreadsheets/d/11cFzSfVdsLrXWKsIq25LZohMTvyk60Y_FiGXzzhpwkY/edit#gid=324694998
	tags := []string{}
	if IsDrama {
		switch title.Country {
		case "Korea":
			tags = append(tags, "韓劇")
		case "Japan":
			tags = append(tags, "日劇")
		case "Taiwan":
			tags = append(tags, "台劇")
		case "China":
			tags = append(tags, "陸劇")
		default:
			tags = append(tags, "其他戲劇")
		}
	}

	// genre:親子 no rating value is for children above 5,
	if IsChildren && title.Rating == 0 {
		title.Rating = 6
	}

	if len(tags) > 0 {
		title.Tag = append(title.Tag, tags...)
	}

	suggestTitle = []string{titlemeta.Title}
	for _, t := range titlemeta.TitleAliases {
		suggestTitle = append(suggestTitle, t)
	}

	for _, row := range titlemeta.Directors {
		suggestFigure = append(suggestFigure, row.CollectionName)
	}

	for _, row := range titlemeta.Writers {
		suggestFigure = append(suggestFigure, row.CollectionName)
	}

	for _, row := range titlemeta.Producers {
		suggestFigure = append(suggestFigure, row.CollectionName)
	}

	for _, row := range titlemeta.Casts {
		suggestFigure = append(suggestFigure, row.CollectionName)
	}

	// title.Suggest.Input = make(map[string][]string)
	title.Suggest.Input = append(suggestTitle, suggestFigure...)

	var endingStr = "0"
	var endYear int64
	var publishDateString string
	var rankingCountryStr string

	if titlemeta.IsEnding == false {
		endingStr = "1"
	}

	if titlemeta.Status == dbmeta.StatusComingSoon.String() {
		endingStr = "2"
	}

	if titlemeta.EndYear == 0 {
		// no EndYear value give this year
		endYear = int64(time.Now().Year())
	} else {
		endYear = titlemeta.EndYear
	}

	if titlemeta.Series != nil && len(titlemeta.Series) > 0 && len(titlemeta.Series[len(titlemeta.Series)-1].Episodes) > 0 {
		// use last episode publish time
		episodes := titlemeta.Series[len(titlemeta.Series)-1].Episodes
		lastEpisode := episodes[len(episodes)-1]

		if lastEpisode.PublishTime > lastEpisode.LicenseStart {
			// use publish time
			publishDateString = time.Unix(lastEpisode.PublishTime, 0).Format("200601021504")
		} else {
			// use license start
			publishDateString = time.Unix(lastEpisode.LicenseStart, 0).Format("200601021504")
		}
	}

	// failover for no Series title, Status == "coming_soon" or title_type == "live"
	if publishDateString == "" {
		publishDateString = time.Unix(titlemeta.TotalLicenseStart, 0).Format("200601021504")
	}

	// default rankingCountryStr, bigger ranking higher
	rankingCountryStr = "000"
	if countryIndex, ok := rankingMap[title.Country]; ok {
		// the country Index should begin from 1
		rankingCountryStr = fmt.Sprintf("%03d", 999-countryIndex)
	}

	title.SortID = fmt.Sprintf("%s%d%s%s%s", endingStr, endYear, publishDateString, rankingCountryStr, titlemeta.ID)

	title.Title = suggestTitle
	title.Figure = suggestFigure
	title.Plans = getTitlePlansFromRedis(titlemeta.ID)
	title.IsEnding = titlemeta.IsEnding

	return title
}

func getTitlePlansFromRedis(id string) []Plan {
	titleIdsMap := fetchPlanLustConfig()
	var plans []Plan
	if _, contained := titleIdsMap[id]; contained {
		plans = append(plans, Plan{PlanID: "", PlanName: "lust"})
	}
	return plans
}

func fetchPlanLustConfig() map[string]bool {
	onceSrv.Do(func() {
		cachePool := kkapp.App.RedisMeta.Slave()
		var titleList []byte
		var err error
		if titleList, err = cachePool.Cmd("HGET", key.MetaGetServiceGeneralConfig(), key.MetaServiceGeneralConfigHashKeys.PlanLustTitleIDs).Bytes(); err != nil {
			log.Println("[ERROR] fetchPlanLustConfig: redis get plan_lust_title_list failed :", err)
			return
		}
		planLustTitleListModel := new(cachemeta.PlanLustTitleList)
		if err := json.Unmarshal(titleList, planLustTitleListModel); err != nil {
			log.Println("[ERROR] fetchPlanLustConfig: unmarshal plan_lust_title_list failed :", err)
			return
		}
		for _, tId := range planLustTitleListModel.TitleIds {
			planLustTitleMap[tId] = true
		}
	})
	return planLustTitleMap
}

func FetchTitleBlockConfig() (titleBlockConfig map[string][]string) {
	cachePool := kkapp.App.RedisMeta.Slave()
	var contentControlBytes []byte
	var err error

	var contentControl cachemeta.ContentControlConfig
	if contentControlBytes, err = cachePool.Cmd("HGET", key.MetaGetServiceGeneralConfig(), key.MetaServiceGeneralConfigHashKeys.ContentControl).Bytes(); err != nil {
		log.Println("[ERROR] fetchTitleBlockConfig: redis get content_control failed :", err)
	} else {
		if err := json.Unmarshal(contentControlBytes, &contentControl); err != nil {
			log.Println("[ERROR] fetchTitleBlockConfig: unmarshal content_control failed :", err)
		} else {
			titleBlockConfig = contentControl.HideLustContent.SearchNotAppend
		}
	}
	return
}

func appendMustNot(hideLustContent bool) (mustNotFilters string) {
	var blockConfig map[string][]string
	if hideLustContent {
		blockConfig = FetchTitleBlockConfig()
	}
	if len(blockConfig) != 0 {
		mustNotFilterFormate := `,"must_not": [%s]`
		blockFilters := []string{}
		for key, config := range blockConfig {
			switch key {
			case "themes":
				blockItems := fmt.Sprintf(`"%s"`, strings.Join(config, `","`))
				blockFilters = append(blockFilters, fmt.Sprintf(`{"terms": {"%s": [%s]}}`, "theme", blockItems))
			}
		}
		mustNotFilters = fmt.Sprintf(mustNotFilterFormate, strings.Join(blockFilters, ","))
	}
	return
}
