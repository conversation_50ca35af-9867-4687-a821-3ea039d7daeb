package bv

import (
	"bytes"
	"encoding/xml"
	"errors"
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"path"
	"regexp"
	"strings"
)

var (
	errMPD              = errors.New("wrong mpd file format")
	rePeriod            = regexp.MustCompile(`<Period .*?>\n`)
	reAdaptation        = regexp.MustCompile(`(?s)<AdaptationSet .*?>(.*?)</AdaptationSet>`)
	reRepresent         = regexp.MustCompile(`(?s)[ ]+?<Representation .*?>(.*?)</Representation>\n`)
	mpdSubtitleTemplate = `  <AdaptationSet lang="%s">
   <Representation bandwidth="256" id="caption_%s" mimeType="text/vtt">
    <BaseURL>%s</BaseURL>
   </Representation>
  </AdaptationSet>
`
)

// BaseURL for subtitle
type BaseURL struct {
	Text string `xml:",chardata"`
}

// Representation for Representation
type Representation struct {
	Text              string `xml:",chardata"`
	Bandwidth         string `xml:"bandwidth,attr"`
	Codecs            string `xml:"codecs,attr"`
	FrameRate         string `xml:"frameRate,attr"`
	Height            string `xml:"height,attr"`
	ID                string `xml:"id,attr"`
	MimeType          string `xml:"mimeType,attr"`
	Sar               string `xml:"sar,attr"`
	Width             string `xml:"width,attr"`
	AudioSamplingRate string `xml:"audioSamplingRate,attr"`
	ContentProtection []struct {
		Text        string `xml:",chardata"`
		SchemeIdUri string `xml:"schemeIdUri,attr"`
		Pssh        struct {
			Text string `xml:",chardata"`
		} `xml:"pssh"`
		Pro struct {
			Text string `xml:",chardata"`
		} `xml:"pro"`
	} `xml:"ContentProtection"`
	AudioChannelConfiguration struct {
		Text        string `xml:",chardata"`
		SchemeIdUri string `xml:"schemeIdUri,attr"`
		Value       string `xml:"value,attr"`
	} `xml:"AudioChannelConfiguration"`
	BaseURL *BaseURL `xml:"BaseURL,omitempty"`

	// internal use
	Data []byte
}

func NewRepresentation(data []byte) (item *Representation) {
	item = new(Representation)
	item.Data = data
	xml.Unmarshal(item.Data, item)
	return item
}

// AdaptationSet in MPD
type AdaptationSet struct {
	Text             string `xml:",chardata"`
	Lang             string `xml:"lang,attr"`
	MaxFrameRate     string `xml:"maxFrameRate,attr"`
	MaxHeight        string `xml:"maxHeight,attr"`
	MaxWidth         string `xml:"maxWidth,attr"`
	Par              string `xml:"par,attr"`
	SegmentAlignment string `xml:"segmentAlignment,attr"`
	StartWithSAP     string `xml:"startWithSAP,attr"`
	Group            string `xml:"group,attr"`
	SegmentTemplate  struct {
		Text           string `xml:",chardata"`
		Duration       string `xml:"duration,attr"`
		Initialization string `xml:"initialization,attr"`
		Media          string `xml:"media,attr"`
		StartNumber    string `xml:"startNumber,attr"`
		Timescale      string `xml:"timescale,attr"`
	} `xml:"SegmentTemplate"`
	ContentProtection struct {
		Text        string `xml:",chardata"`
		DefaultKID  string `xml:"default_KID,attr"`
		SchemeIdUri string `xml:"schemeIdUri,attr"`
		Value       string `xml:"value,attr"`
	} `xml:"ContentProtection"`
	Representation []*Representation `xml:"Representation"`

	// internal parse
	Data                  []byte
	_beforeRepresentation []byte
	_afterRepresentation  []byte
	_Representation       []byte
}

func (i *AdaptationSet) Parse() (err error) {
	var firstIndex int
	firstScope := reRepresent.FindIndex(i.Data)
	if len(firstScope) != 2 {
		return errMPD
	}
	firstIndex = firstScope[0]
	i._beforeRepresentation = append([]byte("  "), i.Data[:firstIndex]...)
	i._Representation = i.Data[firstIndex : len(i.Data)-len("</AdaptationSet>")]
	i._afterRepresentation = []byte("  </AdaptationSet>\n")

	result := reRepresent.FindAllSubmatch(i._Representation, -1)
	for _, item := range result {
		var _representation *Representation
		_representation = NewRepresentation(item[0])
		i.Representation = append(i.Representation, _representation)
	}
	return err
}

func NewAdaptationSet(data []byte) (item *AdaptationSet) {
	item = new(AdaptationSet)
	item.Data = data
	return item
}

// MPD file struct for blendvision
type MPD struct {
	Data           []byte
	Subtitles      []string
	_beforePeriod  []byte
	_Period        []byte
	_afterPeriod   []byte
	_AdaptationSet []*AdaptationSet
}

func (i *MPD) ParseAdaptation() (err error) {
	result := reAdaptation.FindAllSubmatch(i._Period, -1)
	// log.Println(string(result[0][1]))
	for _, item := range result {
		// log.Println(string(item[0]))
		adaptationSet := NewAdaptationSet(item[0])
		err = adaptationSet.Parse()
		if err != nil {
			return
		}
		i._AdaptationSet = append(i._AdaptationSet, adaptationSet)
	}
	return err
}

func (i *MPD) AddSubtitle(relativePath string) (err error) {
	if !strings.HasSuffix(relativePath, ".vtt") {
		return errMPD
	}
	i.Subtitles = append(i.Subtitles, relativePath)
	return nil
}

func (i *MPD) WriteSubtitle() (data []byte) {
	bf := new(bytes.Buffer)
	for _, subtitle := range i.Subtitles {
		// strip ".vtt"
		baseName := path.Base(subtitle)
		lang := baseName[:len(baseName)-4]
		bf.Write([]byte(fmt.Sprintf(mpdSubtitleTemplate, lang, lang, subtitle)))
	}
	return bf.Bytes()
}

func (i *MPD) Parse() (err error) {
	var firstIndex int
	firstScope := rePeriod.FindIndex(i.Data)
	lastIndex := bytes.LastIndex(i.Data, []byte("</Period>"))
	if len(firstScope) != 2 || lastIndex < 0 {
		return errMPD
	}
	firstIndex = firstScope[1]
	i._beforePeriod = i.Data[:firstIndex]
	i._afterPeriod = append([]byte(" "), i.Data[lastIndex:]...)
	i._Period = i.Data[firstIndex:lastIndex]
	i.ParseAdaptation()
	return err
}

// WriteFor specific video height mpd file
func (i *MPD) WriteFor(videoHeight string) []byte {
	dataBuffer := new(bytes.Buffer)
	dataBuffer.Write(i._beforePeriod)
	for _, outer := range i._AdaptationSet {
		dataBuffer.Write(outer._beforeRepresentation)
		for _, inner := range outer.Representation {
			if strings.HasPrefix(inner.MimeType, "video") {
				if inner.Height == videoHeight || videoHeight == "*" {
					dataBuffer.Write(inner.Data)
				}
			} else {
				dataBuffer.Write(inner.Data)
			}
			// dataBuffer.Write(inner.Data)
		}
		dataBuffer.Write(outer._afterRepresentation)
	}
	if len(i.Subtitles) > 0 {
		dataBuffer.Write(i.WriteSubtitle())
	}
	dataBuffer.Write(i._afterPeriod)
	return dataBuffer.Bytes()
}

// NewMPD get a new instance of MPD from file path
func NewMPD(filePath string) (mpd *MPD, err error) {
	xmlFile, err := os.Open(filePath)
	if err != nil {
		log.Println(err)
		return nil, err
	}

	defer xmlFile.Close()

	byteValue, _ := ioutil.ReadAll(xmlFile)
	mpd = new(MPD)
	mpd.Data = byteValue
	err = mpd.Parse()
	if err != nil {
		return nil, err
	}
	return mpd, nil
}

// NewMPDbytes get a new instance of MPD from file path
func NewMPDbytes(src []byte) (mpd *MPD, err error) {
	mpd = new(MPD)
	mpd.Data = src
	err = mpd.Parse()
	if err != nil {
		return nil, err
	}
	return mpd, nil
}
