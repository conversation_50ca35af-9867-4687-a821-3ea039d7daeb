package kkredeem_test

import (
	"fmt"
	"testing"

	"github.com/KKTV/kktv-api-v3/kkapp/kkredeem"
)

func TestGenerateCodes(t *testing.T) {
	prefix := "FH"
	groupId := 4
	count := 5

	g, err := kkredeem.New(prefix, groupId, false)
	if err != nil {
		t.Fatal(err)
	}
	for serial := 1; serial <= count; serial++ {
		fmt.Println(g.CodeFor(serial))
	}
	// Output:
	// FHM28TKHKM
	// FH1VWT0H3M
	// FHMX6T4H0M
	// FHM9LTLH41
	// FH1WVT3HNM
}

func TestGenerateBackwardCompatibleCodes(t *testing.T) {
	prefix := "GC"
	groupId := 3
	count := 5

	g, err := kkredeem.New(prefix, groupId, false)
	if err != nil {
		t.Fatal(err)
	}
	for serial := 1; serial <= count; serial++ {
		fmt.Println(g.<PERSON><PERSON><PERSON>(serial))
	}
	// Output:
	// GCN18T8F5K
	// GCK39TPFMK
	// GCN9LT4F6K
	// GCN2MTYFRK
	// GCN58TLF6K
}
