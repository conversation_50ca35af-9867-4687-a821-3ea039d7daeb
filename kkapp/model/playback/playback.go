package playback

import (
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"strconv"
	"strings"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/kktverror"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/permission"
	plog "github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/model/authority"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	uuid "github.com/satori/go.uuid"
)

const (
	// fmt for redis key
	playbackFmt    = "playback_tokens:v1:%s:json"
	playsessionFmt = "playback_session:v1:%s:ordered_set"

	// HASH store at meta redis, share this from titledetail.go
	keyTitleHashFmt = "meta:v2:title-detail:%s:hash"

	// paytype redis key at playback redis
	paytypeFmt = "paytype:v1:%s:%s:json"
)

const (
	PurposePlayback = "playback"
	PurposeDownload = "download"
)

var (
	// err
	errSession = errors.New("PlaybackSessionError")
	errToken   = errors.New("PlaybackTokenError")

	playbackTokenTTL   int64 = 24 * 60 * 60     // 1 day
	licenseTTLPlayback int64 = 24 * 60 * 60     // 1 day
	licenseTTLDownload int64 = 7 * 24 * 60 * 60 // 7 days
)

// Playback for v3 api
type Playback struct {
	UserID        string `json:"user_id"`
	DeviceID      string `json:"device_id"`
	Medium        string `json:"medium"`
	TitleID       string `json:"title_id"`
	EpisodeID     string `json:"episode_id"`
	KeyID         string `json:"key_id"`
	PlaybackToken string `json:"playback_token"`
	CreatedAt     int64  `json:"created_at"`
	// this is the DRM license expiration time, which will be sent to the license server
	ExpiresAt           int64               `json:"expires_at"`
	Series              []*dbmeta.Series    `json:"-"`
	Episode             *dbmeta.EpisodeMeta `json:"-"`
	SessionLimit        int                 `json:"-"`
	Paytype             string              `json:"-"`
	acceptedAuthorities []authority.Authority
}

func (pb *Playback) GetAcceptedAuthorities() []authority.Authority {
	return pb.acceptedAuthorities
}

type PlaybackRequest struct {
	TitleID   string `json:"title_id"`
	EpisodeID string `json:"episode_id"`
	Medium    string `json:"medium"`
	Purpose   string `json:"purpose"`
}

// old version
// playback_tokens:v1:06e95facc53447e6b83bbf6a0e1c74ef2aa4b3e788ad449896f782e6c457990d:json
// "{\"user_id\":\"4d6d87b11cc43bf240ed6f0d6dc08fc9531cbca555292de589ea923f9b498bd1\",\"title_id\":\"01000291\",\"episode_id\":\"01000291010001\",\"playback_token\":\"06e95facc53447e6b83bbf6a0e1c74ef2aa4b3e788ad449896f782e6c457990d\",\"created_at\":**********,\"expires_at\":1547792415,\"device_id\":\"515c6de9-865c-7219-6b10-1d3b517b7a04\",\"medium\":\"linear\"}"

func (p *Playback) key() (key string) {
	return fmt.Sprintf(playbackFmt, p.PlaybackToken)
}

func (p *Playback) sessionKey() (key string) {
	return fmt.Sprintf(playsessionFmt, p.UserID)
}

// Save playback_tokens to redis
func (p *Playback) Save() {
	pbBytes, _ := json.Marshal(p)
	key := p.key()
	pool := kkapp.App.RedisPlayBack.Master()
	err := pool.Cmd("SETEX", key, playbackTokenTTL, pbBytes).Err

	if err != nil {
		plog.Warn("playback: redis fails to SETEX").Err(err).Str("key", key).Send()
	}

	//sessionkey := p.sessionKey()
	//pool := kkapp.App.RedisPlayBack.Slave()
	//resp, err := pool.Cmd("ZRANGE", key, 0, p.SessionLimit-1, "WITHSCORES").List()
}

// LoadSeries load series from tittle detail
func (p *Playback) LoadSeries() error {

	hashKey := fmt.Sprintf(keyTitleHashFmt, p.TitleID)
	pool := kkapp.App.RedisMeta.Slave()

	arr, err := pool.Cmd("HMGET", hashKey, "series", "accepted_authorities").Array()
	if err != nil {
		return fmt.Errorf("hmget %s failed: %w", hashKey, err)
	}

	series := make([]*dbmeta.Series, 0)
	acceptedAuthorities := make([]authority.Authority, 0)
	for i, v := range []interface{}{&series, &acceptedAuthorities} {
		if bytes, err := arr[i].Bytes(); err != nil {
			return fmt.Errorf("redis: get bytes failed: %w", err)
		} else if err := json.Unmarshal(bytes, v); err != nil {
			return err
		}
	}

	p.Series, p.acceptedAuthorities = series, acceptedAuthorities
	// find the episode
	seriesID := ""
	if len(p.EpisodeID) >= 10 {
		seriesID = p.EpisodeID[0:10]
	}
	for _, series := range p.Series {
		if series.ID != seriesID {
			// if not the right seriesID, skip to next
			continue
		}
		for _, episode := range series.Episodes {
			if episode.ID == p.EpisodeID {
				// found
				p.Episode = episode
				if series.Paytype != "" {
					p.Paytype = series.Paytype
				}
				if episode.KeyID != "" {
					p.KeyID = episode.KeyID
				}
				break
			}
		}
	}
	return nil
}

// Resume playback_tokens from redis
func (p *Playback) Resume() (err error) {
	if p.PlaybackToken == "" {
		return errToken
	}

	key := p.key()
	pool := kkapp.App.RedisPlayBack.Slave()

	pbBytes, err := pool.Cmd("GET", key).Bytes()
	if err != nil {
		// get playback token error
		log.Println("[ERROR] playback token", err)
		return errToken
	}

	err = json.Unmarshal(pbBytes, p)
	if err != nil {
		log.Println("[ERROR] playback format", key)
		return errToken
	}
	return
}

// Session load user session from playback redis
func (p *Playback) Session() (err error) {
	existingTokens := []string{}

	key := p.sessionKey()
	pool := kkapp.App.RedisPlayBack.Slave()

	resp, err := pool.Cmd("ZRANGE", key, 0, p.SessionLimit-1, "WITHSCORES").List()
	if err != nil {
		log.Println("[ERROR] playback session", err)
		return errSession
	}
	var rank int
	for i := 0; i < len(resp); i += 2 {
		log.Println(i, resp[i], resp[i+1])
		tokenDevice := resp[i]
		if strings.HasPrefix(tokenDevice, p.PlaybackToken) {
			// ok, found the session token, err == nil
			return
		}

		sessionCreatedAt, _ := strconv.ParseInt(resp[i+1], 10, 64)
		sessionCreatedAt = -sessionCreatedAt
		if p.CreatedAt >= sessionCreatedAt {
			// session newer than current had
			break
		}

		existingTokens = append(existingTokens, tokenDevice)
		rank++
	}

	if rank < p.SessionLimit {
		// insert this session
		log.Println("Smaller than session limit")
		pool := kkapp.App.RedisPlayBack.Master()
		conn, errRedis := pool.Get()
		if errRedis != nil {
			log.Println("[ERROR]", errRedis)
		}
		defer pool.Put(conn)

		tokenDevice := fmt.Sprintf("%s:%s", p.PlaybackToken, p.DeviceID)
		conn.Cmd("MULTI")
		conn.Cmd("ZADD", key, -p.CreatedAt, tokenDevice)
		conn.Cmd("ZREMRANGEBYRANK", key, p.SessionLimit, -1)
		conn.Cmd("EXPIRE", key, playbackTokenTTL)
		reply := conn.Cmd("EXEC")
		if reply.Err != nil {
			log.Println("[ERROR] playback session", err)
			return errors.New("Cannot active session")
		}
		// successful active this session
		// log active SessionLimitTwo
		if p.SessionLimit > 1 {
			log.Println("[EVENT]", p.UserID, p.DeviceID, p.SessionLimit)
		}

		return
	}

	// no active
	return errSession
}

// Purge session with same deviceID
func (p *Playback) Purge() (err error) {
	purgeTokens := []interface{}{}

	if p.UserID == "" || p.DeviceID == "" || p.PlaybackToken == "" {
		return errToken
	}

	key := p.sessionKey()
	pool := kkapp.App.RedisPlayBack.Slave()
	resp, err := pool.Cmd("ZRANGE", key, 0, -1).List()

	if err != nil {
		log.Println("[ERROR] playback session", err)
		return errSession
	}
	// session item =>
	// playbackToken:deviceID
	for i := 0; i < len(resp); i += 1 {
		tokenDevice := resp[i]
		if !strings.HasPrefix(tokenDevice, p.PlaybackToken) && strings.HasSuffix(tokenDevice, p.DeviceID) {
			// purge the session item not this playblack token but same deviceID
			purgeTokens = append(purgeTokens, tokenDevice)
		}
	}

	if len(purgeTokens) > 0 {
		// purge session item
		log.Println("[INFO] purge session item", p.UserID, purgeTokens)
		pool := kkapp.App.RedisPlayBack.Master()
		err = pool.Cmd("ZREM", append([]interface{}{key}, purgeTokens...)).Err
	}
	return err
}

// VerifyPaytype verify paytype from redis
func (p *Playback) VerifyPaytype(paytype string) (err error) {
	if p.PlaybackToken == "" {
		return errToken
	}

	key := fmt.Sprintf(paytypeFmt, paytype, p.UserID)
	pool := kkapp.App.RedisPlayBack.Slave()

	_, err = pool.Cmd("GET", key).Bytes()
	if err != nil {
		log.Println("[ERROR] paytype", err)
		return err
	}
	return nil
}

// NewToken initializes a new Playback object without saving to storage
func NewToken(userID, deviceID string, pb PlaybackRequest) *Playback {
	playback := new(Playback)
	playback.UserID = userID
	playback.DeviceID = deviceID
	playback.TitleID = pb.TitleID
	playback.EpisodeID = pb.EpisodeID
	playback.Medium = pb.Medium
	playback.CreatedAt = time.Now().Unix()

	var licenseTTL int64
	if pb.Purpose == PurposeDownload {
		licenseTTL = licenseTTLDownload
	} else {
		licenseTTL = licenseTTLPlayback
	}
	playback.ExpiresAt = playback.CreatedAt + licenseTTL
	playback.PlaybackToken = fmt.Sprintf("%s%s",
		strings.Replace(uuid.Must(uuid.NewV4()).String(), "-", "", -1),
		strings.Replace(uuid.Must(uuid.NewV4()).String(), "-", "", -1),
	)

	return playback
}

// NewPlayback for v3 api
// all user could see extra title 預告,或短片
// episodeID is_avod == true allow for all user
// episodeID is_avod == false not allow for expired, guest
func NewPlayback(permissionService permission.Service, userID string, membership dbuser.Membership, deviceID string, pb PlaybackRequest) (playback *Playback, err error) {
	needCheckRole := true
	if strings.HasSuffix(pb.TitleID, "extra") {
		needCheckRole = false
	}

	playback = NewToken(userID, deviceID, pb)

	if needCheckRole {
		// find the episode
		if err := playback.LoadSeries(); err != nil {
			plog.Warn("playback: fail to LoadSeries").Err(err).Str("episode_id", pb.EpisodeID).Send()
		}

		// if no episode
		if playback.Episode == nil {
			return nil, errors.New("NotFoundError")
		}

		// check if license is valid
		currentTime := time.Now().Unix()
		if currentTime < playback.Episode.LicenseStart || currentTime > playback.Episode.LicenseEnd {
			log.Println("license invalid")
			return nil, errors.New("LicenseInvalid")
		}

		req := permission.RequestFullAccessTitleDetail(playback, membership)
		if err := permissionService.Grant(req); err != nil {
			if !errors.Is(err, kktverror.ErrResourceAccessDenied) {
				return nil, err
			}
			if !playback.Episode.IsAvod {
				return nil, errors.New("VIPOnly")
			}
		}
		// check paytype
		if playback.Paytype != "" {
			// require to check paytype
			err = playback.VerifyPaytype(playback.Paytype)
			if err != nil {
				return nil, errors.New("PERMISSION")
			}
		}
	}
	playback.Save()
	return
}

// NewHeartbeat for v3 api
func NewHeartbeat(tokenID, userRole, deviceID string, sessionLimit int) (playback *Playback, err error) {

	playback = new(Playback)
	playback.PlaybackToken = tokenID
	playback.SessionLimit = sessionLimit
	err = playback.Resume()

	if err != nil {
		return nil, err
	}

	if strings.HasSuffix(playback.TitleID, "extra") {
		return
	}

	err = playback.Session()

	return
}

// NewPaytype save user paytype in pb redis
func NewPaytype(userID, paytype string, expires_at int64) (err error) {

	key := fmt.Sprintf(paytypeFmt, paytype, userID)
	pool := kkapp.App.RedisPlayBack.Master()

	now := time.Now().Unix()

	err = pool.Cmd("SETEX", key, expires_at-now, userID).Err
	if err != nil {
		log.Println("[ERROR] paytype", err)
		return err
	}
	return
}
