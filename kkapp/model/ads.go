package model

import (
	"encoding/json"
	"log"
	"sort"

	"github.com/KKTV/kktv-api-v3/kkapp"
)

var (
	adsKey = "meta:v1:ads:json"
)

// Ads struct for api
type Ads struct {
	Ads AdItems `json:"ads"`
}

// AdItems for api
type AdItems []*AdItem

func (a AdItems) Len() int {
	return len(a)
}
func (a AdItems) Swap(i, j int) {
	a[i], a[j] = a[j], a[i]
}
func (a AdItems) Less(i, j int) bool {
	return a[i].StartTime > a[j].StartTime
}

// AdItem for api
type AdItem struct {
	ID       string   `json:"id"`
	Name     string   `json:"name"`
	Source   string   `json:"source"`
	Provider string   `json:"provider"`
	Target   []string `json:"target"`
	// Platforms      []string `json:"platforms,omitempty"`
	ImageURL       string `json:"image_url"`
	StartTime      int64  `json:"start_time,omitempty"`
	EndTime        int64  `json:"end_time,omitempty"`
	ActionLink     string `json:"action_link,omitempty"`
	ActionLinkBg   string `json:"action_link_bg,omitempty"`
	ActionLinkText string `json:"action_link_text,omitempty"`
}

// Save Ads to redis
func (i *Ads) Save() (err error) {
	dataBytes, err := json.Marshal(i)
	if err != nil {
		return
	}

	// redis write
	pool := kkapp.App.RedisMeta.Master()
	err = pool.Cmd("SET", adsKey, dataBytes).Err
	log.Println("Ads Save", err)
	return
}

// NewAllAds get all Ad for console dashboard
func NewAllAds() (ads *Ads, err error) {
	ads = new(Ads)
	// read only
	pool := kkapp.App.RedisMeta.Slave()
	resp, err := pool.Cmd("GET", adsKey).Bytes()
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(resp, ads)
	if err != nil {
		return nil, err
	}
	sort.Sort(ads.Ads)
	return
}
