package users

import (
	"database/sql"
	"errors"
	"time"

	"github.com/jmoiron/sqlx"
	"gopkg.in/guregu/null.v3"
)

type User struct {
	ID                      string      `db:"id" json:"id"`
	Email                   null.String `db:"email" json:"email"`
	Phone                   null.String `db:"phone" json:"phone"`
	AvatarURL               null.String `db:"avatar_url" json:"avatar_url"`
	Name                    null.String `db:"name" json:"name"`
	Birthday                null.Time   `db:"birthday" json:"birthday"`
	Gender                  null.String `db:"gender" json:"gender"`
	ExpiredAt               null.Time   `db:"expired_at" json:"expired_at"`
	Master                  null.String `db:"master" json:"master"`
	CreatedAt               time.Time   `db:"created_at" json:"created_at"`
	UpdatedAt               null.Time   `db:"updated_at" json:"updated_at"`
	Role                    null.String `db:"role" json:"role"`
	MediaSource             null.String `db:"media_source" json:"media_source"`
	AutoRenew               bool        `db:"auto_renew" json:"auto_renew"`
	PaymentInfo             null.String `db:"payment_info" json:"payment_info"`
	Type                    null.String `db:"type" json:"type"`
	CreatedBy               string      `db:"created_by" json:"created_by"`
	ColdStartSelectedTitles null.String `db:"cold_start_selected_titles" json:"cold_start_selected_titles"`
	KkidBoundAt             null.Time   `db:"kkid_bound_at" json:"kkid_bound_at"`
	OriginProvider          null.String `db:"origin_provider" json:"origin_provider"`
	Kkid                    null.String `db:"kkid" json:"kkid"`
	RevokedAt               null.Time   `db:"revoked_at" json:"revoked_at"`
	UnsubscribedEdmAt       null.Time   `db:"unsubscribed_edm_at" json:"unsubscribed_edm_at"`
	Password                null.String `db:"password" json:"password"`
	EmailVerifiedAt         null.Time   `db:"email_verified_at" json:"email_verified_at"`
	PhoneVerifiedAt         null.Time   `db:"phone_verified_at" json:"phone_verified_at"`
	LastLoginAt             null.Time   `db:"last_login_at" json:"last_login_at"`
}

func GetActiveByID(id string, db *sqlx.DB) (*User, error) {
	user := new(User)
	s := `SELECT u.*
		FROM users u
		WHERE u.id = $1 AND u.revoked_at IS NULL`
	if err := db.Get(user, s, id); errors.Is(err, sql.ErrNoRows) {
		return nil, nil
	} else if err != nil {
		return user, err
	}
	return user, nil
}

func GetByID(id string, db *sqlx.DB) (*User, error) {
	user := new(User)
	s := `SELECT u.*
		FROM users u
		WHERE u.id = $1`
	if err := db.Get(user, s, id); errors.Is(err, sql.ErrNoRows) {
		return nil, nil
	} else if err != nil {
		return user, err
	}
	return user, nil
}
