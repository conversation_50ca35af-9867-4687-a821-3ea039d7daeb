package kkuser

import (
	"errors"
	"fmt"
	"log"
	"strings"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp"
)

const (
	userWatchHistoryFmt = "watch-history:v1:%s:json"
)

// WatchHistory for user favorited titles
type WatchHistory struct {
	UserID string   `json:"user_id,omitempty"`
	Titles []string `json:"titles,omitempty"`
}

func (i *WatchHistory) key() (key string) {
	return fmt.Sprintf(userWatchHistoryFmt, i.UserID)
}

// Load user watchhistory titleID
func (i *WatchHistory) Load() {
	// type of key => zset
	key := i.key()
	redisPool := kkapp.App.RedisUser.Slave()
	titles, err := redisPool.Cmd("ZREVRANGE", key, 0, -1).List()

	if err != nil {
		log.Println("[ERROR]", err)
	}

	historyLen := len(titles)

	if historyLen >= 20 {
		i.Titles = titles[0:20]
	} else {
		i.Titles = titles
	}

	// clean up, if this watch history getting to long
	if historyLen > 28 {
		go func(clearKey string) {
			conn := kkapp.App.RedisUser.Master()
			resp := conn.Cmd("ZREMRANGEBYRANK", clearKey, "0", "-21")
			if resp.Err != nil {
				log.Println("[ERROR] clear watch history error", clearKey, resp.Err)
			}
		}(key)
	}

}

// Add add a titleID
func (i *WatchHistory) Add(titleID string) (err error) {
	var titles []string
	if titleID != "" {
		key := i.key()
		redisPool := kkapp.App.RedisUser.Master()
		r := redisPool.Cmd("ZADD", key, time.Now().Unix(), titleID)
		if r.Err != nil {
			log.Println("[ERROR]", r.Err)
			err = r.Err
			return
		}

		titles, err = redisPool.Cmd("ZREVRANGE", key, 0, -1).List()
		if err != nil {
			log.Println("[ERROR]", err)
		}
		i.Titles = titles
	}

	return
}

// Remove remove  titleids from user
func (i *WatchHistory) Remove(titleids string) (err error) {
	var titles []string
	if titleids != "" {
		// handle multiple titleid join with comma
		removeTitles := strings.Split(titleids, ",")
		key := i.key()
		redisPool := kkapp.App.RedisUser.Master()
		r := redisPool.Cmd("ZREM", key, removeTitles)
		if r.Err != nil {
			log.Println("[ERROR]", r.Err)
			err = r.Err
			return
		}

		titles, err = redisPool.Cmd("ZREVRANGE", key, 0, -1).List()
		if err != nil {
			log.Println("[ERROR]", err)
			return
		}
		i.Titles = titles
	}
	return
}

// Destory delete all watch history from user
func (i *WatchHistory) Destory() (err error) {
	key := i.key()
	redisPool := kkapp.App.RedisUser.Master()
	r := redisPool.Cmd("DEL", key)
	if r.Err != nil {
		log.Println("[ERROR]", r.Err)
		err = r.Err
		return
	}
	i.Titles = []string{}
	return
}

// NewWatchHistory for user favorited titles
func NewWatchHistory(userID string) (item *WatchHistory, err error) {
	if userID == "" {
		err = errors.New("empty UserID")
	}

	item = new(WatchHistory)
	item.UserID = userID
	return
}
