package payment_info

import (
	"database/sql"
	"errors"
	"time"

	"github.com/KKTV/kktv-api-v3/pkg/iap"
	zlog "github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/jmoiron/sqlx"
	"gopkg.in/guregu/null.v3"
)

type PaymentInfo struct {
	UserID                 string      `db:"user_id" json:"user_id"`
	Email                  null.String `db:"email" json:"email"`
	CreditCard6no          null.String `db:"credit_card_6no"`
	CreditCard4no          null.String `db:"credit_card_4no"`
	CreditCardTokenValue   null.String `db:"credit_card_token_value"`
	IapReceiptData         null.String `db:"iap_receipt_data" json:"iap_receipt_data"`
	IapReceiptDataHash     null.String `db:"iap_receipt_data_hash" json:"iap_receipt_data_hash"`
	PaymentType            string      `db:"payment_type" json:"payment_type"`
	CaringCode             null.String `db:"caring_code" json:"caring_code"`
	Recipient              null.String `db:"recipient" json:"recipient"`
	RecipientAddress       null.String `db:"recipient_address" json:"recipient_address"`
	CarrierType            null.String `db:"carrier_type" json:"carrier_type"`
	CarrierValue           null.String `db:"carrier_value" json:"carrier_value"`
	CreatedAt              time.Time   `db:"created_at" json:"created_at"`
	UpdatedAt              null.Time   `db:"updated_at" json:"updated_at"`
	Phone                  null.String `db:"phone" json:"phone"`
	CreditCardTokenTerm    null.String `db:"credit_card_token_term" json:"credit_card_token_term"`
	IapLatestTransactionID null.String `db:"iap_latest_transaction_id" json:"iap_latest_transaction_id"`
	IapLatestExpiresDate   null.Time   `db:"iap_latest_expires_date" json:"iap_latest_expires_date"`
	TelecomMpID            null.String `db:"telecom_mp_id" json:"telecom_mp_id"`
	TstarOrderID           null.String `db:"tstar_order_id" json:"tstar_order_id"`
	TstarContractID        null.String `db:"tstar_contract_id" json:"tstar_contract_id"`
	ModSubscriberID        null.String `db:"mod_subscriber_id" json:"mod_subscriber_id"`
	ModSubscriberArea      null.String `db:"mod_subscriber_area" json:"mod_subscriber_area"`
	IabReceiptData         null.String `db:"iab_receipt_data" json:"iab_receipt_data"`
	IabOrderID             null.String `db:"iab_order_id" json:"iab_order_id"`
	IabLatestOrderID       null.String `db:"iab_latest_order_id" json:"iab_latest_order_id"`
	IabLatestExpiresDate   null.Time   `db:"iab_latest_expires_date" json:"iab_latest_expires_date"`
	FamilyID               null.String `db:"family_id" json:"family_id"`
	Backup                 null.String `db:"backup"`
}

func GetByUserID(userID string, db *sqlx.DB) (*PaymentInfo, error) {
	paymentInfo := new(PaymentInfo)
	s := `SELECT p.*
		FROM payment_info p
		WHERE p.user_id = $1`
	if err := db.Get(paymentInfo, s, userID); errors.Is(err, sql.ErrNoRows) {
		return paymentInfo, nil
	} else if err != nil {
		return paymentInfo, err
	}
	return paymentInfo, nil
}

func (p *PaymentInfo) VerifyIAPReceipt() (resp iap.Response, err error) {
	if !p.IapReceiptData.Valid {
		err = errors.New("iap_receipt_data is empty")
		return
	}

	iapClient := iap.NewClient()
	if resp, err = iapClient.VerifyReceipt(p.IapReceiptData.String); err != nil {
		return
	}

	zlog.Info("verify iap receipt").Interface("iap response", resp).Send()

	return
}
