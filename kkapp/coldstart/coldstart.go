package coldstart

import (
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/pkg/key"
	zlog "github.com/KKTV/kktv-api-v3/pkg/log"
)

func MigrateDevicePreferenceToUser(deviceID, userID string) {
	redisPool := kkapp.App.RedisUser.Master()

	// device preference key
	coldstartDevicePreferenceKey := key.GetColdStartPreferenceOfDevice(deviceID)

	// get device preference key
	devicePref, err := redisPool.Cmd("GET", coldstartDevicePreferenceKey).Str()
	if err != nil {
		zlog.Warn("coldstart: failed to get device preference").
			Str("redis_key", coldstartDevicePreferenceKey).
			Err(err).Send()

	} else if len(devicePref) > 0 { // if device preference exists

		// get user preference key
		coldstartUserPreferenceKey := key.GetColdStartPreferenceOfUser(userID)

		// delete original user preference
		err = redisPool.Cmd("DEL", coldstartUserPreferenceKey).Err
		if err != nil {
			zlog.Warn("coldstart: failed to delete user preference").
				Str("redis_key", coldstartUserPreferenceKey).
				Err(err).Send()
		}

		// set new selected user preference
		err = redisPool.Cmd("SET", coldstartUserPreferenceKey, devicePref).Err
		if err != nil {
			zlog.Warn("coldstart: failed to set user preference").
				Str("redis_key", coldstartUserPreferenceKey).
				Err(err).Send()
		}

		// set 30 days for expiry
		m := time.Hour * 24 * 30
		redisPool.Cmd("EXPIRE", coldstartUserPreferenceKey, m.Seconds())

		// delete device preference
		err = redisPool.Cmd("DEL", coldstartDevicePreferenceKey).Err
		if err != nil {
			zlog.Warn("coldstart: failed to delete device preference").
				Str("redis_key", coldstartDevicePreferenceKey).
				Err(err).Send()
		}

		zlog.Info("coldstart: migrate device preference to user").
			Str("device_id", deviceID).
			Str("user_id", userID).
			Send()
	}
}
