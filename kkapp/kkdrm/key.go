package kkdrm

import (
	"crypto/md5"
	"crypto/sha1"
	"encoding/json"
	"errors"
	"fmt"
	"hash"
	"log"
	"net/http"
	"strings"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/go-zoo/bone"
	"golang.org/x/crypto/pbkdf2"
)

var (
	_salt1      = []byte("c83bc4ec5df8c736b8e296069c6a7fab")
	_salt2      = []byte("8ed04770e7622d6473c3e59fba49e334")
	_salt3      = []byte("00000f3454a7748bfec820ffee6b76ba")
	_iterations = 100000
	_defaultTTL = 2592000 // 1 month => 24*60*60*30
	_drmKeyFmt  = "key:v1:%s:hash"

	errEmptyKey     = errors.New("empty key")
	errRedisBackend = errors.New("backend fail")
)

func hashThenHex(h func() hash.Hash, password []byte, salt []byte) []byte {
	return pbkdf2.Key(password, salt, _iterations, h().Size(), h)
}

func createKeyID(contentID, contentType string) (keyID string) {
	//key_id = hash_than_hex('md5', '.'.join([content_id, content_type]), salt1)
	return fmt.Sprintf("%x", hashThenHex(md5.New, []byte(strings.Join([]string{contentID, contentType}, ".")), _salt1))
}

func getKeyFromID(keyID string) string {
	keyIDSHA1 := fmt.Sprintf("%x", hashThenHex(sha1.New, []byte(keyID), _salt2))
	return fmt.Sprintf("%x", hashThenHex(md5.New, []byte(keyIDSHA1), _salt3))
}

// DRMKey kktv drm key struct
type DRMKey struct {
	KeyID       string `json:"key_id"`
	KeyValue    string `json:"key_value"`
	ContentID   string `json:"content_id,omitempty"`
	ContentType string `json:"content_type,omitempty"`
}

// Fetch the key value from backend
func (k *DRMKey) Fetch() (err error) {
	pool := kkapp.App.RedisMeta.Slave()
	hashMap, err := pool.Cmd("HGETALL", k.Key()).Map()
	if err != nil || hashMap["content_id"] == "" {
		return errEmptyKey
	}

	jsonBytes, err := json.Marshal(hashMap)
	if err != nil {
		return err
	}
	json.Unmarshal(jsonBytes, k)
	return
}

// Save the key value to backend with default TTL
func (k *DRMKey) Save() (err error) {
	hashCmd := []interface{}{"key_value", k.KeyValue}
	if k.ContentID != "" {
		hashCmd = append(hashCmd, "content_id", k.ContentID)
	}

	if k.ContentType != "" {
		hashCmd = append(hashCmd, "content_type", k.ContentType)
	}

	pool := kkapp.App.RedisMeta.Master()
	conn, err := pool.Get()

	if err != nil {
		return err
	}
	defer pool.Put(conn)

	hashKey := k.Key()
	log.Println(hashKey, hashCmd)
	conn.Cmd("MULTI")
	conn.Cmd("HMSET", hashKey, hashCmd)
	err = conn.Cmd("EXEC").Err

	return err
}

// Remove the key from redis
func (k *DRMKey) Remove() (err error) {
	pool := kkapp.App.RedisMeta.Master()
	conn, err := pool.Get()

	if err != nil {
		return err
	}
	defer pool.Put(conn)
	hashKey := k.Key()
	err = pool.Cmd("DEL", hashKey).Err
	return err
}

// Key for the redis backend
func (k *DRMKey) Key() string {
	return fmt.Sprintf(_drmKeyFmt, k.KeyID)
}

// NewDRMKeyFromID get a drm key from keyID
func NewDRMKeyFromID(keyID string) (k *DRMKey, err error) {
	if keyID == "" {
		return nil, errEmptyKey
	}
	k = new(DRMKey)
	k.KeyID = keyID

	if fetchErr := k.Fetch(); fetchErr != nil {
		log.Println(fetchErr)
		k.KeyValue = getKeyFromID(k.KeyID)
	}

	return k, err
}

// NewDRMKeyFromContent get a drm key from contentID, contentType
func NewDRMKeyFromContent(contentID, contentType string) (k *DRMKey, err error) {
	if contentID == "" {
		return nil, errEmptyKey
	}

	k = new(DRMKey)
	k.KeyID = createKeyID(contentID, contentType)
	k.ContentID = contentID
	k.ContentType = contentType
	if fetchErr := k.Fetch(); fetchErr != nil {
		k.KeyValue = getKeyFromID(k.KeyID)
	}

	return
}

// GetKey get keys http handler
func GetKey(w http.ResponseWriter, r *http.Request) {
	var err error
	var drmKey *DRMKey
	response := model.MakeOk()

	defer func() {
		if err != nil {
			log.Println("[ERROR]", err)
			response.Status.Type = "FAIL"
			if err != nil {
				response.Status.Message = err.Error()
			}
			kkapp.App.Render.JSON(w, http.StatusInternalServerError, response)
		} else {
			kkapp.App.Render.JSON(w, http.StatusOK, response)
		}
	}()

	keyID := bone.GetValue(r, "keyid")
	drmKey, err = NewDRMKeyFromID(keyID)
	if err != nil {
		return
	}
	if drmKey.ContentID == "" {
		// no validate key
		err = errEmptyKey
		return
	}
	response.Data = drmKey
}
