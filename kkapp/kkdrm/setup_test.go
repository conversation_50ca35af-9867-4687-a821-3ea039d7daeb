//go:build integration

package kkdrm

import (
	"fmt"
	"log"
	"os"
	"testing"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kktvapi/config"
)

func TestMain(m *testing.M) {
	// call flag.Parse() here if TestMain uses flags
	fmt.Println("========================================")
	fmt.Println("TEST suite kkdrm ContextInit")
	fmt.Println("========================================")

	if err := config.Init(); err != nil {
		log.Fatal("init config fail, err", err)
	}
	kkapp.ContextInit()
	result := m.Run()

	fmt.Println("========================================")
	fmt.Println("TEST suite kkdrm TearDown")
	fmt.Println("========================================")
	os.Exit(result)
}
