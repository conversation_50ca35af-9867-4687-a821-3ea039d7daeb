package amplitude

import (
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"log"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp"
)

type AccountDeleted struct {
	UserID          string                 `json:"user_id"`
	EventType       string                 `json:"event_type"`
	Time            time.Time              `json:"time"`
	UserRevokedAt   time.Time              `json:"-"`
	EventProperties map[string]interface{} `json:"event_properties"`
}

func NewAccountDeleted(userID string, userRevokedAt time.Time, reason string) (event *AccountDeleted, err error) {
	event = new(AccountDeleted)
	if userID == "" {
		return event, errors.New("not a userid")
	}
	property := make(map[string]interface{})
	property["log version"] = "0.7.0"
	property["account user id"] = userID
	property["reason"] = reason

	event.UserID = userID
	event.EventType = "Account Deleted"
	event.Time = time.Now()
	event.UserRevokedAt = userRevokedAt
	event.EventProperties = property

	return event, nil
}

// Send to amplitude
func (event *AccountDeleted) Send() {
	var apikey string

	if kkapp.App.Debug {
		apikey = devAPIKey
	} else {
		apikey = prodAPIKey
	}

	/**************************************************
	 * Send User identity
	 **************************************************/

	userData := fmt.Sprintf(
		`{
			"user_id": "%s",
			"user_properties": {
				"$setOnce": {
					"account deleted date": "%s",
				},
				"$set": {
					"account state": "deleted"
				}
			}
		}`,
		event.UserID,
		event.UserRevokedAt.Format("2006-01-02"))

	log.Println("[INFO] - Account Deleted - Send user identity - User Data:", userData)

	form := url.Values{}
	form.Add("api_key", apikey)
	form.Add("identification", userData)

	req, err := http.NewRequest("POST", amplitudeIdentityURL, strings.NewReader(form.Encode()))

	if err != nil {
		log.Println("[ERROR] - Account Deleted - Send user identity - New Request failed.", err)
		return
	}

	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	client := &http.Client{Timeout: time.Duration(time.Second * 10)}
	resp, err := client.Do(req)

	if err != nil {
		// handle error
		log.Println("[ERROR] - Account Deleted - Send user identity - New http.Client failed. ", err)
		return
	}

	defer resp.Body.Close()
	statuscode := resp.StatusCode
	body, _ := ioutil.ReadAll(resp.Body)

	log.Println("[INFO] - Account Deleted - Send user identity - Amplitude body:", string(body))
	log.Println("[INFO] - Account Deleted - Send user identity - Amplitude status:", statuscode)

	if statuscode != 200 {
		log.Println("[ERROR] - Account Deleted - Send user identity - Send Amplitude fail", userData)
	}

	/**************************************************
	 * Send Event
	 **************************************************/

	jsonBytes, err := json.Marshal([]interface{}{event})

	if err != nil {
		log.Println("[ERROR] event json error", err)
		return
	}
	form = url.Values{}
	form.Add("api_key", apikey)
	form.Add("event", string(jsonBytes))

	req, err = http.NewRequest("POST", amplitudeHTTPURL, strings.NewReader(form.Encode()))

	if err != nil {
		// handle error
		log.Println("[ERROR]", err)
		return
	}

	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	client = &http.Client{Timeout: time.Duration(time.Second * 10)}
	resp, err = client.Do(req)

	if err != nil {
		// handle error
		log.Println("err:", err)
		return
	}

	defer resp.Body.Close()
	statuscode = resp.StatusCode
	body, _ = ioutil.ReadAll(resp.Body)

	log.Println("[INFO] amplitude post:", string(jsonBytes))
	log.Println("[INFO] amplitude response body:", string(body))
	log.Println("[INFO] amplitude response status:", statuscode)

	if statuscode != 200 {
		log.Println("[ERROR] amplitue fail", event)
	}
}
