package amplitude

import (
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"log"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/model/dbuser"
)

// AccountTransactionCompleted Account Transaction Completed
type AccountTransactionCompleted struct {
	UserID          string          `json:"user_id"`
	EventType       string          `json:"event_type"`
	Time            time.Time       `json:"time"`
	Price           int             `json:"price"`
	Quantity        int             `json:"quantity"`
	Revenue         int             `json:"revenue"`
	EventProperties EventProperties `json:"event_properties"`
}

// Send to amplitude
// please refer
// https://github.com/KKTV/kktv-sns-v0-amplitude/blob/develop/listeners/transaction_completed_v2.js
func (event *AccountTransactionCompleted) Send() {
	var apikey string

	if kkapp.App.Debug {
		apikey = devAPIKey
	} else {
		apikey = prodAPIKey
	}

	/**************************************************
	 * Send User identity
	 **************************************************/

	userData := fmt.Sprintf(`[{
	'user_id': '%s',
	'user_properties':{
		'$set': {
			'account current membership': 'premium',
			'previous payment type': '%s',
			'previous plan': '%s',
		},
		'$add': {
			'total transactions count': 1,
			'total transactions amount': %d,
		},
	}
	}]
	`, event.UserID, event.EventProperties.PaymentType, event.EventProperties.Plan, event.EventProperties.Revenue)

	log.Println("[INFO] - Account Transaction Completed - Send user identity - User Data:", userData)

	form := url.Values{}
	form.Add("api_key", apikey)
	form.Add("identification", userData)

	req, err := http.NewRequest("POST", amplitudeIdentityURL, strings.NewReader(form.Encode()))

	if err != nil {
		log.Println("[ERROR] - Account Transaction Completed - Send user identity - New Request failed.", err)
		return
	}

	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	client := &http.Client{Timeout: time.Duration(time.Second * 10)}
	resp, err := client.Do(req)

	if err != nil {
		// handle error
		log.Println("[ERROR] - Account Transaction Completed - Send user identity - New http.Client failed. ", err)
		return
	}

	defer resp.Body.Close()
	statuscode := resp.StatusCode
	body, _ := ioutil.ReadAll(resp.Body)

	log.Println("[INFO] - Account Transaction Completed - Send user identity - Amplitude body:", string(body))
	log.Println("[INFO] - Account Transaction Completed - Send user identity - Amplitude status:", statuscode)

	if statuscode != 200 {
		log.Println("[ERROR] - Account Transaction Completed - Send user identity - Send Amplitude fail", userData)
	}

	/**************************************************
	 * Send Event
	 **************************************************/

	jsonBytes, err := json.Marshal([]interface{}{event})

	if err != nil {
		log.Println("[ERROR] event json error", err)
		return
	}
	form = url.Values{}
	form.Add("api_key", apikey)
	form.Add("event", string(jsonBytes))

	// payload := bytes.NewBuffer(jsonBytes)
	req, err = http.NewRequest("POST", amplitudeHTTPURL, strings.NewReader(form.Encode()))

	if err != nil {
		// handle error
		log.Println("[ERROR]", err)
		return
	}

	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	client = &http.Client{Timeout: time.Duration(time.Second * 10)}
	resp, err = client.Do(req)

	if err != nil {
		// handle error
		log.Println("err:", err)
		return
	}

	defer resp.Body.Close()
	statuscode = resp.StatusCode
	body, _ = io.ReadAll(resp.Body)

	log.Println("[INFO] amplitude post:", string(jsonBytes))
	log.Println("[INFO] amplitude response body:", string(body))
	log.Println("[INFO] amplitude response status:", statuscode)

	if statuscode != 200 {
		log.Println("[ERROR] amplitue fail", event)
	}
}

// NewAccountTransactionCompleted create a new event
func NewAccountTransactionCompleted(userID string, triggerCondition string, price int, property EventProperties) (event *AccountTransactionCompleted, err error) {
	event = new(AccountTransactionCompleted)
	if userID == "" {
		return event, errors.New("not a userid")
	}

	// property := make(map[string]interface{})
	// property["log version"] = "0.7.0"
	// property["revenue"] = price
	// property["trigger condition"] = triggerCondition
	// property["order number"] = orderNumber
	property.LogVersion = "0.7.0"
	property.Revenue = price
	property.TriggerCondition = triggerCondition

	event.UserID = userID
	event.EventType = "Account Transaction Completed"
	event.Time = time.Now()
	event.Quantity = 1
	event.Revenue = price
	event.EventProperties = property

	return event, nil
}

// NewMODTransactionCompleted create a new event
func NewMODTransactionCompleted(triggerCondition string, order *dbuser.Order, product *dbuser.Product, paymentInfo dbuser.PaymentInfo) (*AccountTransactionCompleted, error) {
	var property EventProperties
	event := new(AccountTransactionCompleted)

	property.LogVersion = "0.7.0"
	property.Revenue = int(order.Price)
	property.PaymentType = order.PaymentType
	property.Plan = product.Name
	property.TriggerCondition = triggerCondition
	property.OrderNumber = order.ID

	if order.Price == 0 {
		days, _ := strconv.Atoi(product.Duration)
		property.GivenFreeVipDays = days
	}
	if paymentInfo.ModSubscriberID.Valid {
		property.AccountModSubscriberID = paymentInfo.ModSubscriberID.String
	}

	event.UserID = order.UserID
	event.EventType = "Account Transaction Completed"
	event.Time = time.Now()
	event.Price = int(order.Price)
	event.Quantity = 1
	event.Revenue = int(order.Price)
	event.EventProperties = property

	return event, nil
}
