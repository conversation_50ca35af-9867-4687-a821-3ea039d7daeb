package amplitude

// EventProperties struct for amplitude event
type EventProperties struct {
	LogVersion             string `json:"log_version,omitempty"`
	AccountUserID          string `json:"account user id,omitempty"`
	Revenue                int    `json:"revenue,omitempty"`
	TriggerCondition       string `json:"trigger condition,omitempty"`
	OrderNumber            string `json:"order number,omitempty"`
	Plan                   string `json:"plan,omitempty"`
	AccountTelecomBill     string `json:"account telecom bill,omitempty"`
	AccountModSubscriberID string `json:"account mod subscriber id,omitempty"`

	ObtainedVipDays    int    `json:"obtained vip days,omitempty"`
	ObtainedVipMonths  int    `json:"obtained vip months,omitempty"`
	GivenFreeVipDays   int    `json:"given free vip days,omitempty"`
	GivenFreeVipMonths int    `json:"given free vip months,omitempty"`
	PaymentType        string `json:"payment type,omitempty"`
	FamilyID           string `json:"family_id,omitempty"`
	Result             string `json:"result,omitempty"`
	Reason             string `json:"reason,omitempty"`
	QualificationProof string `json:"account qualification proof,omitempty"`
	_                  struct{}
}
