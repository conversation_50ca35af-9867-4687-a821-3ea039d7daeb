package facebook

import (
	"crypto/sha256"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/KKTV/kktv-api-v3/pkg/secret"
)

// Facebook Conversion API Document:
// * https://developers.facebook.com/docs/marketing-api/conversions-api/using-the-api

var (
	// facebook graph api version
	graphAPIVersion = "v13.0"
)

type ConversionEvents []ConversionEvent

func (list *ConversionEvents) Append(item ConversionEvent) {
	*list = append(*list, item)
}

type CustomDataContent struct {
	ID               string   `json:"id,omitempty"`
	Quantity         *int32   `json:"quantity,omitempty"`
	ItemPrice        *float32 `json:"item_price,omitempty"`
	DeliveryCategory string   `json:"delivery_category,omitempty"`
}

type UserData struct { // https://developers.facebook.com/docs/marketing-api/conversions-api/parameters/customer-information-parameters
	Email           string `json:"em,omitempty"`          // lowercase, sha256 required
	Phone           string `json:"ph,omitempty"`          // sha256 required
	FirstName       string `json:"fn,omitempty"`          // lowercase, encoding: utf8, sha256 required
	LastName        string `json:"ln,omitempty"`          // lowercase, encoding: utf8, sha256 required
	Birthday        string `json:"db,omitempty"`          // format: 19970216, sha256 required
	Gender          string `json:"ge,omitempty"`          // format: f/m, sha256 required
	City            string `json:"ct,omitempty"`          // lowercase, encoding: utf8, sha256 required
	State           string `json:"st,omitempty"`          // lowercase, sha256 required, https://en.wikipedia.org/wiki/Federal_Information_Processing_Standard_state_code
	Zip             string `json:"zp,omitempty"`          // lowercase, sha256 required
	Country         string `json:"country,omitempty"`     // lowercase, sha256 required, https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2
	ExternalID      string `json:"external_id,omitempty"` // lowercase, sha256 required
	ClientIPAddress string `json:"client_ip_address,omitempty"`
	ClientUserAgent string `json:"client_user_agent,omitempty"`
	Fbc             string `json:"fbc,omitempty"`
	Fbp             string `json:"fbp,omitempty"`
	SubscriptionID  string `json:"subscription_id,omitempty"`
	FacebookLoginID int64  `json:"fb_login_id,omitempty"`
	LeadID          int64  `json:"lead_id,omitempty"`
}

func (ud *UserData) Hash() {
	if ud.Email != "" {
		ud.Email = strings.ToLower(ud.Email)
		ud.Email = sha256Hash(ud.Email)
	}
	if ud.Phone != "" {
		if strings.HasPrefix(ud.Phone, "+886") {
			ud.Phone = strings.TrimPrefix(ud.Phone, "+")
		} else if strings.HasPrefix(ud.Phone, "09") {
			ud.Phone = "886" + strings.TrimPrefix(ud.Phone, "0")
		}
		ud.Phone = sha256Hash(ud.Phone)
	}
	if ud.FirstName != "" {
		ud.FirstName = sha256Hash(ud.FirstName)
	}
	if ud.LastName != "" {
		ud.LastName = sha256Hash(ud.LastName)
	}
	if ud.Birthday != "" {
		ud.Birthday = strings.ReplaceAll(ud.Birthday, "-", "")
		ud.Birthday = sha256Hash(ud.Birthday)
	}
	if ud.Gender != "" {
		ud.Gender = strings.ToLower(ud.Gender)
		if ud.Gender == "female" || ud.Gender == "male" {
			ud.Gender = ud.Gender[0:1]
		}
		ud.Gender = sha256Hash(ud.Gender)
	}
	if ud.City != "" {
		ud.City = sha256Hash(ud.City)
	}
	if ud.State != "" {
		ud.State = sha256Hash(ud.State)
	}
	if ud.Zip != "" {
		ud.Zip = sha256Hash(ud.Zip)
	}
	if ud.Country != "" {
		ud.Country = sha256Hash(ud.Country)
	}
	if ud.ExternalID != "" {
		ud.ExternalID = sha256Hash(ud.ExternalID)
	}
}

type CustomData struct { // https://developers.facebook.com/docs/marketing-api/conversions-api/parameters/custom-data
	ContentCategory  string              `json:"content_category,omitempty"`
	ContentIDs       []string            `json:"content_ids,omitempty"`
	ContentName      string              `json:"content_name,omitempty"`
	ContentType      string              `json:"content_type,omitempty"`
	Contents         []CustomDataContent `json:"contents,omitempty"`
	Currency         string              `json:"currency,omitempty"`
	DeliveryCategory string              `json:"delivery_category,omitempty"`
	NumItems         string              `json:"num_items,omitempty"`
	OrderID          string              `json:"order_id,omitempty"`
	PredictedLtv     float32             `json:"predicted_ltv,omitempty"`
	SearchString     string              `json:"search_string,omitempty"`
	Status           string              `json:"status,omitempty"`
	Value            *float32            `json:"value,omitempty"`
}

type ConversionEvent struct {
	EventName      string      `json:"event_name"`
	EventTime      int64       `json:"event_time"`
	EventID        string      `json:"event_id,omitempty"`
	EventSourceURL string      `json:"event_source_url,omitempty"`
	ActionSource   string      `json:"action_source,omitempty"`
	UserData       *UserData   `json:"user_data"`
	CustomData     *CustomData `json:"custom_data,omitempty"`
}

type ConversionEventOption func(url.Values)

func WithTestEventCode(testEventCode string) ConversionEventOption {
	return func(values url.Values) {
		values.Set("test_event_code", testEventCode)
	}
}

func sha256Hash(str string) string {
	return fmt.Sprintf("%x", sha256.Sum256([]byte(str)))
}

// SendConversionEvents send facebook conversion events
func SendConversionEvents(data ConversionEvents, opts ...ConversionEventOption) (err error) {
	var resp *http.Response
	defer func() {
		if err != nil {
			log.Println("[ERROR] Send Facebook conversion event, error: ", err)
		}
		if resp != nil {
			resp.Body.Close()
		}
	}()

	pixelID := secret.Values.FBPixelID
	conversionAPIAccessToken := secret.Values.FBConversionAPIToken

	if pixelID == "" || conversionAPIAccessToken == "" {
		err = errors.New("pixel_id or capi_access_token can't be empty")
		return
	}

	for _, event := range data {
		event.UserData.Hash()
	}

	form := url.Values{}
	// access_token
	form.Set("access_token", conversionAPIAccessToken)

	// data
	var jsonData []byte
	if jsonData, err = json.Marshal(data); err != nil {
		return
	}
	log.Println("[INFO] Send Facebook conversion event, data: ", string(jsonData))
	form.Set("data", string(jsonData))

	for _, opt := range opts {
		opt(form)
	}

	client := &http.Client{Timeout: time.Duration(time.Second * 20)}
	capiURL := fmt.Sprintf("https://graph.facebook.com/%s/%s/events", graphAPIVersion, pixelID)
	req, err := http.NewRequest("POST", capiURL, strings.NewReader(form.Encode()))
	if err != nil {
		return
	}
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	resp, err = client.Do(req)

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return
	}

	log.Println("[INFO] Send Facebook conversion event, body: ", string(body))

	return
}

const EventPurchase = "Purchase"

type PurchaseEventData struct {
	OrderNumber  string
	OrderPrice   int64
	ProductPrice int64
	ProductID    string
	ProductName  string
	Email        string
	Phone        string
	Gender       string
	Birthday     string
	Quantity     int64
}

func BuildPurchaseEvent(data PurchaseEventData) ConversionEvent {
	var (
		orderPrice         = float32(data.OrderPrice)
		productPrice       = float32(data.ProductPrice)
		quantity     int32 = 1
	)
	return ConversionEvent{
		EventName:      EventPurchase,
		EventTime:      time.Now().Unix(),
		EventID:        data.OrderNumber,
		EventSourceURL: "https://www.kktv.me",
		UserData: &UserData{
			Email:          data.Email,
			Phone:          data.Phone,
			Gender:         data.Gender,
			Birthday:       data.Birthday,
			Country:        "tw",
			SubscriptionID: data.OrderNumber,
		},
		CustomData: &CustomData{
			Currency:    "twd",
			ContentName: data.ProductName,
			Value:       &orderPrice,
			Contents: []CustomDataContent{
				{
					ID:        data.ProductID,
					Quantity:  &quantity,
					ItemPrice: &productPrice,
				},
			},
		},
	}
}
