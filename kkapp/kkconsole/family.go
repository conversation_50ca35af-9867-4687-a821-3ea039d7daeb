package kkconsole

import (
	"log"
	"net/http"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kkapp/model/kkfamily"
)

// GetConsoleFamily console api for query family
func GetConsoleFamily(w http.ResponseWriter, r *http.Request) {
	response := model.MakeOk()
	// user := r.Context().Value("user").(jwt.MapClaims)
	q := r.URL.Query().Get("q")
	defer func() {
		kkapp.App.Render.JSON(w, http.StatusOK, response)
	}()
	// response.Data = user
	log.Println("[INFO]", q)
	data := make(map[string]interface{})
	if q == "" {
		return
	}

	fm, err := kkfamily.NewFamilyWithOrderStatus(q)
	if err != nil {
		return
	}

	data["family"] = fm
	response.Data = data
}
