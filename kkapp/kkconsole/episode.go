package kkconsole

import (
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"net/http"
	"net/url"
	"path/filepath"
	"strings"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/kksearch"
	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/cloudfront"
	"github.com/go-zoo/bone"
)

var (
	sqlEpisode = map[string]string{
		"update": `UPDATE meta_episode SET meta = meta || $1 WHERE id = $2`,
		"setpub": `UPDATE meta_episode SET pub = $1, unpub = $2 WHERE id = $3;`,
	}
)

type modifyEpisode struct {
	EpisodeID     string          `json:"episode_id"`
	Dash          json.RawMessage `json:"dash"`
	DashUrisNOSub json.RawMessage `json:"dash_uris_nosub"`
	DashUrisSub   json.RawMessage `json:"dash_uris_sub"`
	Hls           json.RawMessage `json:"hls"`
	HlsUrisNOSub  json.RawMessage `json:"hls_uris_nosub"`
	HlsUrisSub    json.RawMessage `json:"hls_uris_sub"`
	Duration      float64         `json:"duration"`
	LicenseStart  int64           `json:"license_start"`
	LicenseEnd    int64           `json:"license_end"`
	Pub           int64           `json:"pub"`
	UnPub         int64           `json:"unpub"`
}

// PutConsoleEpisode console api for modifying episode and series
// for live episode
func PutConsoleEpisode(w http.ResponseWriter, r *http.Request) {
	var err error
	var payLoad modifyEpisode

	response := model.MakeOk()
	consoleUser := r.Context().Value("user").(dbmeta.ConsoleUser)

	defer func() {
		if err != nil {
			log.Println("[ERROR]", err)
			kkapp.App.Render.JSON(w, http.StatusBadRequest, response)
		} else {
			kkapp.App.Render.JSON(w, http.StatusOK, response)
		}
	}()

	jsdecoder := json.NewDecoder(r.Body)
	err = jsdecoder.Decode(&payLoad)

	if err != nil {
		return
	}
	log.Println(consoleUser)
	// live episode always give default duration
	payLoad.Duration = 1000.01
	jsBytes, err := json.Marshal(payLoad)

	if err != nil {
		return
	}

	db := kkapp.App.DbMeta.Master()
	_, err = db.Exec(sqlEpisode["update"], jsBytes, payLoad.EpisodeID)

	if err == nil {
		// update manifest at redis
		dbmeta.Manifest2Redis(payLoad.EpisodeID)
	}

}

// PutConsoleEpisodePublish console api for modifying episode pub && unpub
func PutConsoleEpisodePublish(w http.ResponseWriter, r *http.Request) {
	var err error
	var payLoad modifyEpisode

	response := model.MakeOk()
	consoleUser := r.Context().Value("user").(dbmeta.ConsoleUser)

	defer func() {
		if err != nil {
			log.Println("[ERROR]", err)
			kkapp.App.Render.JSON(w, http.StatusBadRequest, response)
		} else {
			kkapp.App.Render.JSON(w, http.StatusOK, response)
		}
	}()

	jsdecoder := json.NewDecoder(r.Body)
	err = jsdecoder.Decode(&payLoad)

	if err != nil {
		return
	}
	log.Println(consoleUser)
	//jsBytes, err := json.Marshal(payLoad)

	if err != nil {
		return
	}

	db := kkapp.App.DbMeta.Master()
	pub := time.Unix(payLoad.Pub, 0)
	unpub := time.Unix(payLoad.UnPub, 0)
	_, err = db.Exec(sqlEpisode["setpub"], pub, unpub, payLoad.EpisodeID)

	if err == nil && len(payLoad.EpisodeID) > 8 {
		// update title to redis
		dbmeta.Title2Redis(payLoad.EpisodeID[0:8])

		// update elasticsearch
		kksearch.SyncTitles([]string{payLoad.EpisodeID[0:8]})
	}
}

// PostConsoleEpisodeInvalidation create a AWS cloudfront invalidation
// for the thumbnails of episode
func PostConsoleEpisodeInvalidation(w http.ResponseWriter, r *http.Request) {
	var err error
	var episodeID string
	response := model.MakeOk()
	distributionID := "E2N1QPBAI1TKJZ"

	defer func() {
		if err != nil {
			response.Status.Type = "Error"
			response.Status.Message = err.Error()
			kkapp.App.Render.JSON(w, http.StatusInternalServerError, response)
		} else {
			kkapp.App.Render.JSON(w, http.StatusOK, response)
		}
	}()

	episodeID = bone.GetValue(r, "id")

	if episodeID == "" {
		err = errors.New("episode id can't be blank")
		return
	}

	episode, err := dbmeta.NewEpisodeByEpisodeID(episodeID)
	if err != nil {
		err = errors.New(err.Error())
		return
	}

	if episode.Meta.Mezzanines.Hls == nil {
		err = errors.New("the manefest file not ready yet")
		return
	}

	u, err := url.Parse(episode.Meta.Mezzanines.Hls.URI)
	if err != nil {
		errstr := fmt.Sprintf("[ERROR] the manefest file path %s", err)
		err = errors.New(errstr)
		return
	}

	// S3ThumbnailFolder
	// invalid path example: /76/04000176010005_ada2ca94b86ab1f85c1e5eead968f88b/ada2ca94b86ab1f85c1e5eead968f88b/*
	invalPath := ""
	dirs := filepath.Dir(u.Path)
	baseName := filepath.Base(filepath.Dir(u.Path))
	if strings.Contains(baseName, "_") {
		hashID := strings.Split(baseName, "_")[1]
		invalPath = fmt.Sprintf("%s/%s/*", dirs, hashID)
		log.Println("[INFO] cloudfront invalidation path", invalPath)
	} else {
		err = errors.New("[ERROR] no manefest file exists")
		return
	}

	// create invalidation
	now := time.Now()
	svc := cloudfront.New(session.New(), &aws.Config{Region: aws.String("ap-northeast-1")})
	resp, err := svc.CreateInvalidation(&cloudfront.CreateInvalidationInput{
		DistributionId: aws.String(distributionID),
		InvalidationBatch: &cloudfront.InvalidationBatch{
			CallerReference: aws.String(
				fmt.Sprintf("goinvali%s", now.Format("2006/01/02,15:04:05"))),
			Paths: &cloudfront.Paths{
				Quantity: aws.Int64(1),
				Items: []*string{
					aws.String(invalPath),
				},
			},
		},
	})

	if err != nil {
		errstr := fmt.Sprintf("[ERROR] create invalidatiton failed %s", err.Error())
		err = errors.New(errstr)
		return
	}

	log.Println("create invalidation success. response:", resp)
}
