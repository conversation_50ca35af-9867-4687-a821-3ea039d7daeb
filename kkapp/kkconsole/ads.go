package kkconsole

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"net/http"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3"
)

// GetConsoleAds console api
func GetConsoleAds(w http.ResponseWriter, r *http.Request) {
	response := model.MakeOk()
	ads, err := model.NewAllAds()
	if err != nil {
		log.Println("[ERROR]", err)
	} else {
		response.Data = ads
	}

	kkapp.App.Render.JSON(w, http.StatusOK, response)
}

// PutConsoleAds console api
func PutConsoleAds(w http.ResponseWriter, r *http.Request) {
	var err error
	response := model.MakeOk()
	ads := new(model.Ads)

	jsdecoder := json.NewDecoder(r.Body)
	err = jsdecoder.Decode(ads)

	if err != nil {
		log.Println("[ERROR]", err)
	}

	ads.Save()
	message := "ads saved"
	dbmeta.ConsoleLog(r, "meta", message)
	kkapp.App.Render.JSON(w, http.StatusOK, response)
}

// PostConsoleAdsImage console api to upload images for ads
func PostConsoleAdsImage(w http.ResponseWriter, r *http.Request) {
	response := model.MakeOk()
	adID := r.URL.Query().Get("id")

	renderError := func(message string) {
		response.Status.Type = "Error"
		response.Status.Message = message
		kkapp.App.Render.JSON(w, http.StatusBadRequest, response)
	}

	if adID == "" {
		// no adID
		renderError("No AdID")
		return
	}

	// 1 Mb
	r.Body = http.MaxBytesReader(w, r.Body, imageUploadLimit)
	file, handle, err := r.FormFile("file")
	if err != nil {
		renderError(err.Error())
		return
	}

	// should had file now
	defer file.Close()

	fileBytes, err := ioutil.ReadAll(file)
	if err != nil {
		renderError(err.Error())
		return
	}

	// only allow jpeg image
	mimeType := handle.Header.Get("Content-Type")
	if mimeType != "image/jpeg" {
		response.Status.Type = "Error"
		response.Status.Message = "The format file is not valid."
		kkapp.App.Render.JSON(w, http.StatusBadRequest, response)
		return
	}

	// ok ready to save to s3
	s3Key := fmt.Sprintf("ads/%s.jpeg", adID)
	s3client := s3.New(session.New(), &aws.Config{Region: aws.String("ap-northeast-1")})
	imagePath := fmt.Sprintf("%s/ads/%s.jpeg", kkapp.App.S3ImageHost, adID)

	params := &s3.PutObjectInput{
		Bucket:      aws.String(kkapp.App.S3ImageBucket), // required
		Key:         aws.String(s3Key),                   // required
		ACL:         aws.String("public-read"),
		Body:        bytes.NewReader(fileBytes),
		ContentType: aws.String(mimeType),
	}
	_, err = s3client.PutObject(params)
	if err != nil {
		log.Println("[ERROR]", err)
		renderError(err.Error())
		return
	}

	// write to  Ads Image
	ads, _ := model.NewAllAds()
	for idx := range ads.Ads {
		if ads.Ads[idx].ID == adID {
			// found AdsID
			ads.Ads[idx].ImageURL = imagePath
			break
		}
	}
	ads.Save()
	kkapp.App.Render.JSON(w, http.StatusOK, response)
}

// DelConsoleAdsImage console api
func DelConsoleAdsImage(w http.ResponseWriter, r *http.Request) {
	var err error
	response := model.MakeOk()
	adID := r.URL.Query().Get("id")

	renderError := func(message string) {
		response.Status.Type = "Error"
		response.Status.Message = message
		kkapp.App.Render.JSON(w, http.StatusBadRequest, response)
	}

	defer func() {
	}()

	s3Key := fmt.Sprintf("ads/%s.jpeg", adID)
	s3client := s3.New(session.New(), &aws.Config{Region: aws.String("ap-northeast-1")})

	params := &s3.DeleteObjectInput{
		Bucket: aws.String(kkapp.App.S3ImageBucket), // required
		Key:    aws.String(s3Key),                   // required
	}
	_, err = s3client.DeleteObject(params)
	if err != nil {
		renderError(err.Error())
		return
	}

	// write to ads Image
	ads, _ := model.NewAllAds()
	for idx := range ads.Ads {
		if ads.Ads[idx].ID == adID {
			// found AdsID
			ads.Ads[idx].ImageURL = ""
			break
		}
	}
	ads.Save()

	kkapp.App.Render.JSON(w, http.StatusOK, response)
}
