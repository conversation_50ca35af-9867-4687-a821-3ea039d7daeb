package kkconsole

import (
	"encoding/json"
	"log"
	"net/http"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
)

// GetConsoleAnnounce console api
func GetConsoleAnnounce(w http.ResponseWriter, r *http.Request) {
	response := model.MakeOk()

	defer func() {
		kkapp.App.Render.JSON(w, http.StatusOK, response)
	}()

	announce, err := model.NewAllAnnouncement()
	if err != nil {
		log.Println("[ERROR]", err)
	} else {
		response.Data = announce
	}
}

// PutConsoleAnnounce console api
func PutConsoleAnnounce(w http.ResponseWriter, r *http.Request) {
	var err error
	response := model.MakeOk()

	defer func() {
		kkapp.App.Render.JSON(w, http.StatusOK, response)
	}()

	announce := new(model.Announcement)

	jsdecoder := json.NewDecoder(r.Body)
	err = jsdecoder.Decode(announce)

	if err != nil {
		log.Println("[ERROR]", err)
		return
	}

	announce.Save()
	message := "saved announce"
	dbmeta.ConsoleLog(r, "meta", message)
}
