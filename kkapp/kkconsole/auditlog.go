package kkconsole

import (
	"net/http"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
)

// GetConsoleAuditLog console api for query auditlogs
func GetConsoleAuditLog(w http.ResponseWriter, r *http.Request) {
	response := model.MakeOk()
	defer func() {
		kkapp.App.Render.JSON(w, http.StatusOK, response)
	}()
	logs, _ := dbmeta.NewAuditLogList()
	data := make(map[string]interface{})
	data["logs"] = logs
	response.Data = data
}
