package kkconsole

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"net/http"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
	"github.com/KKTV/kktv-api-v3/kkapp/model/kkcontent"
)

// GetConsoleHotKeyWord console api
// just use the api for client
// GetHotkeyWord at /v3/hot_keywords

// PutConsoleHotKeyWord console api
func PutConsoleHotKeyWord(w http.ResponseWriter, r *http.Request) {
	response := model.MakeOk()

	defer func() {
		kkapp.App.Render.JSON(w, http.StatusOK, response)
	}()

	hotkeyword, err := kkcontent.NewHotKeyWord()

	if err != nil {
		log.Println("[ERROR]", err)
		return
	}

	defer r.Body.Close()

	body, err := ioutil.ReadAll(r.Body)
	if err != nil {
		log.Println("[ERROR]", err)
		return
	}
	log.Println(string(body))
	err = json.Unmarshal(body, hotkeyword)
	if err != nil {
		log.Println("[ERROR]", err)
		return
	}

	hotkeyword.Save()

	message := fmt.Sprintf("save hotkeyword %s", hotkeyword.Keywords)
	dbmeta.ConsoleLog(r, "meta", message)
	response.Data = hotkeyword
}
