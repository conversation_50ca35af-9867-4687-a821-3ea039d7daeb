package kkconsole

import (
	"log"
	"net/http"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kkapp/model/dbuser"
)

var (
	sqltoken = map[string]string{
		"get_tokens_by_user_id": `SELECT t.user_id, t.token, t.token_hash, t.source_ip, t.user_agent, t.created_at token_created, t.expired_at token_expired,
 r.id, r.token_id, r.refresh_token, r.created_at, r.expired_at, r.provider
 FROM tokens t, refresh_tokens r WHERE t.user_id = $1 AND
 t.id = r.token_id AND t.expired_at > Now() - interval '30 day' ORDER BY t.created_at DESC;`,

		"expire_token_by_id":   `UPDATE tokens SET expired_at = NOW(), updated_at = NOW() WHERE id = $1 AND expired_at > NOW();`,
		"expire_refresh_token": `UPDATE refresh_tokens SET expired_at = NOW() WHERE refresh_token = $1 AND expired_at > NOW();`,

		"expire_tokens_by_user_id": `UPDATE tokens SET expired_at = NOW(), updated_at = NOW() WHERE user_id = $1 and expired_at > NOW()`,
		"expire_refresh_tokens_by_user_id": `UPDATE refresh_tokens SET expired_at = NOW() WHERE token_id in (
									select id from
									tokens w
									where user_id = $1 and expired_at > NOW())`,
	}
)

// GetConsoleToken console api for query tokens
func GetConsoleToken(w http.ResponseWriter, r *http.Request) {
	response := model.MakeOk()
	q := r.URL.Query().Get("q")
	defer func() {
		kkapp.App.Render.JSON(w, http.StatusOK, response)
	}()
	// response.Data = user
	log.Println("[INFO]", q)
	db := kkapp.App.DbUser.Slave()
	var tokens []dbuser.RefreshToken
	if q != "" {
		err := db.Select(&tokens, sqltoken["get_tokens_by_user_id"], q)
		if err != nil {
			log.Println("[ERROR]", err)
		}
	}

	data := make(map[string]interface{})
	data["tokens"] = tokens
	response.Data = data
}

// PutConsoleToken console api for expire tokens
func PutConsoleToken(w http.ResponseWriter, r *http.Request) {
	var err error
	response := model.MakeOk()
	refreshToken := r.URL.Query().Get("refreshtoken")
	tokenID := r.URL.Query().Get("tokenid")

	defer func() {
		kkapp.App.Render.JSON(w, http.StatusOK, response)
	}()

	// response.Data = user
	log.Println("[INFO]", refreshToken, tokenID)
	db := kkapp.App.DbUser.Master()
	if refreshToken != "" && tokenID != "" {
		_, err = db.Exec(sqltoken["expire_refresh_token"], refreshToken)
		if err != nil {
			log.Println("[ERROR] set expire refresh token", err)
		}

		_, err = db.Exec(sqltoken["expire_token_by_id"], tokenID)
		if err != nil {
			log.Println("[ERROR] set expire token", err)
		}

	}
}

func PutConsoleDeviceAllToken(w http.ResponseWriter, r *http.Request) {
	var err error
	response := model.MakeOk()
	userID := r.URL.Query().Get("userID")
	db := kkapp.App.DbUser.Master()
	tx, err := db.Beginx()

	defer func() {
		if err != nil {
			tx.Rollback()
		}
		tx.Commit()
		log.Println("[INFO] set User's all device logout.")
		kkapp.App.Render.JSON(w, http.StatusOK, response)
	}()

	if userID != "" {
		_, err = db.Exec(sqltoken["expire_refresh_tokens_by_user_id"], userID)
		if err != nil {
			log.Println("[ERROR] set all expired refresh tokens", err)
		}

		_, err = db.Exec(sqltoken["expire_tokens_by_user_id"], userID)
		if err != nil {
			log.Println("[ERROR] set all tokens", err)
		}
	}
}
