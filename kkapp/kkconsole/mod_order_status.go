package kkconsole

import (
	"fmt"
	"net/http"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/kkpayment/mod"
	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/wrapper"
	"github.com/KKTV/kktv-api-v3/pkg/chtmod"
	"github.com/KKTV/kktv-api-v3/pkg/datetimer"
	"github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
)

// GetMODOrderState looking up MOD order state by MOD subscriber ID
func GetMODOrderState(w http.ResponseWriter, r *http.Request) {
	dbReader := kkapp.App.DbUser.Slave().Unsafe()
	response := model.MakeOk()
	md := r.URL.Query().Get("q")
	subscriberInfo := mod.SubscriberInfo{
		ID:           md,
		SubscriberID: md,
	}
	data := map[string]interface{}{
		"subscriberID":   md,
		"subscriberArea": "None",
		"error":          "",
	}

	var err error
	defer func() {
		if err != nil {
			data["error"] = err.Error()
		}
		data["subscriberInfo"] = subscriberInfo
		response.Data = data
		_ = kkapp.Render.JSON(w, http.StatusOK, response)
	}()

	var (
		paymentInfoSrv = wrapper.NewPaymentInfoService(dbReader)
		paymentInfo    *dbuser.PaymentInfo
		area           string
	)
	if paymentInfo, err = paymentInfoSrv.GetByMODSubscriberID(md); err != nil {
		log.Error("(console) GetMODOrderState: failed to get payment info by mod subscriber id").
			Str("mod_subscriber_id", md).
			Err(err).
			Send()
		return
	} else if paymentInfo == nil {
		log.Warn("(console) GetMODOrderState: payment info not found").
			Str("mod_subscriber_id", md).
			Err(err).
			Send()
		return
	} else if area = paymentInfo.ModSubscriberArea.String; area == "" {
		log.Warn("(console) GetMODOrderState: mod subscriber area is empty").
			Str("user_id", paymentInfo.UserID).
			Str("mod_subscriber_id", md).
			Err(err).
			Send()
		return
	}

	data["subscriberArea"] = area
	subscriberInfo.FoundArea = area
	subscriberInfo.UserID = paymentInfo.UserID

	var (
		modClient            = chtmod.NewClient()
		userSubscriptionInfo *chtmod.UserSubscriptionInfo
	)
	if userSubscriptionInfo, err = modClient.GetUserSubscriptionInfo(md, area); err != nil {
		log.Error("(console) GetMODOrderState: failed to get user subscription info").
			Str("user_id", paymentInfo.UserID).
			Str("mod_subscriber_id", md).
			Err(err).
			Send()
		return
	}

	var (
		externalProductID = chtmod.GetExternalProductID(userSubscriptionInfo.Order.ItemID, userSubscriptionInfo.Order.ItemType)
		productService    = wrapper.NewProductService(dbReader, nil)
		product           *dbuser.Product
	)
	if product, err = productService.GetByMODExternalProductID(externalProductID); err != nil {
		log.Error("(console) GetMODOrderState: failed to get product by external product id").
			Str("user_id", paymentInfo.UserID).
			Str("mod_subscriber_id", md).
			Str("external_product_id", externalProductID).
			Err(err).
			Send()
		return
	} else if product == nil {
		log.Warn("(console) GetMODOrderState: product not found").
			Str("user_id", paymentInfo.UserID).
			Str("mod_subscriber_id", md).
			Str("external_product_id", externalProductID).
			Send()
		return
	}

	subscriberInfo.ItemID = externalProductID
	subscriberInfo.ExtID = externalProductID
	subscriberInfo.ProductName = product.ItemName
	subscriberInfo.Price = fmt.Sprintf("%.1f", product.Price)
	subscriberInfo.MType = userSubscriptionInfo.Order.ItemType
	subscriberInfo.StartTime = time.Unix(userSubscriptionInfo.Order.StartTime, 0).In(datetimer.LocationTaipei).Format(time.DateTime)
}
