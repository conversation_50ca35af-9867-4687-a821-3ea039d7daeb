package kkconsole

import (
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
	"github.com/KKTV/kktv-api-v3/kkapp/model/dbredeem"
	"github.com/go-zoo/bone"
)

// GetConsoleRedeem console api for query redeem
func GetConsoleRedeem(w http.ResponseWriter, r *http.Request) {
	response := model.MakeOk()
	q := r.URL.Query().Get("q")
	defer func() {
		kkapp.App.Render.JSON(w, http.StatusOK, response)
	}()

	log.Println("[INFO]", q)

	coupons := dbredeem.NewRedeemBoard()

	data := make(map[string]interface{})
	data["coupons"] = coupons
	response.Data = data
}

// GetConsoleRedeemDetail console api for query redeem
func GetConsoleRedeemDetail(w http.ResponseWriter, r *http.Request) {
	var detail dbredeem.CouponGroupDetail
	response := model.MakeOk()
	q := r.URL.Query().Get("q")

	defer func() {
		kkapp.App.Render.JSON(w, http.StatusOK, response)
	}()

	if q == "" {
		return
	}

	_, err := strconv.Atoi(q)

	if err == nil {
		// q is groupID
		detail = dbredeem.NewCouponGroup(q, "")
	} else {
		// q is coupon code
		detail = dbredeem.NewCouponGroup("", q)
	}

	log.Println("[INFO]", q)

	// data := make(map[string]interface{})
	// data["coupons"] = coupons
	response.Data = detail
}

// ToggleConsoleRedeemCodeStatus console api for revoke/active coupon code status
func ToggleConsoleRedeemCodeStatus(w http.ResponseWriter, r *http.Request) {
	var err error
	response := model.MakeOk()

	defer func() {
		if err != nil {
			log.Println("[ERROR]", err)
			kkapp.App.Render.JSON(w, http.StatusBadRequest, response)
		} else {
			kkapp.App.Render.JSON(w, http.StatusOK, response)
		}
	}()

	payload := new(struct {
		Reason string `json:"reason"`
	})

	jsdecoder := json.NewDecoder(r.Body)
	err = jsdecoder.Decode(payload)

	if err != nil {
		log.Println("[ERROR]", err)
		return
	}

	redeemCodeID := bone.GetValue(r, "id")
	status := bone.GetValue(r, "status")

	switch status {
	case "revoke":
		err = dbredeem.RevokeCouponCode(redeemCodeID)
	case "active":
		err = dbredeem.ActiveCouponCode(redeemCodeID)
	default:
		err = errors.New("Invalid status")
	}

	if err != nil {
		log.Println("[ERROR]", err)
		response.Status.Message = err.Error()
		return
	}

	redeem, _ := dbredeem.GetCouponCode(redeemCodeID)
	message := fmt.Sprintf("%s coupon code, code: %s, group id: %d, reason: %s, updated at: %v",
		strings.Title(status),
		redeem.Code,
		redeem.GroupID,
		payload.Reason,
		redeem.UpdatedAt.Time.Format("2006-01-02T15:04:05"))
	dbmeta.ConsoleLog(r, "redeem", message)
}

// ToggleConsoleRedeemCodeStatusByGroup console api for revoke/active coupon codes in specific coupon group
func ToggleConsoleRedeemCodeStatusByGroup(w http.ResponseWriter, r *http.Request) {
	var err error
	response := model.MakeOk()

	defer func() {
		if err != nil {
			log.Println("[ERROR]", err)
			kkapp.App.Render.JSON(w, http.StatusBadRequest, response)
		} else {
			kkapp.App.Render.JSON(w, http.StatusOK, response)
		}
	}()

	payload := new(struct {
		Reason string `json:"reason"`
	})

	jsdecoder := json.NewDecoder(r.Body)
	err = jsdecoder.Decode(payload)

	if err != nil {
		log.Println("[ERROR]", err)
		return
	}

	groupID := bone.GetValue(r, "id")
	status := bone.GetValue(r, "status")

	switch status {
	case "revoke":
		err = dbredeem.RevokeCouponCodesByGroupID(groupID)
	case "active":
		err = dbredeem.ActiveCouponCodesByGroupID(groupID)
	default:
		err = errors.New("Invalid status")
	}

	if err != nil {
		log.Println("[ERROR]", err)
		response.Status.Message = err.Error()
		return
	}

	message := fmt.Sprintf("%s coupon codes by group, group id: %s, reason: %s, updated at: %v",
		strings.Title(status),
		groupID,
		payload.Reason,
		time.Now().UTC().Format("2006-01-02T15:04:05"))
	dbmeta.ConsoleLog(r, "redeem", message)

}
