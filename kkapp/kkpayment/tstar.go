package kkpayment

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strings"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/amplitude"
	"github.com/KKTV/kktv-api-v3/kkapp/model/dbuser"
	zlog "github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/payment"
	"github.com/jmoiron/sqlx"
	uuid "github.com/satori/go.uuid"
	"gopkg.in/guregu/null.v3"
)

var (
	response tstarResp

	dbMaster *sqlx.DB
	dbSlave  *sqlx.DB

	sqltstar = map[string]string{
		"productInfo":       "SELECT id, name, country, price, duration, created_at, updated_at, deleted_at, payment_type, currency, price_no_tax, tax_rate, item_name, item_unit, auto_renew, active, free_duration, as_subscribe, fee, payment_type_code, purchase_upper_limit_count, external_product_id FROM products WHERE payment_type='tstar' AND external_product_id=$1 AND active=true;",
		"newTstarUser":      "INSERT INTO users ( id, phone, role, auto_renew, type, created_by) VALUES ( $1, $2, 'expired', false, 'general', 'tstar')",
		"expiredDate":       "SELECT EXTRACT(EPOCH FROM expired_at)::integer FROM users WHERE id=$1",
		"updateOrder":       "UPDATE orders SET status = 'ok', info=$1, realized_at=$2 WHERE id=$3",
		"updateUser":        "UPDATE users SET role = 'premium', auto_renew=$1, expired_at=$2 WHERE id=$3",
		"upsertPaymentInfo": "INSERT INTO payment_info (payment_type, user_id, created_at, telecom_mp_id, tstar_order_id, tstar_contract_id) VALUES ('tstar', $1, $2, $3, $4, $5) ON CONFLICT (user_id) DO UPDATE SET (payment_type, created_at, telecom_mp_id, tstar_order_id, tstar_contract_id) = ('tstar', $6, $7, $8, $9) WHERE payment_info.user_id=$10",
		"paymentInfo": `SELECT user_id, email, phone, payment_type, credit_card_6no, credit_card_4no, credit_card_token_value, credit_card_token_term, iap_receipt_data, iap_receipt_data_hash,
									iap_latest_expires_date, iap_latest_transaction_id, caring_code, recipient, recipient_address, carrier_type, carrier_value,
									telecom_mp_id, tstar_order_id, tstar_contract_id, mod_subscriber_id, mod_subscriber_area, iab_receipt_data, iab_order_id,
									iab_latest_order_id, iab_latest_expires_date, created_at, updated_at, family_id
							from payment_info WHERE user_id=$1`,
		"getUser": `SELECT
						u.id, u.email, u.phone, u."name", u.expired_at,
						u.created_at, u.updated_at, u."role", u.media_source, u.auto_renew,
						u."type", u.created_by, p.payment_type AS pay_type, p.created_at AS pay_created, p.updated_at AS pay_updated,
						p.phone AS pay_phone, p.telecom_mp_id, p.tstar_order_id, p.tstar_contract_id, u.revoked_at
					FROM public.users AS u
					LEFT JOIN public.payment_info AS p ON p.user_id = u.id
					WHERE u.phone=$1
					AND u.revoked_at IS NULL
					ORDER BY u.phone;`,
		"getExistsOrderID": "SELECT COUNT(1) AS cnt FROM payment_info WHERE tstar_order_id=$1;",
		"getUnsubscribedOrder": `SELECT COUNT(1)
					         FROM unsubscribe
					         WHERE payment_type='tstar' AND (REPLACE((payment_info_snapshot->'tstarOrderId')::TEXT, '"', '')=$1) AND user_id=$2;`,
		"setCancelOrder":    "UPDATE orders SET status = 'cancel', canceled_at = NOW(), info = $1 WHERE payment_type = 'tstar' AND user_id = $2 AND status IS NULL AND price > 0::money",
		"insertUnsubscribe": "INSERT INTO unsubscribe (user_id, name, email, reason, payment_type, payment_info_snapshot) VALUES ($1, $2, $3, $4, $5, $6);",
		"setUserAutorenew":  "UPDATE users SET auto_renew=false, updated_at=NOW() WHERE id=$1 ;",
		"updateUserPhone":   "UPDATE users SET phone=$1 WHERE id=$2",
		"insertNewOrder": `INSERT INTO orders (id, user_id, product_id, price, payment_type, start_date, end_date, status, order_date, info, created_at, realized_at, price_no_tax, tax_rate, invoice, canceled_at,  fee, external_order_id)
		                               VALUES ($1,      $2,         $3,    $4,           $5,         $6,       $7,   NULL,         $8,   $9,        $10,        NULL,          $11,      $12,    NULL,        NULL,  $13,              $14);`,
	}
)

// Payload struct for request body
type Payload struct {
	RequestID string `json:"REQUEST_ID"`
	Param     param  `json:"PARAM"`
}

type param struct {
	MsISDN     string `json:"MSISDN"`
	Action     string `json:"ACTION"`
	SvcID      string `json:"SVC_ID"`
	OrderID    string `json:"ORDER_ID"`
	Orderdate  string `json:"ORDER_DATE,omitempty"`
	Modifydate string `json:"MODIFY_DATE"`
	ContractID string `json:"CONTRACT_ID"`
	IsFirst    string `json:"IS_FIRST,omitempty"`
	Discount   string `json:"SPEC_DIS,omitempty"`
	MsIsdnOld  string `json:"MSISDN_OLD,omitempty"`
	MsIsdnNew  string `json:"MSISDN_NEW,omitempty"`
}

// tstarResp struct for response
type tstarResp struct {
	RequestID  string      `json:"REQUEST_ID"`
	ResultCode string      `json:"RESULT_CODE"`
	ResultMsg  string      `json:"RESULT_MSG"`
	ResultData interface{} `json:"RESULT_DATA"`
}

type userInfo struct {
	ID              string      `db:"id" json:"id"`
	Email           null.String `db:"email" json:"email"`
	Phone           null.String `db:"phone" json:"phone"`
	Name            null.String `db:"name" json:"name"`
	ExpiredAt       null.Time   `db:"expired_at" json:"expiredAt"`
	CreatedAt       null.Time   `db:"created_at" json:"createdAt"`
	UpdatedAt       null.Time   `db:"updated_at" json:"updatedAt"`
	Role            string      `db:"role" json:"role"`
	MediaSource     null.String `db:"media_source" json:"mediaSource"`
	AutoRenew       bool        `db:"auto_renew" json:"autoRenew"`
	Type            string      `db:"type" json:"type"`
	CreatedBy       null.String `db:"created_by" json:"createdBy"`
	PayType         null.String `db:"pay_type" json:"pay_type,omitempty"`
	PayCreated      null.Time   `db:"pay_created" json:"pay_created,omitempty"`
	PayUpdatedAt    null.Time   `db:"pay_updated" json:"paymentUpdatedAt"`
	PayPhone        null.String `db:"pay_phone" json:"payPhone"`
	TelecomMpID     null.String `db:"telecom_mp_id" json:"telcomID"`
	TstarOrderID    null.String `db:"tstar_order_id" json:"tstarOrderID"`
	TstarContractID null.String `db:"tstar_contract_id" json:"tstarContractID"`
	RevokedAt       null.Time   `db:"revoked_at" json:"revoked_at"`
}

// structs for payment_info_snapshot field of unsubscribe table
type tstarPaymentInfo struct {
	Email                  string `json:"email"`
	Phone                  string `json:"phone"`
	UserID                 string `json:"userId"`
	Recipient              string `json:"recipient"`
	CaringCode             string `json:"caringCode"`
	CarrierType            string `json:"carrierType"`
	PaymentType            string `json:"paymentType"`
	TelecomMpID            string `json:"telecomMpId"`
	CarrierValue           string `json:"carrierValue"`
	TstarOrderID           string `json:"tstarOrderId"`
	CreditCard4no          string `json:"creditCard4no"`
	CreditCard6no          string `json:"creditCard6no"`
	IapReceiptData         string `json:"iapReceiptData"`
	ModSubscriberID        string `json:"modSubscriberId"`
	TstarContractID        string `json:"tstarContractId"`
	RecipientAddress       string `json:"recipientAddress"`
	ModSubscriberArea      string `json:"modSubscriberArea"`
	IapReceiptDataHash     string `json:"iapReceiptDataHash"`
	CreditCardTokenTerm    string `json:"creditCardTokenTerm"`
	CreditCardTokenValue   string `json:"creditCardTokenValue"`
	IapLatestTransactionID string `json:"iapLatestTransactionId"`
}

func makeOK(id string) (resp tstarResp) {
	resp.RequestID = id
	resp.ResultCode = "000"
	resp.ResultMsg = "作業成功"
	resp.ResultData = ""
	return
}

func checkPhoneNumber(w http.ResponseWriter, p *Payload) bool {
	if p.Param.MsISDN == "" || NormalizePhoneNumber(p.Param.MsISDN) == "" {
		response.ResultCode = "900"
		errMsg := fmt.Sprintf("錯誤的電話號碼: %s", p.Param.MsISDN)
		response.ResultMsg = errMsg
		kkapp.App.Render.JSON(w, http.StatusOK, response)
		return false
	}
	return true
}

func checkOldPhoneNewPhone(w http.ResponseWriter, p *Payload) bool {
	if (p.Param.MsIsdnNew == "" || NormalizePhoneNumber(p.Param.MsIsdnNew) == "") || (p.Param.MsIsdnOld == "" || NormalizePhoneNumber(p.Param.MsIsdnOld) == "") {
		response.ResultCode = "900"
		errMsg := fmt.Sprintf("錯誤的電話號碼: %s", p.Param.MsIsdnNew)
		response.ResultMsg = errMsg
		kkapp.App.Render.JSON(w, http.StatusOK, response)
		return false
	}
	return true
}

func checkOrder(w http.ResponseWriter, p *Payload) bool {
	if p.Param.OrderID == "" {
		response.ResultCode = "900"
		response.ResultMsg = "缺少 ORDER_ID"
		kkapp.App.Render.JSON(w, http.StatusOK, response)
		return false
	}
	return true
}

func checkProduct(w http.ResponseWriter, p *Payload) bool {
	if p.Param.SvcID == "" {
		response.ResultCode = "904"
		errMsg := fmt.Sprintf("不正確的 SVC_ID: %s", p.Param.SvcID)
		response.ResultMsg = errMsg
		kkapp.App.Render.JSON(w, http.StatusOK, response)
		return false
	}

	svcID := p.Param.SvcID
	if p.Param.Discount == "1" {
		svcID = fmt.Sprintf("%s.discount", svcID)
	}
	product, err := GetProductInfo(svcID)
	if err != nil {
		response.ResultCode = "904"
		errMsg := fmt.Sprintf("不正確的 SVC_ID: %s", p.Param.SvcID)
		response.ResultMsg = errMsg
		kkapp.App.Render.JSON(w, http.StatusOK, response)
		return false
	}

	if product.Name == "" {
		response.ResultCode = "904"
		errMsg := fmt.Sprintf("不正確的 SVC_ID: %s", svcID)
		response.ResultMsg = errMsg
		kkapp.App.Render.JSON(w, http.StatusOK, response)
		return false
	}
	return true
}

// PostTSTAR handling POST data from TSTAR
func PostTSTAR(w http.ResponseWriter, r *http.Request) {
	var err error
	var payload Payload

	decoder := json.NewDecoder(r.Body)
	if err = decoder.Decode(&payload); err != nil {
		log.Println("[ERROR] - TSTAR - Decode json payload failed.", err)
		return
	}

	// Prepare response
	response = makeOK(payload.RequestID)

	zlog.Info("tstar api").Interface("payload", payload).Send()

	// Dealing with service
	switch payload.Param.Action {
	case "createservice":
		Subscribe(w, &payload)

	case "cancelservice":
		Unsubscribe(w, &payload)

	case "changemsisdn":
		ChangeMSISDN(w, &payload)

	case "resendmsg":
		ResendSMS(w, &payload)

	case "suspended":
		Dummy(w, &payload)

	case "restartservice":
		Dummy(w, &payload)

	case "changeorder":
		NotSupport(w, &payload)

	default:
		NotSupport(w, &payload)
	}

}

// Subscribe handler for dealing with subscribe task
func Subscribe(w http.ResponseWriter, p *Payload) {

	ok := checkPhoneNumber(w, p)
	if !ok {
		return
	}

	ok = checkOrder(w, p)
	if !ok {
		return
	}

	ok = checkProduct(w, p)
	if !ok {
		return
	}

	// Get Product
	svcID := p.Param.SvcID
	if p.Param.Discount == "1" {
		svcID = fmt.Sprintf("%s.discount", svcID)
	}
	product, err := GetProductInfo(svcID)
	if err != nil {
		response.ResultCode = "904"
		errMsg := fmt.Sprintf("不正確的 SVC_ID: %s", p.Param.SvcID)
		response.ResultMsg = errMsg
		kkapp.App.Render.JSON(w, http.StatusOK, response)
		return
	}

	phone := NormalizePhoneNumber(p.Param.MsISDN)
	orderID := p.Param.OrderID
	user := getUserbyPhone(phone)
	zlog.Info("tstar subscribe").Interface("user", user).Send()
	// User exists
	if user.ID != "" {
		// Reject the request if the user already subscribed to another payment
		// invoke decider
		var deciderParams map[string]string = map[string]string{}
		deciderParams["payment_type"] = "tstar"
		deciderParams["identifier"] = user.ID
		deciderParams["product_identifier"] = product.Name
		deciderParams["phone"] = phone
		if detail, result := payment.Decide(deciderParams); result == payment.PAYMENT_CONFLICTS_ERROR || result == payment.USER_ALREADY_SUBSCRIBED_ERROR {
			zlog.Error("Payment conflict").Fields(detail).Send()
			response.ResultCode = "910"
			response.ResultMsg = "電話已在 KKTV 申租其他方案，請向用戶確認"
			kkapp.App.Render.JSON(w, http.StatusOK, response)
			return
		} else if result == payment.REQUEST_ERROR {
			zlog.Error("Decider request error").Fields(deciderParams).Send()
		} else if result == payment.INTERNAL_SERVER_ERROR {
			zlog.Error("Decider internal server error").Send()
		}
	}

	// Check payment_type
	got := getExistsOrderID(orderID)
	if got {
		response.ResultCode = "908"
		errMsg := fmt.Sprintf("重複申裝，電話號碼: %s 、訂單： %s", phone, orderID)
		response.ResultMsg = errMsg
		kkapp.App.Render.JSON(w, http.StatusOK, response)
		return
	}

	// Create Order
	err = createOrder(w, p, &product)
	if err != nil {
		return
	}

	// Send SMS message by calling TSTAR API
	pn := strings.Replace(phone, "+886", "0", -1)
	smsMessage := GetSubscriptionMessage(pn, p.Param.IsFirst, p.Param.Discount, product.Name)
	go sendSMS2User(smsMessage, p)

	// Done and response
	response.ResultCode = "000"
	response.ResultMsg = "作業成功"
	kkapp.App.Render.JSON(w, http.StatusOK, response)

}

func createOrder(w http.ResponseWriter, p *Payload, product *dbuser.Product) error {
	// Get or Create User by Phone
	var newUserID string
	var isFirst bool

	phone := NormalizePhoneNumber(p.Param.MsISDN)
	user := getUserbyPhone(phone)

	// 0:YES(首次申裝) 1:NO(非首次申裝)
	// Ref. VASCP加值服務API20180605.docx
	isFirst = false
	if p.Param.IsFirst == "0" {
		isFirst = true
	}

	if user.ID == "" {

		// New user ID
		newUserID = strings.Replace(uuid.Must(uuid.NewV4()).String(), "-", "", -1)

		// Create user
		dbMaster = kkapp.App.DbUser.Master()
		_, err := dbMaster.Exec(sqltstar["newTstarUser"], newUserID, phone)
		if err != nil {
			log.Println("[ERROR] - TSTAR - Create new TSTAR user failed. ", err)
			response.ResultCode = "999"
			response.ResultMsg = "系統錯誤 － 無法新增使用者"
			kkapp.App.Render.JSON(w, http.StatusOK, response)
			return err
		}

		// Send amplitude events
		// FIXME use axios for tstar user signup
		r, _ := http.NewRequest("GET", "/", nil)
		r.Header.Set("User-Agent", "axios/0.18.1 ")

		event, err := amplitude.NewAccountSignup(newUserID, "free trial", "", "", phone, "tstar", r)
		if err != nil {
			log.Println("[ERROR] - TSTAR - Send event of create new TSTAR user failed. ", err)
		}
		go event.Send()

		// Assign new user ID for userID
		user.ID = newUserID

	}

	// Build temp orders
	tempOrders := BuildTempOrders(user.ID, product, isFirst)

	// Calculate Start Date
	userExpiredDate, err := GetUserExpiredDate(user.ID)
	if err != nil {
		log.Println("GetUserExpiredDate Failed.", err)
	}
	initStartDate := CalcuStartDate(userExpiredDate)

	fmt.Println("UserID:", user.ID, " ExpiredDate is:", userExpiredDate, " init StartDate is:", initStartDate)

	// Build final orders
	orders := BuildOrders(tempOrders, initStartDate)
	err = saveOrdersToDatabase(orders, p, product)
	// Save Orders to databases
	if err != nil {
		log.Println("[Error] - Tstar - save orders to database failed. ", err)
		response.ResultCode = "999"
		response.ResultMsg = "系統錯誤 － 無法儲存訂單"
		kkapp.App.Render.JSON(w, http.StatusOK, response)
		return err
	}

	// Send Amplitude event by triggerCondition, order, product, paymentInfo
	if err != nil {
		log.Println("Oops! PaymentInfo should not be nil!", err)
		response.ResultCode = "999"
		response.ResultMsg = "系統錯誤 － Unable to update payment info."
		kkapp.App.Render.JSON(w, http.StatusOK, response)
		return err
	}

	// success, send amplitude event
	var ep amplitude.EventProperties
	month, day, err := ParseDuration(product.Duration)
	currentOrder := orders[0]
	ep = amplitude.EventProperties{
		OrderNumber: currentOrder.ID,
		Plan:        product.Name,
		PaymentType: "tstar",
	}

	if currentOrder.Price > 0 {
		if day > 0 {
			ep.ObtainedVipDays = day
		}
		if month > 0 {
			ep.ObtainedVipMonths = month
		}

	} else {
		if day > 0 {
			ep.GivenFreeVipDays = day
		}
		if month > 0 {
			ep.GivenFreeVipMonths = month
		}
	}

	event, _ := amplitude.NewAccountTransactionCompleted(user.ID, "upgraded", int(currentOrder.Price), ep)
	log.Println("[INFO] amplitude event", user.ID, currentOrder)
	event.Send()
	return err
}

// Unsubscribe handler for dealing with unsubscribe task
func Unsubscribe(w http.ResponseWriter, p *Payload) {

	ok := checkPhoneNumber(w, p)
	if !ok {
		return
	}

	ok = checkOrder(w, p)
	if !ok {
		return
	}

	ok = checkProduct(w, p)
	if !ok {
		return
	}

	phone := NormalizePhoneNumber(p.Param.MsISDN)
	orderID := p.Param.OrderID

	// Check user exists or not
	user := getUserbyPhone(phone)
	if user.ID == "" {
		response.ResultCode = "900"
		errMsg := fmt.Sprintf("使用者不存在，電話號碼: %s", phone)
		response.ResultMsg = errMsg
		kkapp.App.Render.JSON(w, http.StatusOK, response)
		return
	}

	// Check payment type, should be tstar
	if user.PayType.String != "tstar" {
		response.ResultCode = "900"
		errMsg := fmt.Sprintf("使用者訂單資訊不正確 (payment_info)，無法取消，電話號碼: %s", phone)
		response.ResultMsg = errMsg
		kkapp.App.Render.JSON(w, http.StatusOK, response)
		return
	}

	// Check order is unsubscribed already or not
	exists, err := checkUnsubscribedOrder(p, user)
	if exists {
		response.ResultCode = "909"
		errMsg := fmt.Sprintf("重複申退，電話號碼: %s 、訂單： %s", phone, orderID)
		response.ResultMsg = errMsg
		kkapp.App.Render.JSON(w, http.StatusOK, response)
		return
	}

	log.Printf("Phone number: %s, Product ID: %s, Tstar Order ID: %s", phone, p.Param.SvcID, orderID)

	err = cancelOrder(w, p, user)
	if err != nil {
		response.ResultCode = "999"
		response.ResultMsg = "系統錯誤 － 無法取消訂單 " + err.Error()
		kkapp.App.Render.JSON(w, http.StatusOK, response)
		return
	}

	// Done and response
	response.ResultCode = "000"
	response.ResultMsg = "作業成功"
	kkapp.App.Render.JSON(w, http.StatusOK, response)
}

func saveOrdersToDatabase(orders []*dbuser.Order, payload *Payload, product *dbuser.Product) (err error) {

	db := kkapp.App.DbUser.Master()
	tx, err := db.Begin()
	if err != nil {
		log.Println("[ERROR] Begin transaction", err)
		return
	}

	// Dealing with orders
	for idx, order := range orders {
		var strSnapshot sql.NullString
		var externalOrderID sql.NullString
		if idx == 0 {
			// Generate a snapshot and save it into orders table for debuging, tracing in the future
			infoSnapshot, err := json.Marshal(payload.Param)
			if err == nil {
				strSnapshot = sql.NullString{
					String: string(infoSnapshot),
					Valid:  true,
				}
			}

			externalOrderID = sql.NullString{
				String: payload.Param.OrderID,
				Valid:  true,
			}
		}

		// Insert new order
		_, err = tx.Exec(sqltstar["insertNewOrder"], order.ID, order.UserID, order.ProductID, order.Price, order.PaymentType, order.StartDate, order.EndDate, order.OrderDate, strSnapshot, "now()", order.PriceNoTax, order.TaxRate, order.Fee, externalOrderID)
		if err != nil {
			log.Println("[ERROR] - Tstar - Create new order", err)
			tx.Rollback()
			return err
		}

		// Realize first order
		realizeAt := time.Now()
		if idx == 0 {
			_, err = tx.Exec(sqltstar["updateOrder"], order.Info, realizeAt, order.ID)
			if err != nil {
				log.Println("[ERROR] - Tstar - Realize order failed.", err)
				tx.Rollback()
				return err
			}
		}

	}

	// Update user's expired_date, role, autorenew,
	lastOrder := orders[len(orders)-1]
	expiredAt := lastOrder.StartDate
	autoRenew := product.AutoRenew

	zlog.Info("tstar api: update user").
		Bool("auto_renew", autoRenew).
		Time("expired_at", expiredAt).
		Str("user_id", lastOrder.UserID).Send()

	_, err = tx.Exec(sqltstar["updateUser"], autoRenew, expiredAt, lastOrder.UserID)
	if err != nil {
		log.Println("[ERROR] - Tstar - Update user's role, autorenew, expiredAt failed.", err)
		tx.Rollback()
		return err
	}

	// Create or Update paymentinfo
	//   if product.AutoRenew then TelecomMpID is CYC_TSTAR, else TSTAR
	//   assign ORDER_ID, CONTRACT_ID (Ref. kktv-service-order transaction_tstar.go Line: 29~35)
	telecomMpID := ""
	if autoRenew {
		telecomMpID = "CYC_TSTAR"
	} else {
		telecomMpID = "TSTAR"
	}

	createAt := time.Now()
	// SQL for Ref: INSERT INTO payment_info (payment_type, user_id, created_at, telecom_mp_id, tstar_order_id, tstar_contract_id)
	//      VALUES ('tstar', $1, $2, $3, $4, $5) ON CONFLICT (user_id)
	//      DO UPDATE SET (payment_type, created_at, telecom_mp_id, tstar_order_id, tstar_contract_id)
	//      = ('tstar', $6, $7, $8, $9) WHERE payment_info.user_id = $10",
	tOrderID := payload.Param.OrderID
	tContractID := payload.Param.ContractID
	_, err = tx.Exec(sqltstar["upsertPaymentInfo"], lastOrder.UserID, createAt, telecomMpID, tOrderID, tContractID, createAt, telecomMpID, tOrderID, tContractID, lastOrder.UserID)
	if err != nil {
		log.Println("[ERROR] - Tstar - Update paymentInfo failed.", err)
		tx.Rollback()
		return err
	}

	// Finally, commit it.
	if err := tx.Commit(); err != nil {
		return err
	}

	return nil
}

func checkUnsubscribedOrder(p *Payload, user userInfo) (bool, error) {
	var orderCnt int
	dbSlave = kkapp.App.DbUser.Slave()
	err := dbSlave.Get(&orderCnt, sqltstar["getUnsubscribedOrder"], p.Param.OrderID, user.ID)
	if err != nil {
		log.Println("[ERROR] - TSTAR - checkUnsubscribedOrder Failed.", err)
		return false, err
	}

	if orderCnt > 0 {
		return true, err
	}

	return false, err
}

func cancelOrder(w http.ResponseWriter, p *Payload, user userInfo) (err error) {

	db := kkapp.App.DbUser.Master()
	tx, err := db.Begin()
	if err != nil {
		log.Println("[ERROR] - Tstar - cancelOrder - Begin transaction", err)
		return err
	}

	// Generate a snapshot and save it into orders table for debuging, tracing in the future
	infoSnapshot, _ := json.Marshal(p.Param)
	strSnapshot := string(infoSnapshot)

	// Update auto_renew and expired_at field of users table
	_, err = tx.Exec(sqltstar["setUserAutorenew"], user.ID)
	if err != nil {
		log.Println("[ERROR] - Tstar - cancelOrder - setUserAutorenew", err)
		tx.Rollback()
		return err
	}

	// Update status field
	_, err = tx.Exec(sqltstar["setCancelOrder"], strSnapshot, user.ID)
	if err != nil {
		log.Println("[ERROR] - Tstar - cancelOrder - setCancelOrder", err)
		tx.Rollback()
		return err
	}

	// Generate a struct that matching original JSON style and save it into unsubscribe table
	paymentInfoSnapshot := new(tstarPaymentInfo)
	paymentInfoSnapshot.UserID = user.ID
	paymentInfoSnapshot.PaymentType = "tstar"
	paymentInfoSnapshot.TelecomMpID = user.TelecomMpID.String
	paymentInfoSnapshot.TstarOrderID = user.TstarOrderID.String
	paymentInfoSnapshot.TstarContractID = user.TstarContractID.String

	tstarInfo, _ := json.Marshal(paymentInfoSnapshot)
	strTstarInfo := string(tstarInfo)

	// Insert unsubscribe record into unsubscribe table
	_, err = tx.Exec(sqltstar["insertUnsubscribe"], user.ID, user.Name, user.Email, "By TSTAR API (\"cancelservice\")", "tstar", strTstarInfo)
	if err != nil {
		log.Println("[ERROR] - Tstar - cancelOrder - insertUnsubscribe", err)
		tx.Rollback()
		return err
	}

	// Finally, commit it.
	if err := tx.Commit(); err != nil {
		log.Println("[ERROR] - Tstar - cancelOrder - transaction commit", err)
		return err
	}

	// Send cancelled event to Amplitude
	tstarEvent, err := amplitude.NewTstarTransactionCancelled("By TSTAR API (\"cancelservice\")", user.ID)
	if err != nil {
		log.Println("Generate cancelled event to Amplitude failed.", err)
	}
	tstarEvent.Send()

	return
}

// Dummy handler for whatever tstar sends to here
func Dummy(w http.ResponseWriter, p *Payload) {
	response.ResultCode = "000"
	response.ResultMsg = "作業成功"
	response.ResultData = ""
	kkapp.App.Render.JSON(w, http.StatusOK, response)
}

// ChangeMSISDN handler for Tstar to change user's phone number
func ChangeMSISDN(w http.ResponseWriter, p *Payload) {

	ok := checkOldPhoneNewPhone(w, p)
	if !ok {
		return
	}

	ok = checkOrder(w, p)
	if !ok {
		return
	}

	ok = checkProduct(w, p)
	if !ok {
		return
	}

	phoneNew := NormalizePhoneNumber(p.Param.MsIsdnNew)
	phoneOld := NormalizePhoneNumber(p.Param.MsIsdnOld)

	// Check user exists or not by phoneOld
	user := getUserbyPhone(phoneOld)
	if user.ID == "" {
		response.ResultCode = "900"
		errMsg := fmt.Sprintf("使用者不存在，電話號碼: %s", phoneOld)
		response.ResultMsg = errMsg
		kkapp.App.Render.JSON(w, http.StatusOK, response)
		return
	}

	// Check user exists or not by phoneNew
	tempNew := getUserbyPhone(phoneNew)
	if tempNew.ID != "" {
		response.ResultCode = "910"
		errMsg := fmt.Sprintf("新電話已在 KKTV 註冊: %s", phoneNew)
		response.ResultMsg = errMsg
		kkapp.App.Render.JSON(w, http.StatusOK, response)
		return
	}

	dbMaster = kkapp.App.DbUser.Master()
	_, err := dbMaster.Exec(sqltstar["updateUserPhone"], phoneNew, user.ID)
	if err != nil {
		log.Printf("\n[INFO] - TSTAR - Change MSISDN - 系統錯誤 － 無法更新電話")
		response.ResultCode = "999"
		response.ResultMsg = "系統錯誤 － 無法更新電話 " + err.Error()
		kkapp.App.Render.JSON(w, http.StatusOK, response)
		return
	}

	response.ResultCode = "000"
	response.ResultMsg = "作業成功"
	response.ResultData = ""
	kkapp.App.Render.JSON(w, http.StatusOK, response)
}

// NotSupport handler for telling Tstar not support task
func NotSupport(w http.ResponseWriter, p *Payload) {
	// Prepare response
	response.ResultCode = "999"
	response.ResultMsg = "不支援此服務"
	response.ResultData = ""
	kkapp.App.Render.JSON(w, http.StatusOK, response)

	return
}

// ResendSMS handler for Tstar to resend SMS message
func ResendSMS(w http.ResponseWriter, p *Payload) {

	ok := checkPhoneNumber(w, p)
	if !ok {
		return
	}

	// Get Product
	product, err := GetProductInfo(p.Param.SvcID)
	if err != nil {
		response.ResultCode = "904"
		errMsg := fmt.Sprintf("不正確的 SVC_ID: %s", p.Param.SvcID)
		response.ResultMsg = errMsg
		kkapp.App.Render.JSON(w, http.StatusOK, response)
		return
	}

	phone := NormalizePhoneNumber(p.Param.MsISDN)
	user := getUserbyPhone(phone)

	if user.ID == "" {
		response.ResultCode = "900"
		errMsg := fmt.Sprintf("使用者不存在，電話號碼: %s", phone)
		response.ResultMsg = errMsg
		kkapp.App.Render.JSON(w, http.StatusOK, response)
		return
	}

	pn := strings.Replace(phone, "+886", "0", -1)
	smsMessage := GetSubscriptionMessage(pn, p.Param.IsFirst, p.Param.Discount, product.Name)
	go sendSMS2User(smsMessage, p)

	kkapp.App.Render.JSON(w, http.StatusOK, response)
}
