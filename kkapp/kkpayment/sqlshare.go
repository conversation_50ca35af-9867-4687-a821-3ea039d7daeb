package kkpayment

var (
	// share sql query
	sqlshare = map[string]string{

		"extend": `UPDATE orders SET order_date = NOW() + interval '2 day' WHERE status IS NULL and id = $1;`,

		"cancel": `UPDATE orders SET status = 'cancel', canceled_at = NOW() WHERE status IS NULL AND user_id = $1 AND payment_type = $2;`,

		"order": `SELECT id, user_id, product_id, price::numeric::int, payment_type,
 start_date, end_date, order_date,
 status, created_at, realized_at, canceled_at,
 price_no_tax::numeric::int, tax_rate, invoice, fee, external_order_id, info FROM orders WHERE
 user_id = $1 AND payment_type = $2 AND
 order_date > Date(NOW()) - interval '1 day' AND status IS NULL ORDER BY order_date LIMIT 1;`,

		"product": `SELECT id, name, duration, free_duration, price::numeric::int, price_no_tax::numeric::int, fee, tax_rate, payment_type, payment_type_code,
 auto_renew, as_subscribe, external_product_id, discount_price::numeric::int, discount_price_no_tax::numeric::int, discount_fee, discount_duration from products WHERE name = $1 AND payment_type = $2;`,

		"upsertpayment": `INSERT INTO payment_info (
 user_id,
 email,
 phone,
 credit_card_6no,
 credit_card_4no,
 credit_card_token_value,
 credit_card_token_term,
 iap_receipt_data,
 iap_receipt_data_hash,
 iap_latest_transaction_id,
 iap_latest_expires_date,
 payment_type,
 caring_code,
 recipient,
 recipient_address,
 carrier_type,
 carrier_value,
 telecom_mp_id,
 mod_subscriber_id,
 mod_subscriber_area,
 iab_receipt_data,
 iab_order_id,
 iab_latest_order_id,
 iab_latest_expires_date,
 created_at,
 updated_at,
 family_id
 ) VALUES (
	:user_id,
	:email,
	:phone,
	:credit_card_6no,
	:credit_card_4no,
	:credit_card_token_value,
	:credit_card_token_term,
	:iap_receipt_data,
	:iap_receipt_data_hash,
	:iap_latest_transaction_id,
	:iap_latest_expires_date,
	:payment_type,
	:caring_code,
	:recipient,
	:recipient_address,
	:carrier_type,
	:carrier_value,
	:telecom_mp_id,
	:mod_subscriber_id,
	:mod_subscriber_area,
	:iab_receipt_data,
	:iab_order_id,
	:iab_latest_order_id,
	:iab_latest_expires_date,
	NOW(),
	NOW(),
	:family_id
 )
 ON CONFLICT (user_id) DO UPDATE SET
 email = :email,
 phone = :phone,
 credit_card_6no = :credit_card_6no,
 credit_card_4no = :credit_card_4no,
 credit_card_token_value = :credit_card_token_value,
 credit_card_token_term = :credit_card_token_term ,
 iap_receipt_data = :iap_receipt_data,
 iap_receipt_data_hash = :iap_receipt_data_hash ,
 iap_latest_transaction_id = :iap_latest_transaction_id ,
 iap_latest_expires_date = :iap_latest_expires_date ,
 payment_type = :payment_type ,
 caring_code = :caring_code ,
 recipient = :recipient ,
 recipient_address = :recipient_address ,
 carrier_type = :carrier_type ,
 carrier_value = :carrier_value ,
 telecom_mp_id = :telecom_mp_id ,
 mod_subscriber_id = :mod_subscriber_id ,
 mod_subscriber_area = :mod_subscriber_area ,
 iab_receipt_data = :iab_receipt_data ,
 iab_order_id = :iab_order_id ,
 iab_latest_order_id = :iab_latest_order_id ,
 iab_latest_expires_date = :iab_latest_expires_date,
 updated_at = NOW(),
 family_id = :family_id
 WHERE payment_info.user_id = :user_id;`,

		"insertinvoice": `INSERT INTO invoices (
 order_id,
 total_amount,
 invoice_number,
 random_number,
 original,
 created_at) VALUES (
 :order_id,
 :total_amount,
 :invoice_number,
 :random_number,
 :original,
 :created_at);`,
	}
)
