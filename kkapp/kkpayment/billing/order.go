package billing

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/kkutil"
	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/feature"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/kktverror"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/permission"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/wrapper"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/wrapper/middleware"
	"github.com/KKTV/kktv-api-v3/pkg/billing"
	"github.com/KKTV/kktv-api-v3/pkg/database"
	"github.com/KKTV/kktv-api-v3/pkg/datetimer"
	zlog "github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	modelmw "github.com/KKTV/kktv-api-v3/pkg/model/middleware"
	"github.com/KKTV/kktv-api-v3/pkg/render"
	"github.com/KKTV/kktv-api-v3/pkg/validator"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/go-zoo/bone"
	"github.com/rs/zerolog"
	"gopkg.in/guregu/null.v3"
)

type PostBapiOrderBody struct {
	Email          string `json:"email"`
	ProductName    string `json:"product_name"`
	ReturnUrl      string `json:"return_url"`
	CancelUrl      string `json:"cancel_url"`
	Quantity       int    `json:"quantity"`
	InvoiceType    string `json:"invoice_type"`
	CarrierType    string `json:"carrier_type"`
	CarrierNumber  string `json:"carrier_number"`
	DonateCode     string `json:"donate_code"`
	BuyerName      string `json:"buyer_name"`
	BillingAddress string `json:"billing_address"`
	UTMMedium      string `json:"utm_medium"`
	UTMSource      string `json:"utm_source"`
	DeviceType     string `json:"device_type"`
}

type Handler struct {
	dbUserReader          database.DB
	dbUserWriter          database.DB
	userService           wrapper.UserService
	productPackageService wrapper.ProductPackageService
	billingOrderRepo      wrapper.PackageBillingOrderService
	permissionService     permission.Service
	featureService        feature.Service
	billingClient         billing.Client
}

func NewHandler() *Handler {
	userDBWriter := kkapp.App.DbUser.Master().Unsafe()
	userDBReader := kkapp.App.DbUser.Slave().Unsafe()
	return &Handler{
		dbUserReader:          userDBReader,
		dbUserWriter:          userDBWriter,
		userService:           wrapper.NewUserService(userDBWriter),
		permissionService:     kkapp.App.PermissionService,
		featureService:        feature.NewService(),
		productPackageService: wrapper.NewProductPackageService(userDBReader, userDBWriter),
		billingOrderRepo:      wrapper.NewPackageBillingOrderService(userDBReader, userDBWriter),
		billingClient:         kkapp.App.BillingClient,
	}
}

func (h *Handler) PostBillingOrders(w http.ResponseWriter, r *http.Request) {
	access := r.Context().Value(middleware.KeyAccessUser).(modelmw.AccessUser)
	userID := access.UserID

	response := model.MakeOk()

	var err error
	defer func() {
		if err != nil {
			errStr := err.Error()
			response.Status.Type = "FAIL"
			response.Status.Message = errStr

			render.JSON(w, http.StatusBadRequest, response)
		} else {
			render.JSON(w, http.StatusOK, response)
		}
	}()

	var reqBody PostBapiOrderBody
	err = json.NewDecoder(r.Body).Decode(&reqBody)
	if err != nil {
		err = errors.New("invalid parameters")
		return
	}
	if reqBody.Email == "" {
		err = errors.New("missing email")
		return
	}
	if reqBody.Quantity < 1 {
		err = errors.New("missing quantity")
		return
	}
	if reqBody.ProductName == "" {
		err = errors.New("missing product_name")
		return
	}
	if reqBody.DeviceType != "" &&
		reqBody.DeviceType != DeviceTypeDesktopWeb &&
		reqBody.DeviceType != DeviceTypeMobileWeb {
		err = errors.New("invalid device type")
		return
	}

	// update cache for billing products
	billingProducts, err := h.billingClient.ListProducts()
	if err != nil {
		zlog.Warn("GetConsoleBillingProduct: billingClient: list products error").Err(err).Send()
	}

	product := billingProducts.Map[reqBody.ProductName]
	if product.Name == "" {
		err = errors.New("product not found")
		return
	}

	if product.Price > 0 {
		if reqBody.ReturnUrl == "" {
			err = errors.New("missing return_url")
			return
		}
		if reqBody.CancelUrl == "" {
			err = errors.New("missing cancel_url")
			return
		}
		if reqBody.InvoiceType == "" {
			err = errors.New("missing invoice_type")
			return
		}

		if !slice.Contain([]string{"carrier", "paper", "donate"}, reqBody.InvoiceType) {
			err = errors.New("invalid invoice_type")
			return
		}

		switch reqBody.InvoiceType {
		case "carrier":
			if reqBody.CarrierType == "" {
				err = errors.New("missing carrier_type")
				return
			}

			if !slice.Contain([]string{"barcode", "citizen_certificate", "ezpay_member"}, reqBody.CarrierType) {
				err = errors.New("invalid carrier_type")
				return
			}

			if reqBody.CarrierType != "ezpay_member" {
				if reqBody.CarrierNumber == "" {
					err = errors.New("missing carrier_number")
					return
				}

				if slice.Contain([]string{"barcode", "citizen_certificate"}, reqBody.CarrierType) {
					numberLength := 8
					reg := regexp.MustCompile(`\A\/[A-Z\d\.\+\-]{7}\z`)
					if reqBody.CarrierType == "citizen_certificate" {
						numberLength = 16
						reg = regexp.MustCompile(`\A[A-Z]{2}\d{14}\z`)
					}
					if len(reqBody.CarrierNumber) != numberLength || !reg.MatchString(reqBody.CarrierNumber) {
						err = errors.New("invalid carrier_number")
						return
					}
				}
			}
		case "paper":
			if reqBody.BuyerName == "" {
				err = errors.New("missing buyer_name")
				return
			}
			if reqBody.BillingAddress == "" {
				err = errors.New("missing billing_address")
				return
			}
		case "donate":
			if reqBody.DonateCode == "" {
				err = errors.New("missing donate_code")
				return
			}
		}
	}

	logErr := func(topic string, err error) *zerolog.Event {
		return zlog.Error("create billing order: "+topic).Str("user_id", userID).Err(err)
	}

	var loginUser *dbuser.User
	if loginUser, err = h.userService.GetActiveByID(userID); err != nil {
		logErr("get user by id failed", err).Send()
		err = errors.New("get user failed")
		return
	} else if loginUser == nil {
		err = errors.New("user not found")
		return
	}

	var productPackage *dbuser.ProductPackage
	if productPackage, err = h.productPackageService.GetByBillingID(reqBody.ProductName); err != nil {
		logErr("get product package by billing identifier failed", err).
			Str("product_identifier", reqBody.ProductName).
			Send()
		err = errors.New("get product package failed")
		return
	} else if productPackage == nil {
		err = errors.New("product package not found")
		return
	}

	isMembershipEnabled, err := h.featureService.HasFlagForUser(feature.FlagEnablingMembership, loginUser.ID)
	if err != nil {
		zlog.Warn("PostBillingOrders: featureService fail to check flag: FlagEnablingMembership").
			Str("user_id", loginUser.ID).Err(err).Send()
	}
	membership := loginUser.GetMembership(isMembershipEnabled)

	var canCreateOrder bool
	if canCreateOrder, err = h.canCreateBillingOrder(loginUser, productPackage); err != nil {
		logErr("can create billing order failed", err).
			Interface("user", loginUser).
			Interface("product_package", productPackage).
			Send()
		err = errors.New("can create billing order failed")
		return
	} else if !canCreateOrder {
		zlog.Warn("create billing order: can not create billing order").
			Str("user_id", userID).
			Interface("user", loginUser).
			Interface("product_package", productPackage).
			Send()
		err = errors.New("user has subscribed")
		return
	}

	isActivePaidUser := h.isVIP(membership)
	payload := buildCreateOrderPayload(userID, reqBody, isActivePaidUser, product.Price)

	bResp, err := h.billingClient.CreateOrder(payload, userID)
	if err != nil {
		logErr("billingClient CreateOrder fail", err).
			Str("user_id", userID).
			Interface("payload", payload).Send()
		return
	}

	// step3. Unmarshal to response data if api succeed
	var data billing.CreateOrderRespData
	if err = json.Unmarshal([]byte(bResp.Data), &data); err != nil {
		logErr("unmarshal response by billing create order", err).
			Str("response_data", bResp.Data).
			Send()
		return
	}

	// create package billing order
	packageBillingOrder := &dbuser.PackageBillingOrder{
		UserID:             userID,
		BillingOrderNumber: data.OrderNumber,
		ProductPackageID:   productPackage.ID,
	}

	if err = h.createPackageBillingOrder(packageBillingOrder); err != nil {
		logErr("create package billing order failed", err).
			Interface("package_billing_order", packageBillingOrder).
			Send()
		err = errors.New("create package billing order failed")
		return
	}
	response.Data = data
}

type gracePurchaseReq struct {
	OrderID    string `json:"order_id" validate:"required"`
	ReturnURL  string `json:"return_url"`
	CancelURL  string `json:"cancel_url"`
	DeviceType string `json:"device_type" validate:"oneof=desktop mobile"`
}

func (h *Handler) PurchaseForGracePeriod(w http.ResponseWriter, r *http.Request) {
	req := gracePurchaseReq{}

	resp := model.KKResp{
		Status: model.KKStatus{Type: "FAIL"},
	}
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		resp.Status.Message = "invalid request body"
		render.JSON(w, http.StatusBadRequest, resp)
		return
	} else if err := validator.Validate(req); err != nil {
		resp.Status.Message = err.Error()
		render.JSON(w, http.StatusBadRequest, resp)
		return
	}

	access := r.Context().Value(middleware.KeyAccessUser).(modelmw.AccessUser)
	userID := access.UserID
	loginUser, err := h.userService.GetActiveByID(userID)
	if err != nil {
		zlog.Error("BillingHandler: PurchaseForGracePeriod: userRepo fail to get user").Err(err).Str("user_id", userID).Send()
		resp.Status.Message = "get user failed"
		render.JSON(w, http.StatusInternalServerError, resp)
		return
	} else if loginUser == nil {
		resp.Status.Message = "user not found"
		render.JSON(w, http.StatusNotFound, resp)
		return
	}

	permissionReq := permission.RequestPurchaseProductPackage(loginUser)
	if err := h.permissionService.Grant(permissionReq); kktverror.IsInternalErr(err) {
		zlog.Error("BillingHandler: PurchaseForGracePeriod: permissionService fail to grant permission").Err(err).Str("user_id", userID).Send()
		resp.Status.Message = "grant permission failed"
		render.JSON(w, http.StatusInternalServerError, resp)
		return
	} else if err != nil {
		resp.Status.Message = "permission denied"
		render.JSON(w, http.StatusForbidden, resp)
		return
	}

	orderReq := billing.CreateGracePeriodOrderReq{
		OrderNumber: req.OrderID,
		ReturnURL:   req.ReturnURL,
		CancelURL:   req.CancelURL,
		DeviceType:  req.DeviceType,
	}
	orderResp, err := h.billingClient.CreateGracePeriodOrder(userID, orderReq)
	zlog.Debug("BillingHandler: PurchaseForGracePeriod: create grace period order success").
		Str("user_id", userID).Interface("order_resp", orderResp).Err(err).Send()
	if err != nil {
		resp.Status.Message = "create grace period order failed"
		resp.Status.Subtype = err.Error()
		render.JSON(w, http.StatusBadRequest, resp)
		return
	}

	// NOTICE: no need to create record in package_billing_order
	// because new order for grace period is a child order of the original order

	resp.Status.Type = "OK"
	resp.Data = orderResp
	render.JSON(w, http.StatusOK, resp)
}

func (h *Handler) createPackageBillingOrder(packageBillingOrder *dbuser.PackageBillingOrder) error {
	now := time.Now()
	packageBillingOrder.CreatedAt = now
	packageBillingOrder.UpdatedAt = now
	return h.billingOrderRepo.Create(packageBillingOrder)
}

// isVIP check if user is VIP. VIP means user is not in free trial or expired
func (h *Handler) isVIP(membership dbuser.Membership) bool {
	return h.permissionService.IsPaidMember(membership)
}

type User struct {
	ID        string      `db:"id" json:"id"`
	Role      string      `db:"role" json:"role"`
	Type      string      `db:"type" json:"type"`
	CreatedAt null.Time   `db:"created_at" json:"createdAt"`
	CreatedBy null.String `db:"created_by" json:"createdBy"`
	ExpiredAt null.Time   `db:"expired_at" json:"expiredAt"`
	AutoRenew bool        `db:"auto_renew" json:"autoRenew"`
}

type Order struct {
	ID          string    `db:"id" json:"id"`
	ProductName string    `db:"item_name" json:"item_name"`
	Price       int64     `db:"price" json:"price"`
	Duration    string    `db:"duration" json:"duration"`
	OrderDate   int64     `db:"order_date" json:"order_date"`
	Status      string    `db:"status"`
	Info        OrderInfo `db:"info"`
	PaymentType string    `db:"payment_type"`
}

type OrderInfo map[string]interface{}

func (oi OrderInfo) Value() (driver.Value, error) {
	if len(oi) == 0 {
		return nil, nil
	}

	if jsonValue, err := json.Marshal(oi); err != nil {
		return nil, err
	} else {
		return driver.Value(jsonValue), nil
	}
}

func (oi *OrderInfo) Scan(src interface{}) error {
	var source []byte
	_m := make(map[string]interface{})

	switch src.(type) {
	case []uint8:
		source = []byte(src.([]uint8))
	case nil:
		return nil
	default:
		return errors.New("incompatible type for OrderInfo")
	}
	err := json.Unmarshal(source, &_m)
	if err != nil {
		return err
	}
	*oi = OrderInfo(_m)
	return nil
}

func (o *Order) IsActiveCVSCode() bool {
	return o.PaymentType == "cvs_code" && o.Status == dbuser.OrderStatusInProgress.String()
}

type GetOrderResponse struct {
	Number        string              `json:"number"`
	Price         int32               `json:"price"`
	OrderDate     int64               `json:"order_date"`
	PaymentStatus string              `json:"payment_status"`
	Product       GetOrderProductResp `json:"product"`
	User          GetOrderUserResp    `json:"user"`
	CVSCode       string              `json:"cvs_code,omitempty"`
	ExpiredAt     int                 `json:"expired_at,omitempty"`
}

type GetOrderProductResp struct {
	Name           string `json:"name"`
	IntervalAmount string `json:"interval_amount"`
}

type GetOrderUserResp struct {
	IsInTrialPeriod bool `json:"is_in_trial_period"`
}

func GetOrder(w http.ResponseWriter, r *http.Request) {
	var err error

	response := model.MakeOk()

	defer func() {
		if err != nil {
			response.Status.Type = "FAIL"
			response.Status.Message = err.Error()
			status := http.StatusBadRequest
			if err.Error() == "internal server error" {
				status = http.StatusInternalServerError
			}
			kkapp.App.Render.JSON(w, status, response)
		} else {
			kkapp.App.Render.JSON(w, http.StatusOK, response)
		}
	}()

	orderNum := bone.GetValue(r, "order_number")
	u := r.Context().Value("user").(model.JwtUser)

	if u.IsGuest() || u.Sub == "" {
		err = errors.New("guest user not allow")
		return
	}

	userId := u.Sub

	sql := `SELECT id, expired_at, created_at, role, auto_renew, type, created_by FROM users WHERE id = $1`
	var user User
	db := kkapp.App.DbUser.Slave()
	err = db.Get(&user, sql, userId)
	if err != nil {
		err = errors.New("user not found")
		return
	}

	loc, _ := time.LoadLocation("Asia/Taipei")

	userCreatedAt := user.CreatedAt.Time.In(loc)
	userCreatedAtPlusOneDay := userCreatedAt.AddDate(0, 0, 1)

	beginOfNextDay := time.Date(userCreatedAtPlusOneDay.Year(), userCreatedAtPlusOneDay.Month(), userCreatedAtPlusOneDay.Day(), 0, 0, 0, 0, loc)
	trialPeriodEnd := beginOfNextDay.Add(time.Hour * 24 * time.Duration(7)).UTC()

	// 判斷是否還在試用期內
	var isinTrialPeriod bool
	if user.Role == dbuser.RoleFreeTrial.String() || time.Now().UTC().Before(trialPeriodEnd) {
		isinTrialPeriod = true
	}

	// get order detail from billing if order number doesn't match kktv order format
	if !strings.HasPrefix(orderNum, "KT") {
		billingClient := kkapp.App.BillingClient
		resp, err := billingClient.GetOrder(userId, orderNum)
		if err != nil {
			zlog.Error("get order detail: get billing order failed").Err(err).Send()
			err = errors.New("order not found")
			return
		}

		var data billing.CustomerOrderData
		err = json.Unmarshal([]byte(resp.Data), &data)
		if err != nil {
			zlog.Error("get order detail: parse billing response data failed").Err(err).Send()
			err = errors.New("internal server error")
			return
		}

		subscribeProduct := data.Order.SubscribeProduct
		productName := subscribeProduct.Name
		productPrice := subscribeProduct.Price
		productIntervalAmount := subscribeProduct.IntervalAmount
		if productName == "" && len(data.Order.Products) > 0 {
			lastIndex := len(data.Order.Products) - 1
			lastProduct := data.Order.Products[lastIndex]
			productName = lastProduct.Name
			productPrice = lastProduct.Price
			productIntervalAmount = lastProduct.IntervalAmount
		}

		// 方案名稱以 package 為主
		var packageTitles map[string]string
		if packageTitles, err = GetPackageTitlesByBillingOrders([]billing.Order{data.Order}); err != nil {
			zlog.Error("get order detail: get billing package titles failed").
				Str("user_id", userId).
				Str("billing_order_number", orderNum).
				Err(err).
				Send()
			err = errors.New("internal server error")
			return
		}
		if packageTitle, ok := packageTitles[orderNum]; ok {
			productName = packageTitle
		}

		// 訂購日期
		// TODO billing order detail 加上 created_at 之後改抓 billing 回傳的 created_at
		now := time.Now().In(loc)
		beginOfToday := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, loc)
		orderDate := beginOfToday.Unix()

		response.Data = GetOrderResponse{
			Number:        data.Order.Number,
			Price:         productPrice,
			OrderDate:     orderDate,
			PaymentStatus: data.Order.PaymentStatus.String(),
			Product: GetOrderProductResp{
				Name:           productName,
				IntervalAmount: productIntervalAmount,
			},
			User: GetOrderUserResp{
				IsInTrialPeriod: isinTrialPeriod,
			},
		}
		return
	}

	sql = `SELECT
		o.id,
		o.price::numeric::int,
		date_part('epoch', o.order_date)::int as order_date,
		p.item_name,
		p.duration,
		o.status,
		o.payment_type,
		o.info
	FROM
		orders o
		LEFT JOIN products p ON o.product_id = p.id
	WHERE
		o.id = $1
		AND o.user_id = $2
	ORDER BY
		o.created_at DESC;`

	var order Order
	err = db.Get(&order, sql, orderNum, userId)
	if err != nil {
		zlog.Error("get order detail: get order failed").Err(err).Send()
		err = errors.New("order not found")
		return
	}

	unit, durationInt := kkutil.ParseDuration(order.Duration)
	unit = kkutil.DurationUnitChtName(unit)

	intervalAmount := fmt.Sprintf("%d%s", durationInt, unit)
	if durationInt == 1 {
		intervalAmount = unit
	}

	var paymentStatus string
	switch order.Status {
	case dbuser.OrderStatusInProgress.String():
		paymentStatus = "pending"
	case dbuser.OrderStatusOK.String():
		paymentStatus = "paid"
	case dbuser.OrderStatusFail.String():
		paymentStatus = "failed"
	}

	responseData := GetOrderResponse{
		Number:        order.ID,
		Price:         int32(order.Price),
		OrderDate:     order.OrderDate,
		PaymentStatus: paymentStatus,
		Product: GetOrderProductResp{
			Name:           order.ProductName,
			IntervalAmount: intervalAmount,
		},
		User: GetOrderUserResp{
			IsInTrialPeriod: isinTrialPeriod,
		},
	}

	if order.IsActiveCVSCode() {
		var (
			cvsCode, expireDate, expireTime string
			ok                              bool
		)
		infoValue := order.Info

		if cvsCode, ok = infoValue["CVSCode"].(string); !ok {
			zlog.Warn("get order detail error: cvsCode assertion failed").
				Err(err).
				Interface("order info:", order.Info).Send()
		}

		if expireDate, ok = infoValue["ExpireDate"].(string); !ok {
			zlog.Warn("get order detail error: expireDate assertion failed").
				Err(err).
				Interface("order info:", order.Info).Send()
		}

		if expireTime, ok = infoValue["ExpireTime"].(string); !ok {
			zlog.Warn("get order detail error: expireTime assertion failed").
				Err(err).
				Interface("order info:", order.Info).Send()
		}
		responseData.CVSCode = cvsCode
		dateTimeStr := expireDate + " " + expireTime
		if expireDateTime, err := time.ParseInLocation("2006-01-02 15:04:05", dateTimeStr, datetimer.LocationTaipei); err != nil {
			zlog.Warn("get order detail error: convert expired date time failed").
				Str("date time:", dateTimeStr).
				Send()
		} else {
			responseData.ExpiredAt = int(expireDateTime.Unix())
		}
	}

	response.Data = responseData
}

func (h *Handler) canCreateBillingOrder(user *dbuser.User, productPackage *dbuser.ProductPackage) (bool, error) {
	if !productPackage.Active {
		return false, nil
	}

	// confirm that the product package is a pre-order
	// if the product package is a pre-order, users should create a billing order
	// unless users don't have authority
	isPreOrder := false
	if productPackage.BillingProductIds != nil {
		for _, productID := range *productPackage.BillingProductIds {
			product := &billing.Product{Identifier: productID}
			if product.IsPreOrder() {
				isPreOrder = true
				break
			}
		}
	}
	var permissionReq permission.Request
	if isPreOrder {
		permissionReq = permission.RequestPurchasePreOrderProductPackage(user)
	} else {
		permissionReq = permission.RequestPurchaseProductPackage(user)
	}

	err := h.permissionService.Grant(permissionReq)
	if kktverror.IsInternalErr(err) {
		return false, err
	}
	return err == nil, nil
}
