//go:build integration

package kkpayment_test

import (
	"bytes"
	"fmt"
	"log"
	"net/http"
	"net/http/httptest"
	"os"
	"strings"
	"testing"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/kkpayment"
	"github.com/go-zoo/bone"
	"github.com/justinas/alice"
	"github.com/rs/cors"
)

var (
	mux *bone.Mux
)

func TestMain(m *testing.M) {
	// call flag.Parse() here if TestMain uses flags
	log.Println("======================================")
	log.Println("TESTCASE Context INIT")
	log.Println("======================================")

	testEnvInit()

	setupData()

	setupAPI()

	ret := m.Run()
	//if ret == 0 {
	//}

	TearDown()

	os.Exit(ret)
}

func setupAPI() {
	log.Println("======================================")
	log.Println("TESTCASE Setup API")
	log.Println("======================================")

	mux = bone.New()
	common := alice.New(
		cors.New(cors.Options{
			AllowedHeaders:   []string{"Content-Type", "Authorization", "X-Device-ID"},
			AllowedMethods:   []string{"GET", "OPTIONS", "POST", "PUT", "DELETE"},
			MaxAge:           86400,
			AllowCredentials: true}).Handler,
	)

	mux.Post("/api", common.ThenFunc(kkpayment.PostTSTAR))

}

func setupData() {
	log.Println("======================================")
	log.Println("TESTCASE Setup Data")
	log.Println("======================================")

	db := kkapp.App.DbUser.Master()
	// Add user for TestChangeMSISDN - Case 06: owner of MSISDN_New exists
	db.Exec("INSERT INTO users (id, email, phone, avatar_url, name, birthday, gender, expired_at, master, created_at, updated_at, role, media_source, auto_renew, payment_info, type, created_by, cold_start_selected_titles) VALUES('99fa993541197ed242c15a194216d127bb2fe85ca4670c965ba0010438b75282', NULL, '+886975183100', NULL, 'test01', NULL, NULL, '2018-07-31 00:00:00.000', NULL, '2018-06-27 16:12:05.852', '2018-07-12 23:23:18.810', 'premium', NULL, false, NULL, 'general', '', NULL);")
	db.Exec("INSERT INTO users (id, email, phone, avatar_url, name, birthday, gender, expired_at, master, created_at, updated_at, role, media_source, auto_renew, payment_info, type, created_by, cold_start_selected_titles) VALUES('99fa993541197ed242c15a194216d127bb2fe85ca4670c965ba0010438b75283', NULL, '+886975183123', NULL, NULL, NULL, NULL, '2018-07-31 00:00:00.000', NULL, '2018-06-27 16:12:05.852', '2018-07-12 23:23:18.810', 'premium', NULL, false, NULL, 'general', '', NULL);")

	// TestCancelService - Case 05: check payment type - should be tstar
	// Add user
	db.Exec("INSERT INTO users (id, email, phone, avatar_url, name, birthday, gender, expired_at, master, created_at, updated_at, role, media_source, auto_renew, payment_info, type, created_by, cold_start_selected_titles) VALUES('99fa993541197ed242c15a194216d127bb2fe85ca4670c965ba0010438b75284', NULL, '+886922333444', NULL, NULL, NULL, NULL, '2018-07-31 00:00:00.000', NULL, '2018-06-27 16:12:05.852', '2018-07-12 23:23:18.810', 'premium', NULL, false, NULL, 'general', '', NULL);")
	// Add payment_info - not tstar payment type
	db.Exec("INSERT INTO payment_info (user_id, email, credit_card_6no, credit_card_4no, credit_card_token_value, iap_receipt_data, iap_receipt_data_hash, payment_type, caring_code, recipient, recipient_address, carrier_type, carrier_value, created_at, updated_at, phone, credit_card_token_term, iap_latest_transaction_id, iap_latest_expires_date, telecom_mp_id, tstar_order_id, tstar_contract_id, mod_subscriber_id, mod_subscriber_area, iab_receipt_data, iab_order_id, iab_latest_order_id, iab_latest_expires_date) VALUES('99fa993541197ed242c15a194216d127bb2fe85ca4670c965ba0010438b75284', NULL, NULL, NULL, NULL, NULL, NULL, 'mod', NULL, NULL, NULL, NULL, NULL, '2018-01-25 00:27:00.887', NULL, NULL, NULL, NULL, NULL, 'CYC_TSTAR', 'ORD20190318185211659', '0087009873', NULL, NULL, NULL, NULL, NULL, NULL);")

	// TestCancelService - Case 06: check order is unsubscribed already
	// Add user
	db.Exec("INSERT INTO users (id, email, phone, avatar_url, name, birthday, gender, expired_at, master, created_at, updated_at, role, media_source, auto_renew, payment_info, type, created_by, cold_start_selected_titles) VALUES('99fa993541197ed242c15a194216d127bb2fe85ca4670c965ba0010438b75285', NULL, '+886911222555', NULL, NULL, NULL, NULL, '2018-07-31 00:00:00.000', NULL, '2018-06-27 16:12:05.852', '2018-07-12 23:23:18.810', 'premium', NULL, false, NULL, 'general', '', NULL);")
	// Add payment_info
	db.Exec("INSERT INTO payment_info (user_id, email, credit_card_6no, credit_card_4no, credit_card_token_value, iap_receipt_data, iap_receipt_data_hash, payment_type, caring_code, recipient, recipient_address, carrier_type, carrier_value, created_at, updated_at, phone, credit_card_token_term, iap_latest_transaction_id, iap_latest_expires_date, telecom_mp_id, tstar_order_id, tstar_contract_id, mod_subscriber_id, mod_subscriber_area, iab_receipt_data, iab_order_id, iab_latest_order_id, iab_latest_expires_date) VALUES('99fa993541197ed242c15a194216d127bb2fe85ca4670c965ba0010438b75285', NULL, NULL, NULL, NULL, NULL, NULL, 'tstar', NULL, NULL, NULL, NULL, NULL, '2018-01-25 00:27:00.887', NULL, NULL, NULL, NULL, NULL, 'CYC_TSTAR', 'ORD20190318185211789', '0087008877', NULL, NULL, NULL, NULL, NULL, NULL);")
	// Add unsubscribe
	db.Exec("INSERT INTO unsubscribe (user_id, name, email, reason, created_at, payment_type, payment_info_snapshot) VALUES('99fa993541197ed242c15a194216d127bb2fe85ca4670c965ba0010438b75285', NULL, NULL, 'By TSTAR API (\"cancelservice\")', '2017-11-10 22:27:03.083', 'tstar', '{\"email\": null, \"phone\": null, \"userId\": \"99fa993541197ed242c15a194216d127bb2fe85ca4670c965ba0010438b75285\", \"recipient\": null, \"caringCode\": null, \"carrierType\": null, \"paymentType\": \"tstar\", \"telecomMpId\": \"CYC_TSTAR\", \"carrierValue\": null, \"tstarOrderId\": \"ORD20190318185211789\", \"creditCard4no\": null, \"creditCard6no\": null, \"iapReceiptData\": null, \"modSubscriberId\": null, \"tstarContractId\": \"0087008877\", \"recipientAddress\": null, \"modSubscriberArea\": null, \"iapReceiptDataHash\": null, \"creditCardTokenTerm\": null, \"creditCardTokenValue\": null, \"iapLatestTransactionId\": null}');")

	// TestCancelService - Case 07: cancel order
	// Add user
	db.Exec("INSERT INTO users (id, email, phone, avatar_url, name, birthday, gender, expired_at, master, created_at, updated_at, role, media_source, auto_renew, payment_info, type, created_by, cold_start_selected_titles) VALUES('99fa993541197ed242c15a194216d127bb2fe85ca4670c965ba0010438b75286', NULL, '+886911222666', NULL, NULL, NULL, NULL, '2018-07-31 00:00:00.000', NULL, '2018-06-27 16:12:05.852', '2018-07-12 23:23:18.810', 'premium', NULL, false, NULL, 'general', '', NULL);")
	// Add payment_info
	db.Exec("INSERT INTO payment_info (user_id, email, credit_card_6no, credit_card_4no, credit_card_token_value, iap_receipt_data, iap_receipt_data_hash, payment_type, caring_code, recipient, recipient_address, carrier_type, carrier_value, created_at, updated_at, phone, credit_card_token_term, iap_latest_transaction_id, iap_latest_expires_date, telecom_mp_id, tstar_order_id, tstar_contract_id, mod_subscriber_id, mod_subscriber_area, iab_receipt_data, iab_order_id, iab_latest_order_id, iab_latest_expires_date) VALUES('99fa993541197ed242c15a194216d127bb2fe85ca4670c965ba0010438b75286', NULL, NULL, NULL, NULL, NULL, NULL, 'tstar', NULL, NULL, NULL, NULL, NULL, '2018-01-25 00:27:00.887', NULL, NULL, NULL, NULL, NULL, 'CYC_TSTAR', 'ORD20190531185211659', '0089008988', NULL, NULL, NULL, NULL, NULL, NULL);")
	// Add orders
	db.Exec("INSERT INTO orders (id, user_id, product_id, price, payment_type, start_date, end_date, status, order_date, info, created_at, realized_at, price_no_tax, tax_rate, invoice, canceled_at, fee, external_order_id) VALUES('KT0520190511000098', '99fa993541197ed242c15a194216d127bb2fe85ca4670c965ba0010438b75286', 40, 139, 'tstar', '2019-06-11 00:00:00.000', '2019-07-11 00:00:00.000', NULL, '2019-06-11 00:00:00.000', 'null', '2019-05-11 19:53:34.014', NULL, 132, 5, NULL, NULL, 0, NULL);")
	db.Exec("INSERT INTO orders (id, user_id, product_id, price, payment_type, start_date, end_date, status, order_date, info, created_at, realized_at, price_no_tax, tax_rate, invoice, canceled_at, fee, external_order_id) VALUES('KT0520190411000098', '99fa993541197ed242c15a194216d127bb2fe85ca4670c965ba0010438b75286', 40, 139, 'tstar', '2019-05-11 00:00:00.000', '2019-06-11 00:00:00.000', 'ok', '2019-06-11 00:00:00.000', 'null', '2019-04-11 19:53:34.014', NULL, 132, 5, NULL, NULL, 0, NULL);")

	// TestCreateServcie - Case 04: check user has subscribed by phone number
	// Add user
	db.Exec("INSERT INTO users (id, email, phone, avatar_url, name, birthday, gender, expired_at, master, created_at, updated_at, role, media_source, auto_renew, payment_info, type, created_by, cold_start_selected_titles) VALUES('99fa993541197ed242c15a194216d127bb2fe85ca4670c965ba0010438b75287', NULL, '+886975183183', NULL, NULL, NULL, NULL, '2019-12-31 00:00:00.000', NULL, '2019-05-31 16:12:05.852', '2019-05-31 23:23:18.810', 'premium', NULL, true, NULL, 'general', 'tstar', NULL);")
	// Add payment_info
	db.Exec("INSERT INTO payment_info (user_id, email, credit_card_6no, credit_card_4no, credit_card_token_value, iap_receipt_data, iap_receipt_data_hash, payment_type, caring_code, recipient, recipient_address, carrier_type, carrier_value, created_at, updated_at, phone, credit_card_token_term, iap_latest_transaction_id, iap_latest_expires_date, telecom_mp_id, tstar_order_id, tstar_contract_id, mod_subscriber_id, mod_subscriber_area, iab_receipt_data, iab_order_id, iab_latest_order_id, iab_latest_expires_date) VALUES('99fa993541197ed242c15a194216d127bb2fe85ca4670c965ba0010438b75287', NULL, NULL, NULL, NULL, NULL, NULL, 'tstar', NULL, NULL, NULL, NULL, NULL, '2018-01-25 00:27:00.887', NULL, NULL, NULL, NULL, NULL, 'CYC_TSTAR', 'ORD20190531185211888', '0088008888', NULL, NULL, NULL, NULL, NULL, NULL);")

	// TestCreateServcie - Case 05: check duplicated ORDER_ID
	// Add user
	db.Exec("INSERT INTO users (id, email, phone, avatar_url, name, birthday, gender, expired_at, master, created_at, updated_at, role, media_source, auto_renew, payment_info, type, created_by, cold_start_selected_titles) VALUES('99fa993541197ed242c15a194216d127bb2fe85ca4670c965ba0010438b75288', NULL, '+886975183888', NULL, NULL, NULL, NULL, '2019-12-31 00:00:00.000', NULL, '2019-05-31 16:12:05.852', '2019-05-31 23:23:18.810', 'premium', NULL, true, NULL, 'general', '', NULL);")
	// Add payment_info
	db.Exec("INSERT INTO payment_info (user_id, email, credit_card_6no, credit_card_4no, credit_card_token_value, iap_receipt_data, iap_receipt_data_hash, payment_type, caring_code, recipient, recipient_address, carrier_type, carrier_value, created_at, updated_at, phone, credit_card_token_term, iap_latest_transaction_id, iap_latest_expires_date, telecom_mp_id, tstar_order_id, tstar_contract_id, mod_subscriber_id, mod_subscriber_area, iab_receipt_data, iab_order_id, iab_latest_order_id, iab_latest_expires_date) VALUES('99fa993541197ed242c15a194216d127bb2fe85ca4670c965ba0010438b75288', NULL, NULL, NULL, NULL, NULL, NULL, 'tstar', NULL, NULL, NULL, NULL, NULL, '2018-01-25 00:27:00.887', NULL, NULL, NULL, NULL, NULL, 'CYC_TSTAR', 'ORD20190531185211999', '0088008899', NULL, NULL, NULL, NULL, NULL, NULL);")

	// Reset IAB test payment_info
	db.Exec("UPDATE payment_info SET iab_order_id = NULL, iab_latest_order_id = NULL  WHERE user_id = '8ac862a948eadd086cae69a666604742c893c85ae0efd216d333bb05a461eac9'")

	// credit_card test
	db.Exec("INSERT INTO users (id, phone, role, type, expired_at) VALUES('1235', '+***********', 'freetrial', 'general', '2030-01-01 00:00:00');")

}

func TearDown() {
	log.Println("======================================")
	log.Println("TESTCASE TearDown")
	log.Println("======================================")

	// Remove test account, and tokens
	db := kkapp.App.DbUser.Master()

	// Remove user for TestChangeMSISDN - Case 06: owner of MSISDN_New exists
	db.Exec("DELETE FROM users WHERE id = '99fa993541197ed242c15a194216d127bb2fe85ca4670c965ba0010438b75282'")
	db.Exec("DELETE FROM users WHERE id = '99fa993541197ed242c15a194216d127bb2fe85ca4670c965ba0010438b75283'")

	// Remove data for TestCancelService - Case 05: check payment type - should be tstar
	db.Exec("DELETE FROM payment_info WHERE user_id = '99fa993541197ed242c15a194216d127bb2fe85ca4670c965ba0010438b75282'")
	db.Exec("DELETE FROM payment_info WHERE user_id = '99fa993541197ed242c15a194216d127bb2fe85ca4670c965ba0010438b75284'")
	db.Exec("DELETE FROM unsubscribe WHERE user_id='99fa993541197ed242c15a194216d127bb2fe85ca4670c965ba0010438b75284'")
	db.Exec("DELETE FROM users WHERE id = '99fa993541197ed242c15a194216d127bb2fe85ca4670c965ba0010438b75284'")

	// Remove data for TestCancelService - Case 06: check order is unsubscribed already
	db.Exec("DELETE FROM payment_info WHERE user_id = '99fa993541197ed242c15a194216d127bb2fe85ca4670c965ba0010438b75285'")
	db.Exec("DELETE FROM unsubscribe WHERE user_id='99fa993541197ed242c15a194216d127bb2fe85ca4670c965ba0010438b75285'")
	db.Exec("DELETE FROM users WHERE id = '99fa993541197ed242c15a194216d127bb2fe85ca4670c965ba0010438b75285'")

	// Remove data for TestCancelService - Case 07: cancel order
	db.Exec("DELETE FROM payment_info WHERE user_id = '99fa993541197ed242c15a194216d127bb2fe85ca4670c965ba0010438b75286'")
	db.Exec("DELETE FROM unsubscribe WHERE user_id='99fa993541197ed242c15a194216d127bb2fe85ca4670c965ba0010438b75286'")
	db.Exec("DELETE FROM orders WHERE user_id='99fa993541197ed242c15a194216d127bb2fe85ca4670c965ba0010438b75286'")
	db.Exec("DELETE FROM users WHERE id = '99fa993541197ed242c15a194216d127bb2fe85ca4670c965ba0010438b75286'")

	// Remove data for TestCreateService - Case 04: user has subscribed by phone number
	db.Exec("DELETE FROM payment_info WHERE user_id = '99fa993541197ed242c15a194216d127bb2fe85ca4670c965ba0010438b75287'")
	db.Exec("DELETE FROM users WHERE id = '99fa993541197ed242c15a194216d127bb2fe85ca4670c965ba0010438b75287'")

	// Remove data for TestCreateService - Case 05: check duplicated ORDER_ID
	db.Exec("DELETE FROM payment_info WHERE user_id = '99fa993541197ed242c15a194216d127bb2fe85ca4670c965ba0010438b75288'")
	db.Exec("DELETE FROM users WHERE id = '99fa993541197ed242c15a194216d127bb2fe85ca4670c965ba0010438b75288'")

	// Clean IAB payment test
	db.Exec("DELETE FROM orders WHERE user_id = '8ac862a948eadd086cae69a666604742c893c85ae0efd216d333bb05a461eac9'")
	db.Exec("UPDATE payment_info SET iab_order_id = NULL, iab_latest_order_id = NULL  WHERE user_id = '8ac862a948eadd086cae69a666604742c893c85ae0efd216d333bb05a461eac9'")

	// credit_card
	// clear order
	db.Exec("DELETE FROM orders USING users WHERE users.id = orders.user_id AND users.phone = '+***********'")
	// clear payment_info
	db.Exec("DELETE FROM payment_info USING users WHERE users.id = payment_info.user_id AND users.phone = '+***********'")
	// clear user
	db.Exec("DELETE from users u WHERE phone = '+***********'")

	// Remove data for TestCreateService - Case 06: create order
	var userID string
	err := db.Get(&userID, "SELECT id FROM users WHERE phone='+886912123999'")
	if err != nil {
		log.Println("|||*** TearDown *** Failed *** |||", err)
	}
	_, err = db.Exec("DELETE FROM payment_info WHERE user_id='" + userID + "'")
	if err != nil {
		log.Println("|||*** TearDown *** Failed *** |||", err)
	}
	_, err = db.Exec("DELETE FROM orders WHERE user_id='" + userID + "'")
	if err != nil {
		log.Println("|||*** TearDown *** Failed *** |||", err)
	}
	_, err = db.Exec("DELETE FROM users WHERE phone = '+886912123999'")
	if err != nil {
		log.Println("|||*** TearDown *** Failed *** |||", err)
	}

}

func doTest(t *testing.T, jsonStr []byte, expectCode string, expectStr string, comments string) {
	r, _ := http.NewRequest("POST", "/api", bytes.NewBuffer(jsonStr))
	w := httptest.NewRecorder()

	mux.ServeHTTP(w, r)

	// more detail info for go test -v
	fmt.Println("")
	fmt.Println("")
	fmt.Println(comments)
	fmt.Println(w.Result().StatusCode)
	fmt.Println(w.Body.String())

	if status := w.Code; status != http.StatusOK {
		t.Errorf("\nhandler returned wrong status code: got %v want %v", status, http.StatusOK)
	}
	if !strings.Contains(w.Body.String(), expectCode) && !strings.Contains(w.Body.String(), expectStr) {
		t.Errorf("\nhandler returned wrong body: got %v want %v", w.Body.String(), expectCode+" && "+expectStr)
	}
}

func TestChangeOrder(t *testing.T) {

	var jsonStr = []byte(`{"REQUEST_ID":"20160419153658666","PARAM":{"ACTION":"changeorder","SVC_ID_OLD":"SVC20160413120023333","SVC_ID_NEW":"SVC20160413120023333","ORDER_ID_OLD":"ORD20160422185211659","ORDER_ID_NEW":"ORD20160422185211659","CONTRACT_ID":"CON1234568","MSISDN":"0923123123","MODIFY_DATE":"2016/05/01 10:15:20"}}`)
	doTest(t, jsonStr, "999", "不支援此服務", "*** TestChangeOrder ***")

}

func TestSuspended(t *testing.T) {

	var jsonStr = []byte(`{"PARAM":{"ACTION":"suspended","CONTRACT_ID":"CON1234568","MODIFY_DATE":"2016/05/0110:15:20","MSISDN":"0923123123","ORDER_ID":"ORD20160422185211659","SVC_ID":"SVC20160413120023333"},"REQUEST_ID":"20160419153658666"}`)
	doTest(t, jsonStr, "000", "作業成功", "*** TestSuspended ***")

}

func TestRestartService(t *testing.T) {

	var jsonStr = []byte(`{"PARAM":{"ACTION":"restartservice","CONTRACT_ID":"CON1234568","MODIFY_DATE":"2016/05/0110:15:20","MSISDN":"0923123123","ORDER_ID":"ORD20160422185211659","SVC_ID":"SVC20160413120023333"},"REQUEST_ID":"20160419153658666"}`)
	doTest(t, jsonStr, "000", "作業成功", "*** TestRestartService ***")

}

func TestChangeMSISDN(t *testing.T) {

	// Case 01: empty new phone
	jsonStr := []byte(`{"REQUEST_ID":"20160419153658666","PARAM":{"ACTION":"changemsisdn","SVC_ID":"SVC20170216104530478","ORDER_ID":"ORD20160422185211659","CONTRACT_ID":"CON1234568","MSISDN_OLD":"0923123123","MSISDN_NEW":"","MODIFY_DATE":"2016/05/01 10:15:20"}}`)
	doTest(t, jsonStr, "900", "錯誤的電話號碼", "*** TestChangeMSISDN - Case 01: empty new phone ***")

	// Case 02: empty old phone
	jsonStr = []byte(`{"REQUEST_ID":"20160419153658666","PARAM":{"ACTION":"changemsisdn","SVC_ID":"SVC20170216104530478","ORDER_ID":"ORD20160422185211659","CONTRACT_ID":"CON1234568","MSISDN_OLD":"","MSISDN_NEW":"0923123456","MODIFY_DATE":"2016/05/0110:15:20"}}`)
	doTest(t, jsonStr, "900", "錯誤的電話號碼", "*** TestChangeMSISDN - Case 02: Change MSISDN - empty old phone ***")

	// Case 03: empty ORDER_ID
	jsonStr = []byte(`{"REQUEST_ID":"20160419153658666","PARAM":{"ACTION":"changemsisdn","SVC_ID":"SVC20170216104530478","ORDER_ID":"","CONTRACT_ID":"CON1234568","MSISDN_OLD":"0923123123","MSISDN_NEW":"0923123456","MODIFY_DATE":"2016/05/0110:15:20"}}`)
	doTest(t, jsonStr, "900", "缺少 ORDER_ID", "*** TestChangeMSISDN - Case 03: Change MSISDN - empty ORDER_ID ***")

	// Case 04: empty SVC_ID
	jsonStr = []byte(`{"REQUEST_ID":"20160419153658666","PARAM":{"ACTION":"changemsisdn","SVC_ID":"","ORDER_ID":"ORD20160422185211659","CONTRACT_ID":"CON1234568","MSISDN_OLD":"0923123123","MSISDN_NEW":"0923123456","MODIFY_DATE":"2016/05/0110:15:20"}}`)
	doTest(t, jsonStr, "904", "不正確的 SVC_ID", "*** TestChangeMSISDN - Case 04: Change MSISDN - empty SVC_ID ***")

	// Case 05: owner of MSISDN_OLD does not exist
	jsonStr = []byte(`{"REQUEST_ID":"20160419153658666","PARAM":{"ACTION":"changemsisdn","SVC_ID":"SVC20170216104530478","ORDER_ID":"ORD20160422185211659","CONTRACT_ID":"CON1234568","MSISDN_OLD":"0923000000","MSISDN_NEW":"0923123456","MODIFY_DATE":"2016/05/0110:15:20"}}`)
	doTest(t, jsonStr, "900", "使用者不存在", "*** TestChangeMSISDN - Case 05: Change MSISDN - owner of MSISDN_OLD does not exist")

	// Case 06: owner of MSISDN_New exists
	jsonStr = []byte(`{"REQUEST_ID":"20160419153658666","PARAM":{"ACTION":"changemsisdn","SVC_ID":"SVC20170216104530478","ORDER_ID":"ORD20160422185211659","CONTRACT_ID":"CON1234568","MSISDN_OLD":"0975183100","MSISDN_NEW":"0975183123","MODIFY_DATE":"2016/05/0110:15:20"}}`)
	doTest(t, jsonStr, "910", "新電話已在 KKTV 註冊", "*** TestChangeMSISDN - Case 06: Change MSISDN - owner of MSISDN_New exists")

	// Case 07: Change MSISDN should be successful
	jsonStr = []byte(`{"REQUEST_ID":"20190529153658666","PARAM":{"ACTION":"changemsisdn","SVC_ID":"SVC20170216104530478","ORDER_ID":"ORD20190529185211659","CONTRACT_ID":"CON1234568","MSISDN_OLD":"0975183123","MSISDN_NEW":"0975123456","MODIFY_DATE":"2019/05/2910:15:20"}}`)
	doTest(t, jsonStr, "000", "作業成功", "*** TestChangeMSISDN - Case 07: Change MSISDN - Change MSISDN should be successful")

}

func TestCancelService(t *testing.T) {
	// Case 01: check phone number - empty
	jsonStr := []byte(`{"PARAM":{"ACTION":"cancelservice","CONTRACT_ID":"CON1234568","MODIFY_DATE":"2019/05/3010:15:20","MSISDN":"","ORDER_ID":"ORD20190318185211659","SVC_ID":"SVC20170216104530478"},"REQUEST_ID":"20190530153658666"}`)
	doTest(t, jsonStr, "900", "錯誤的電話號碼", "*** TestCancelService - Case 01: check phone number ***")

	// Case 01: check phone number - wrong format
	jsonStr = []byte(`{"PARAM":{"ACTION":"cancelservice","CONTRACT_ID":"CON1234568","MODIFY_DATE":"2019/05/3010:15:20","MSISDN":"0911222abc","ORDER_ID":"ORD20190318185211659","SVC_ID":"SVC20170216104530478"},"REQUEST_ID":"20190530153658666"}`)
	doTest(t, jsonStr, "900", "錯誤的電話號碼", "*** TestCancelService - Case 01: check phone number ***")

	// Case 02: check ORDER_ID
	jsonStr = []byte(`{"PARAM":{"ACTION":"cancelservice","CONTRACT_ID":"CON1234568","MODIFY_DATE":"2019/05/3010:15:20","MSISDN":"0911222333","ORDER_ID":"","SVC_ID":"SVC20170216104530478"},"REQUEST_ID":"20190530153658666"}`)
	doTest(t, jsonStr, "900", "缺少 ORDER_ID", "*** TestCancelService - Case 02: check ORDER_ID ***")

	// Case 03: check SVC_ID
	jsonStr = []byte(`{"PARAM":{"ACTION":"cancelservice","CONTRACT_ID":"CON1234568","MODIFY_DATE":"2019/05/3010:15:20","MSISDN":"0911222333","ORDER_ID":"ORD20190318185211659","SVC_ID":""},"REQUEST_ID":"20190530153658666"}`)
	doTest(t, jsonStr, "904", "不正確的 SVC_ID", "*** TestCancelService - Case 03: check SVC_ID ***")

	// Case 04: check user not exist
	jsonStr = []byte(`{"PARAM":{"ACTION":"cancelservice","CONTRACT_ID":"CON1234568","MODIFY_DATE":"2019/05/3010:15:20","MSISDN":"0923000000","ORDER_ID":"ORD20190318185211659","SVC_ID":"SVC20170216104530478"},"REQUEST_ID":"20190530153658666"}`)
	doTest(t, jsonStr, "900", "使用者不存在", "*** TestCancelService - Case 04: check user not exist ***")

	// Case 05: check payment type - should be tstar
	jsonStr = []byte(`{"PARAM":{"ACTION":"cancelservice","CONTRACT_ID":"CON1234568","MODIFY_DATE":"2019/05/3010:15:20","MSISDN":"0922333444","ORDER_ID":"ORD20190318185211659","SVC_ID":"SVC20170216104530478"},"REQUEST_ID":"20190530153658666"}`)
	doTest(t, jsonStr, "900", "使用者訂單資訊不正確", "*** TestCancelService - Case 05: check payment type - should be tstar ***")

	// Case 06: check order is unsubscribed already
	jsonStr = []byte(`{"PARAM":{"ACTION":"cancelservice","CONTRACT_ID":"0087008877","MODIFY_DATE":"2019/05/3010:15:20","MSISDN":"0911222555","ORDER_ID":"ORD20190318185211789","SVC_ID":"SVC20170216104530478"},"REQUEST_ID":"20190530153658666"}`)
	doTest(t, jsonStr, "909", "重複申退", "*** TestCancelService - Case 06: check order is unsubscribed already ***")

	// Case 07: cancel order
	jsonStr = []byte(`{"PARAM":{"ACTION":"cancelservice","CONTRACT_ID":"0089008988","MODIFY_DATE":"2019/05/3010:15:20","MSISDN":"0911222666","ORDER_ID":"ORD20190531185211659","SVC_ID":"SVC20170216104530478"},"REQUEST_ID":"20190531153658666"}`)
	doTest(t, jsonStr, "000", "作業成功", "*** TestCancelService - Case 07: cancel order ***")
}

func TestCreateService(t *testing.T) {
	// Case 01: check phone number - empty
	jsonStr := []byte(`{"PARAM":{"ACTION":"createservice","CONTRACT_ID":"CON1234568","MODIFY_DATE":"2019/05/3110:15:20","MSISDN":"","ORDER_ID":"ORD20190531185211659","SVC_ID":"SVC20170216104530478"},"REQUEST_ID":"20190531141058666"}`)
	doTest(t, jsonStr, "900", "錯誤的電話號碼", "*** TestCreateService - Case 01: check phone number ***")

	// Case 01: check phone number - wrong format
	jsonStr = []byte(`{"PARAM":{"ACTION":"createservice","CONTRACT_ID":"CON1234568","MODIFY_DATE":"2019/05/3110:15:20","MSISDN":"0911222abc","ORDER_ID":"ORD20190531185211659","SVC_ID":"SVC20170216104530478"},"REQUEST_ID":"20190531141058666"}`)
	doTest(t, jsonStr, "900", "錯誤的電話號碼", "*** TestCreateService - Case 01: check phone number ***")

	// Case 02: check ORDER_ID
	jsonStr = []byte(`{"PARAM":{"ACTION":"createservice","CONTRACT_ID":"CON1234568","MODIFY_DATE":"2019/05/3110:15:20","MSISDN":"0911222333","ORDER_ID":"","SVC_ID":"SVC20170216104530478"},"REQUEST_ID":"20190531141058666"}`)
	doTest(t, jsonStr, "900", "缺少 ORDER_ID", "*** TestCreateService - Case 02: check ORDER_ID ***")

	// Case 03: check SVC_ID
	jsonStr = []byte(`{"PARAM":{"ACTION":"createservice","CONTRACT_ID":"CON1234568","MODIFY_DATE":"2019/05/3110:15:20","MSISDN":"0911222333","ORDER_ID":"ORD20190531185211659","SVC_ID":""},"REQUEST_ID":"20190531141058666"}`)
	doTest(t, jsonStr, "904", "不正確的 SVC_ID", "*** TestCreateService  - Case 03: check SVC_ID ***")

	// Case 04:	check user has subscribed by phone number
	jsonStr = []byte(`{"PARAM":{"ACTION":"createservice","CONTRACT_ID":"CON1234568","MODIFY_DATE":"2019/05/3110:15:20","MSISDN":"0975183183","ORDER_ID":"ORD20190531185211659","SVC_ID":"SVC20170216104530478"},"REQUEST_ID":"20190531141058666"}`)
	doTest(t, jsonStr, "910", "電話已在 KKTV 註冊", "*** TestCreateService  - Case 04: check user has subscribed by phone number ***")

	// Case 05:	check duplicated ORDER_ID
	jsonStr = []byte(`{"PARAM":{"ACTION":"createservice","CONTRACT_ID":"0088008899","MODIFY_DATE":"2019/05/3110:15:20","MSISDN":"0975183999","ORDER_ID":"ORD20190531185211999","SVC_ID":"SVC20170216104530478"},"REQUEST_ID":"20190531141058666"}`)
	doTest(t, jsonStr, "908", "重複申裝", "*** TestCreateService  - Case 05: check duplicated ORDER_ID ***")

	// Case 06:	create order - SVC20170216104530478 - KKTV VIP 1 個月 - free_duration 45 days
	jsonStr = []byte(`{"PARAM":{"ACTION":"createservice","CONTRACT_ID":"0077007777","MODIFY_DATE":"2019/05/3110:15:20","MSISDN":"0912123999","ORDER_ID":"ORD20190531185211777","SVC_ID":"SVC20170216104530478"},"REQUEST_ID":"20190531141058666"}`)
	doTest(t, jsonStr, "000", "作業成功", "*** TestCreateService  - Case 06: create order ***")

}
