package kkpayment

import (
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/model/dbuser"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/broadcasting"
	"github.com/KKTV/kktv-api-v3/pkg/broadcast"
	zlog "github.com/KKTV/kktv-api-v3/pkg/log"
	usermodel "github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/KKTV/kktv-api-v3/pkg/model/events"
	"github.com/KKTV/kktv-api-v3/pkg/render"
	"github.com/KKTV/kktv-api-v3/pkg/runtimelib"
	"github.com/google/uuid"
	"gopkg.in/guregu/null.v3"
)

var (
	//IAPPaymentType mapping the payment type in DB
	IAPPaymentType string = "01"

	sqlIAPS2S = map[string]string{
		"getActiveIAPProductByName": `
			SELECT
				id,
				name,
				country,
				price,
				duration,
				created_at,
				updated_at,
				deleted_at,
				payment_type,
				currency,
				price_no_tax,
				tax_rate,
				item_name,
				item_unit,
				auto_renew,
				active,
				free_duration,
				as_subscribe,
				fee,
				fee_rate,
				payment_type_code,
				purchase_upper_limit_count,
				external_product_id,
				discount_duration,
				discount_price,
				bundle,
				sort,
				category
			FROM
				products
			WHERE
				payment_type = 'iap'
				AND active = TRUE
				AND name = $1;`,

		"getLatestIAPOrder": `
			SELECT
				o.id,
				o.user_id,
				o.product_id,
				o.price::numeric::int,
				o.payment_type,
				o.start_date,
				o.end_date,
				o.order_date,
				o.status,
				o.created_at,
				o.realized_at,
				o.canceled_at,
				o.price_no_tax::numeric::int,
				o.tax_rate,
				o.invoice,
				o.fee,
				o.external_order_id,
				o.info
			FROM
				orders o
				LEFT JOIN users u ON o.user_id = u.id
			WHERE
				o.payment_type = 'iap'
				AND o.user_id = $1
				AND u.revoked_at IS NULL
			ORDER BY
				o.order_date DESC
			LIMIT 1;`,

		"getUserbyLatestTransactionID": `
			SELECT
				u.id,
				date_part('epoch', u.expired_at)::int AS expired_at,
				u.created_at,
				u.role,
				u.auto_renew,
				u.type,
				u.created_by,
				p.iap_receipt_data,
				p.iap_receipt_data_hash,
				date_part('epoch', p.iap_latest_expires_date)::int AS iap_latest_expires_date,
				p.iap_latest_transaction_id
			FROM
				users u
				JOIN payment_info p ON u.id = p.user_id
			WHERE
				p.payment_type = 'iap'
				AND p.iap_latest_transaction_id = $1
				AND u.revoked_at IS NULL;`,

		"getUserbyIAPReceiptDataHash": `
			SELECT
				u.id,
				date_part('epoch', u.expired_at)::int AS expired_at,
				u.created_at,
				u.role,
				u.auto_renew,
				u.type,
				u.created_by,
				p.iap_receipt_data,
				p.iap_receipt_data_hash,
				date_part('epoch', p.iap_latest_expires_date)::int AS iap_latest_expires_date,
				p.iap_latest_transaction_id
			FROM
				users u
				JOIN payment_info p ON u.id = p.user_id
			WHERE
				p.payment_type = 'iap'
				AND p.iap_receipt_data_hash = $1
				AND u.revoked_at IS NULL;`,

		"getUserCountbyOriginalTransactionID": `
			SELECT
				COUNT(DISTINCT o.user_id) AS count
			FROM
				public.orders o
				LEFT JOIN users u ON o.user_id = u.id
			WHERE
				o.payment_type = 'iap'
				AND(o.info ->> 'original_transaction_id' = $1
					OR o.info -> 'receipt' -> 'in_app' #>'{0}'->>'original_transaction_id' = $1
					OR o.info -> 'receipt' ->> 'original_transaction_id' = $1)
				AND u.revoked_at IS NULL;`,

		"getUserbyOriginalTransactionID": `
			SELECT
				u.id,
				date_part('epoch', u.expired_at)::int AS expired_at,
				u.created_at,
				u.role,
				u.auto_renew,
				u.type,
				u.created_by,
				p.iap_receipt_data,
				p.iap_receipt_data_hash,
				date_part('epoch', p.iap_latest_expires_date)::int AS iap_latest_expires_date,
				p.iap_latest_transaction_id
			FROM
				users u
				JOIN orders o ON u.id = o.user_id
				JOIN payment_info p ON u.id = p.user_id
			WHERE
				o.payment_type = 'iap'
				AND(o.info ->> 'original_transaction_id' = $1
					OR o.info -> 'receipt' -> 'in_app' #>'{0}'->>'original_transaction_id' = $1
					OR o.info -> 'receipt' ->> 'original_transaction_id' = $1)
				AND u.revoked_at IS NULL
			ORDER BY
				u.created_at DESC
			LIMIT 1;`,

		"saveNotification": `
			INSERT INTO server_notifications
				(user_id, product_id, order_id, payment_type, notification_type, created_at, payload,
					cancellation_reason, canceled_at, is_in_billing_retry_period, expiration_intent,
					expires_date, grace_period_expires_date, external_order_id, auto_renew_status_change_date, auto_renew_status)
			VALUES
				(:user_id, :product_id, :order_id, :payment_type, :notification_type, NOW(), :payload,
					:cancellation_reason, :canceled_at, :is_in_billing_retry_period, :expiration_intent,
					:expires_date, :grace_period_expires_date, :external_order_id, :auto_renew_status_change_date, :auto_renew_status);`,
	}
)

// S2SPayload struct for POST body
type S2SPayload struct {
	AutoRenewAdamID              string         `json:"auto_renew_adam_id"`
	AutoRenewProductID           string         `json:"auto_renew_product_id"`
	AutoRenewStatus              string         `json:"auto_renew_status"`
	AutoRenewStatusChangeDate    string         `json:"auto_renew_status_change_date"`
	AutoRenewStatusChangeDateMs  string         `json:"auto_renew_status_change_date_ms"`
	AutoRenewStatusChangeDatePst string         `json:"auto_renew_status_change_date_pst"`
	CancellationDate             string         `json:"cancellation_date"`
	CancellationDateMs           string         `json:"cancellation_date_ms"`
	CancellationDatePst          string         `json:"cancellation_date_pst"`
	Environment                  string         `json:"environment"`
	ExpirationIntent             string         `json:"expiration_intent"`
	NotificationType             string         `json:"notification_type"`
	Password                     string         `json:"password"`
	UnifiedReceipt               UnifiedReceipt `json:"unified_receipt"`
	WebOrderLineItemID           string         `json:"web_order_line_item_id"`
	Product                      *dbuser.Product
}

// ReceiptInfo struct for POST body
type ReceiptInfo struct {
	AppItemID                   string `json:"app_item_id"`
	Bid                         string `json:"bid"`
	Bvrs                        string `json:"bvrs"`
	CancellationDate            string `json:"cancellation_date"`
	CancellationDateMs          string `json:"cancellation_date_ms"`
	CancellationDatePst         string `json:"cancellation_date_pst"`
	CancellationReason          string `json:"cancellation_reason"`
	ExpiresDate                 string `json:"expires_date"`
	ExpiresDateFormatted        string `json:"expires_date_formatted"`
	ExpiresDateFormattedPst     string `json:"expires_date_formatted_pst"`
	ExpiresDateMs               string `json:"expires_date_ms"`
	ExpiresDatePst              string `json:"expires_date_pst"`
	IsInIntroOfferPeriod        string `json:"is_in_intro_offer_period"`
	IsTrialPeriod               string `json:"is_trial_period"`
	ItemID                      string `json:"item_id"`
	OriginalPurchaseDate        string `json:"original_purchase_date"`
	OriginalPurchaseDateMs      string `json:"original_purchase_date_ms"`
	OriginalPurchaseDatePst     string `json:"original_purchase_date_pst"`
	OriginalTransactionID       string `json:"original_transaction_id"`
	ProductID                   string `json:"product_id"`
	PurchaseDate                string `json:"purchase_date"`
	PurchaseDateMs              string `json:"purchase_date_ms"`
	PurchaseDatePst             string `json:"purchase_date_pst"`
	Quantity                    string `json:"quantity"`
	SubscriptionGroupIdentifier string `json:"subscription_group_identifier"`
	TransactionID               string `json:"transaction_id"`
	UniqueIdentifier            string `json:"unique_identifier"`
	UniqueVendorIdentifier      string `json:"unique_vendor_identifier"`
	VersionExternalIdentifier   string `json:"version_external_identifier"`
	WebOrderLineItemID          string `json:"web_order_line_item_id"`
}

// UnifiedReceipt struct for POST body
type UnifiedReceipt struct {
	Environment        string               `json:"environment"`
	LatestReceipt      string               `json:"latest_receipt"`
	LatestReceiptInfo  []ReceiptInfo        `json:"latest_receipt_info"`
	PendingRenewalInfo []PendingRenewalInfo `json:"pending_renewal_info"`
	Status             int                  `json:"status"`
}

// PendingRenewalInfo struct for POST body
type PendingRenewalInfo struct {
	AutoRenewProductID        string `json:"auto_renew_product_id"`
	AutoRenewStatus           string `json:"auto_renew_status"`
	ExpirationIntent          string `json:"expiration_intent"`
	GracePeriodExpiresDate    string `json:"grace_period_expires_date"`
	GracePeriodExpiresDateMs  string `json:"grace_period_expires_date_ms"`
	GracePeriodExpiresDatePst string `json:"grace_period_expires_date_pst"`
	IsInBillingRetryPeriod    string `json:"is_in_billing_retry_period"`
	OriginalTransactionID     string `json:"original_transaction_id"`
	PriceConsentStatus        string `json:"price_consent_status"`
	ProductID                 string `json:"product_id"`
}

// PostIAPS2S handling data from Apple App Store Server to Server notification via HTTP POST
func PostIAPS2S(w http.ResponseWriter, r *http.Request) {
	var err error
	var p S2SPayload

	decoder := json.NewDecoder(r.Body)
	if err = decoder.Decode(&p); err != nil {
		logErr(fmt.Sprintf("Decode json payload failed. payload: %s", r.Body), err)
		return
	}

	zlog.Info(runtimelib.GetFuncName(1)+": Received server to server notification").
		Interface("payload", p).Send()

	// Check Environment: return success if incoming notification is not for PROD environment
	if p.Environment != "PROD" {
		render.JSON(w, http.StatusOK, "")
		return
	}

	var transactionID, originalTransactionID string
	if len(p.UnifiedReceipt.LatestReceiptInfo) > 0 {
		// Use latest transaction_id to see if it matches a user's subscription in DB.
		transactionID = p.UnifiedReceipt.LatestReceiptInfo[0].TransactionID
		originalTransactionID = p.UnifiedReceipt.LatestReceiptInfo[0].OriginalTransactionID
	}

	// Try to get user by transaction id, original transaction id, latest transaction id
	u, err := p.getUser()
	if err != nil {
		logWarn(fmt.Sprintf("Get user failed. notification_type: %s, transaction_id: %s, original_transaction_id: %s",
			p.NotificationType, transactionID, originalTransactionID), err)
	}

	// Get user's lastest iap order
	var o *dbuser.Order
	if u != nil {
		if o, err = p.getLatestIAPOrder(u.ID); err != nil {
			logWarn(fmt.Sprintf("Latest order not found. notification_type: %s, user_id: %s",
				p.NotificationType, u.ID), err)
		}
	}

	// Save received notification to server_notifications for record
	if err = saveNotification(&p, u, o); err != nil {
		logWarn(fmt.Sprintf("Save server notifications. notification_type: %s",
			p.NotificationType), err)
	}

	// 找不到 user 或 user 最後的 iap 訂單時結束流程
	// 表示不處理新訂閱的狀況，新訂閱應該都是透過 client 通過 API 打 receipt 進來
	if u == nil || o == nil {
		logWarn(fmt.Sprintf("New IAP subscription user. notification_type: %s",
			p.NotificationType), err)
		render.JSON(w, http.StatusOK, "")
		return
	}

	// 檢查每種 notification type 共同的 key
	// ** 檢查完看起來沒有實際使用裡面的資訊來更新用戶資訊，因為後續是用 DB 存的 receipt 重拉資訊回來更新用戶資訊
	if err = p.checkCommonKeys(); err != nil {
		logWarn(fmt.Sprintf("Check common keys failed. notification_type: %s",
			p.NotificationType), err)
	}

	// 檢查每種 notification type 個別需要的 key
	// ** 檢查完看起來沒有實際使用裡面的資訊來更新用戶資訊，因為後續是用 DB 存的 receipt 重拉資訊回來更新用戶資訊
	err = p.checkRequiredKeysByNotificationType()
	if err != nil {
		logWarn(fmt.Sprintf("Check required keys failed. notification_type: %s",
			p.NotificationType), err)
	}

	// 查詢 IAP 產品方案
	product, err := p.getActiveIAPProductByName()
	if err != nil {
		logWarn(fmt.Sprintf("Get active IAP product by name failed. notification_type: %s",
			p.NotificationType), err)
	}
	p.Product = product

	// 以下流程目前看起來沒有實際根據收到 notification type 進行處理 ( 除了 REFUND )，
	// 而是透過已存的 iap receipt 重新向 App Store 取得用戶的訂閱資料
	iapUser, err := NewIAPUser(u.ID)
	if err != nil {
		logWarn(fmt.Sprintf("New IAP user error. notification_type: %s, user_id: %s",
			p.NotificationType, u.ID), err)
	}

	// 只有判斷 notification type 為 REFUND 時會進行個別處理 (取消用戶的 IAP 未實現續訂單)，
	// 其餘的流程皆比照 client 打 iap receipt 上來的方式處理
	// Ref: https://developer.apple.com/in-app-purchase/
	// 根據上述官方文件，REFUND 事件只發生在以下幾種 in-app purchase 類型，所以目前可能僅有 10 天 70 元方案會發生
	// * consumable in-app purchase
	// * non-consumable in-app purchase
	// * non-renewing subscription
	rid := uuid.New().String()
	broadcaster := broadcasting.GetBroadcaster()
	broadcaster.SyncEmit(events.SignalUserWillBeUpdated, &events.UserWillBeUpdatedEvent{
		RequestID: rid, UserID: u.ID,
	})
	if p.NotificationType == "REFUND" {
		// 取消用戶的 IAP 未實現續訂單
		iapUser.CancelOrder()
		broadcastUserUpdate(broadcaster, u.ID, rid, p.NotificationType, true)
		render.JSON(w, http.StatusOK, "")
		return
	}

	// 重新用 DB 已存的 iap receipt 向 App Store 請求 verifyReceipt，
	// 並根據 verifyReceipt 回傳的訂閱資料更新使用者狀態 ( 流程同 client 使用的 v3 PostIAP API )
	err = iapUser.Deal()

	if err != nil {
		zlog.Error("PostIAPS2S: deal fail").Err(err).Str("user_id", u.ID).Interface("payload", p).Send()
	} else {
		broadcastUserUpdate(broadcaster, u.ID, rid, p.NotificationType, false)
	}

	render.JSON(w, http.StatusOK, "")
}

func broadcastUserUpdate(broadcaster broadcast.EventManager, userID string, rid string, notificationType string, isCanceled bool) {

	var updateReason string
	if isCanceled {
		updateReason = "iap server2server cancel order"
	} else {
		updateReason = "upgrade via iap server2server payment "
	}

	broadcaster.Emit(events.SignalUserUpdated, &events.UserUpdatedEvent{
		RequestID:    rid,
		UserID:       userID,
		UpdateReason: updateReason,
		ModifiedBy:   usermodel.AuditModifierTypeSystem.String(),
	})
}

func (p *S2SPayload) checkRequiredKeysByNotificationType() (err error) {

	// Ref: https://developer.apple.com/documentation/appstoreservernotifications/notification_type
	switch p.NotificationType {
	case "CANCEL":
		// Indicates that Apple Support canceled the auto-renewable subscription and the customer received a refund as of the timestamp in cancellation_date_ms.
		if len(p.CancellationDateMs) == 0 || len(p.CancellationDate) == 0 {
			return errors.New("cancellation_date key is empty")
		}

	case "DID_CHANGE_RENEWAL_STATUS":
		// Indicates a change in the subscription renewal status. In the JSON response,
		// check auto_renew_status_change_date_ms to retrieve the date and time of the last status update. Check auto_renew_status to get the current renewal status.
		if len(p.AutoRenewStatusChangeDate) == 0 {
			return errors.New("auto_renew_status_change_date key is empty")
		}

		if len(p.UnifiedReceipt.LatestReceiptInfo[0].PurchaseDateMs) == 0 {
			return errors.New("purchase_date_ms key is empty")
		}

		if len(p.AutoRenewStatus) == 0 {
			return errors.New("auto_renew_status key is empty")
		}

	case "DID_FAIL_TO_RENEW":
		// Indicates a subscription that failed to renew due to a billing issue.
		// Check is_in_billing_retry_period to retrieve the current retry status of the subscription.
		// Check grace_period_expires_date to get the new service expiration date if the subscription is in a billing grace period.
		if len(p.UnifiedReceipt.PendingRenewalInfo) == 0 || len(p.UnifiedReceipt.PendingRenewalInfo[0].IsInBillingRetryPeriod) == 0 {
			return errors.New("is_in_billing_retry_period key is empty")
		}

		if len(p.UnifiedReceipt.LatestReceiptInfo) == 0 || len(p.UnifiedReceipt.LatestReceiptInfo[0].ExpiresDateMs) == 0 {
			return errors.New("expires_date key is empty")
		}

	case "DID_RECOVER":
		// Indicates a successful automatic renewal of an expired subscription that failed to renew in the past.
		// Check expires_date to determine the next renewal date and time.
		if len(p.UnifiedReceipt.LatestReceiptInfo) == 0 || len(p.UnifiedReceipt.LatestReceiptInfo[0].ExpiresDateMs) == 0 {
			return errors.New("expires_date key is empty")
		}

		if len(p.UnifiedReceipt.LatestReceiptInfo[0].PurchaseDateMs) == 0 {
			return errors.New("purchase_date_ms key is empty")
		}

	case "INITIAL_BUY":
		// Occurs at the user’s initial purchase of the subscription.
		// Store latest_receipt on your server as a token to verify the user’s subscription status at any time by validating it with the App Store.
		if len(p.UnifiedReceipt.LatestReceiptInfo) > 0 && len(p.UnifiedReceipt.LatestReceiptInfo[0].PurchaseDate) == 0 {
			return errors.New("purchase_date key is empty")
		}

	case "INTERACTIVE_RENEWAL":
		// Indicates the customer renewed a subscription interactively,
		// either by using your app’s interface,
		// or on the App Store in the account’s Subscriptions settings.
		// Make service available immediately.
		if len(p.AutoRenewStatus) == 0 {
			return errors.New("auto_renew_status key is empty")
		}

	case "REFUND":
		// Indicates that the App Store successfully refunded a transaction for a consumable in-app purchase,
		// a non-consumable in-app purchase, or a non-renewing subscription.
		// The cancellation_date_ms contains the timestamp of the refunded transaction.
		// The original_transaction_id and product_id identify the original transaction and product.
		// The cancellation_reason contains the reason.
		if len(p.UnifiedReceipt.LatestReceiptInfo) > 0 && len(p.UnifiedReceipt.LatestReceiptInfo[0].CancellationDateMs) == 0 {
			return errors.New("cancellation_date_ms key is empty")
		}

	case "DID_CHANGE_RENEWAL_PREF":
		// Indicates that the customer made a change in their subscription plan that takes effect at the next renewal.
		// The currently active plan isn’t affected.
		// Check the auto_renew_product_id field in unified_receipt.Pending_renewal_info to retrieve the product identifier for the product the customer’s subscription renews.

	case "DID_RENEW":
		// Indicates that a customer’s subscription has successfully auto-renewed for a new transaction period.
		// Provide the customer with access to the subscription’s content or service.
	}

	return nil
}

func (p *S2SPayload) checkCommonKeys() (err error) {
	if len(p.UnifiedReceipt.PendingRenewalInfo) == 0 && p.UnifiedReceipt.PendingRenewalInfo[0].OriginalTransactionID == "" {
		return errors.New("original_transaction_id key is empty")
	}

	if len(p.AutoRenewProductID) == 0 {
		return errors.New("product_id key is empty")
	}

	if len(p.UnifiedReceipt.LatestReceiptInfo) == 0 || len(p.UnifiedReceipt.LatestReceiptInfo[0].TransactionID) == 0 {
		return errors.New("p.UnifiedReceipt.LatestReceiptInfo[0].TransactionID key is empty")
	}

	return nil
}

func (p *S2SPayload) getActiveIAPProductByName() (product *dbuser.Product, err error) {
	pID := p.UnifiedReceipt.LatestReceiptInfo[0].ProductID
	db := kkapp.App.DbUser.Slave()
	product = new(dbuser.Product)
	if err = db.Get(product, sqlIAPS2S["getActiveIAPProductByName"], pID); err != nil {
		return nil, err
	}
	return product, err
}

func (p *S2SPayload) getUser() (u *IAPUser, err error) {

	var originalTransactionID string
	var latestTransactionID string

	lenR := len(p.UnifiedReceipt.LatestReceiptInfo)
	if lenR > 0 {
		originalTransactionID = p.UnifiedReceipt.LatestReceiptInfo[0].OriginalTransactionID
		latestTransactionID = p.UnifiedReceipt.LatestReceiptInfo[lenR-1].TransactionID
	}

	// Try to match user by iap_latest_transaction_id in payment_info table
	if u, err = p.getUserbyLatestTransactionID(latestTransactionID); err != nil {
		logWarn(fmt.Sprintf("Get user by latest transaction id failed. latest_transaction_id: %s", latestTransactionID), err)
	}

	if u == nil {
		// Try to match user by iap_receipt_data_hash in payment_info table
		if u, err = p.getUserbyIAPReceiptDataHash(originalTransactionID); err != nil {
			logWarn(fmt.Sprintf("Get user by iap_receipt_data_hash failed. original_transaction_id: %s", originalTransactionID), err)
		}
		if u != nil {
			return u, nil
		}
	}

	if u == nil {
		var activeUserCount int64

		// ### Important
		// * 相同 original_transaction_id 可能綁定在多個 user 的訂單上，此處查詢應加上 revoked_at IS NULL 作為條件
		// > 例如：user revoke 舊的 iap 帳號，某天又在開新帳號後重新訂閱方案，
		// > 此時系統收到 app store 提供的 original_transaction_id 有機會拿到與先前舊帳號的訂單一樣
		if activeUserCount, err = p.getUserCountbyOriginalTransactionID(originalTransactionID); err != nil {
			logWarn(fmt.Sprintf("Get user count by original transaction id failed. original_transaction_id: %s", originalTransactionID), err)
		}

		// 有效帳號數量 > 1
		if activeUserCount > 1 {
			return nil, errors.New("get user count by original_transaction_id should be one but there are more users")
		}

		// 有效帳號數量 == 0
		if activeUserCount == 0 {
			return nil, errors.New("get user count by original_transaction_id should be one but we get 0")
		}
	}

	if u, err = p.getUserbyOriginalTransactionID(originalTransactionID); err != nil {
		return nil, errors.New("get user by original_transaction_id. " + err.Error())
	}

	return u, nil
}

func (p *S2SPayload) getUserbyLatestTransactionID(tID string) (user *IAPUser, err error) {
	user = new(IAPUser)
	db := kkapp.App.DbUser.Slave()
	err = db.Get(user, sqlIAPS2S["getUserbyLatestTransactionID"], tID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("[ERROR] - %s - user is empty. Error:%s", p.NotificationType, err.Error())
		}
		return nil, fmt.Errorf("[ERROR] - %s - get user by latest transaction ID failed. Error:%s", p.NotificationType, err.Error())
	}

	return user, nil
}

func (p *S2SPayload) getUserbyIAPReceiptDataHash(tID string) (user *IAPUser, err error) {
	user = new(IAPUser)
	db := kkapp.App.DbUser.Slave()
	err = db.Get(user, sqlIAPS2S["getUserbyIAPReceiptDataHash"], tID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("[ERROR] - %s - user is empty. Error:%s", p.NotificationType, err.Error())
		}
		return nil, fmt.Errorf("[ERROR] - %s - get user by iap_receipt_data_hash failed. Error:%s", p.NotificationType, err.Error())
	}

	return user, nil
}

func (p *S2SPayload) getUserCountbyOriginalTransactionID(tID string) (userCount int64, err error) {
	db := kkapp.App.DbUser.Slave()
	err = db.Get(&userCount, sqlIAPS2S["getUserCountbyOriginalTransactionID"], tID)
	if err != nil {
		if err == sql.ErrNoRows {
			return 0, fmt.Errorf("[ERROR] - %s - user count is empty. Error:%s", p.NotificationType, err.Error())
		}
		return 0, fmt.Errorf("[ERROR] - %s - get user count failed. Error:%s", p.NotificationType, err.Error())
	}

	return userCount, nil
}

func (p *S2SPayload) getUserbyOriginalTransactionID(transactionID string) (user *IAPUser, err error) {
	user = new(IAPUser)
	db := kkapp.App.DbUser.Slave()
	err = db.Get(user, sqlIAPS2S["getUserbyOriginalTransactionID"], transactionID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("[ERROR] - %s - user ID is empty. Error:%s", p.NotificationType, err.Error())
		}
		return nil, fmt.Errorf("[ERROR] - %s - get user ID failed. Error:%s", p.NotificationType, err.Error())
	}

	return user, nil
}

// saveNotification
func saveNotification(p *S2SPayload, u *IAPUser, o *dbuser.Order) (err error) {
	var j []byte
	if j, err = json.Marshal(p); err != nil {
		return err
	}

	n := new(NotificationLog)
	// user_id is required to server_notifications table
	if u == nil || u.ID == "" {
		return errors.New("user_id is empty")
	}

	n.UserID = u.ID
	n.Payload = string(j)
	n.PaymentType = "iap"
	n.NotificationType = p.NotificationType
	n.ProductID = p.UnifiedReceipt.LatestReceiptInfo[0].ProductID
	n.ExternalOrderID = p.UnifiedReceipt.LatestReceiptInfo[0].TransactionID
	n.CancellationReason = p.UnifiedReceipt.LatestReceiptInfo[0].CancellationReason

	// order_id is optional to server_notifications table
	if o != nil && o.ID != "" {
		n.OrderID = null.NewString(o.ID, true)
	}

	if p.UnifiedReceipt.LatestReceiptInfo[0].CancellationDateMs != "" {
		// set canceled_at only when no error occurs
		if iCanceledAt, err := strconv.ParseInt(p.UnifiedReceipt.LatestReceiptInfo[0].CancellationDateMs, 10, 64); err == nil {
			n.CanceledAt = time.Unix(0, iCanceledAt*int64(time.Millisecond)).UTC()
		}
	}

	if p.UnifiedReceipt.LatestReceiptInfo[0].ExpiresDateMs != "" {
		// set expires_date only when no error occurs
		if iExpiresDate, err := strconv.ParseInt(p.UnifiedReceipt.LatestReceiptInfo[0].ExpiresDateMs, 10, 64); err == nil {
			n.ExpiresDate = time.Unix(0, iExpiresDate*int64(time.Millisecond)).UTC()
		}
	}

	if p.AutoRenewStatusChangeDateMs != "" {
		// set auto_renew_status_change_date only when no error occurs
		if iAutoRenewStatusChangeDate, err := strconv.ParseInt(p.AutoRenewStatusChangeDateMs, 10, 64); err == nil {
			n.AutoRenewStatusChangeDate = time.Unix(0, iAutoRenewStatusChangeDate*int64(time.Millisecond)).UTC()
		}
	}

	if p.AutoRenewStatus != "" {
		// set auto_renew_status only when no error occurs
		if iAutoRenewStatus, err := strconv.ParseBool(p.AutoRenewStatus); err == nil {
			n.AutoRenewStatus = iAutoRenewStatus
		}
	}

	// ### expiration_intent values:
	// * 1: The customer canceled their subscription.
	// * 2: Billing error; for example, the customer’s payment information is no longer valid.
	// * 3: The customer didn’t consent to an auto-renewable subscription price increase that requires customer consent, allowing the subscription to expire.
	// * 4: The product wasn’t available for purchase at the time of renewal.
	// * 5: The subscription expired for some other reason.
	// > Ref: https://developer.apple.com/documentation/appstorereceipts/expiration_intent
	if p.UnifiedReceipt.PendingRenewalInfo[0].ExpirationIntent != "" {
		n.ExpirationIntent = p.UnifiedReceipt.PendingRenewalInfo[0].ExpirationIntent
	}

	if p.UnifiedReceipt.PendingRenewalInfo[0].IsInBillingRetryPeriod == "1" {
		n.IsInBillingRetryPeriod = true
	} else {
		n.IsInBillingRetryPeriod = false
	}

	if p.UnifiedReceipt.PendingRenewalInfo[0].GracePeriodExpiresDateMs != "" {
		// set grace_period_expires_date only when no error occurs
		if iGracePeriodExpiresDate, err := strconv.ParseInt(p.UnifiedReceipt.PendingRenewalInfo[0].GracePeriodExpiresDateMs, 10, 64); err == nil {
			n.GracePeriodExpiresDate = time.Unix(0, iGracePeriodExpiresDate*int64(time.Millisecond)).UTC()
		}
	}

	db := kkapp.App.DbUser.Master()
	if _, err = db.NamedExec(sqlIAPS2S["saveNotification"], n); err != nil {
		return err
	}

	return
}

func (p *S2SPayload) getLatestIAPOrder(userID string) (o *dbuser.Order, err error) {
	o = new(dbuser.Order)
	db := kkapp.App.DbUser.Slave()
	err = db.Get(o, sqlIAPS2S["getLatestIAPOrder"], userID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("[ERROR] - %s - no order found! query by userID:%s - Error:%s", p.NotificationType, userID, err.Error())
		}
		return nil, fmt.Errorf("[ERROR] - %s - get order failed! query by userID:%s - Error:%s", p.NotificationType, userID, err.Error())
	}

	return o, nil
}

func logWarn(topic string, err error) {
	zlog.Warn(runtimelib.GetFuncName(2) + ": " + topic).
		Err(err).
		Send()
}

func logErr(topic string, err error) {
	zlog.Error(runtimelib.GetFuncName(2) + ": " + topic).
		Err(err).
		Send()
}
