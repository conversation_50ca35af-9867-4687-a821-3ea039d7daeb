package kkpayment

import (
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"log"
	"net/http"
	"net/url"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/amplitude"
	"github.com/KKTV/kktv-api-v3/kkapp/facebook"
	"github.com/KKTV/kktv-api-v3/kkapp/kkpayment/email"
	"github.com/KKTV/kktv-api-v3/kkapp/kkutil"
	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
	"github.com/KKTV/kktv-api-v3/kkapp/model/dbuser"
	"github.com/KKTV/kktv-api-v3/kkapp/model/playback"
	"github.com/KKTV/kktv-api-v3/kkapp/sns"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/broadcasting"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/kktverror"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/permission"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/wrapper"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/wrapper/middleware"
	"github.com/KKTV/kktv-api-v3/pkg/datetimer"
	zlog "github.com/KKTV/kktv-api-v3/pkg/log"
	usermodel "github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/KKTV/kktv-api-v3/pkg/model/events"
	modelmw "github.com/KKTV/kktv-api-v3/pkg/model/middleware"
	"github.com/KKTV/kktv-api-v3/pkg/payment"
	"github.com/KKTV/kktv-api-v3/pkg/validator"
	"github.com/google/uuid"
	"github.com/jmoiron/sqlx"
	"github.com/slack-go/slack"
	"gopkg.in/guregu/null.v3"
)

const (
	// creditCardPaymentType = "00"
	creditCardPaymentType = "credit_card"
)

var (
	reMobileBardCode             = regexp.MustCompile(`^/[0-9A-Z+.-]{7}$`)
	reCitizenPersonalCertificate = regexp.MustCompile(`^[A-Z]{2}[0-9]{14}$`)

	sqlcredit = map[string]string{

		"stoporder": `UPDATE orders SET status = 'cancel', canceled_at = NOW() WHERE user_id = $1
 AND realized_at IS NULL AND status IS NULL AND price > 0::money;`,

		"unsubscribe": `UPDATE users SET auto_renew = false, updated_at = NOW() WHERE id = $1`,

		"createorder": `INSERT INTO orders
 (id, user_id, product_id, payment_type, start_date, end_date, order_date, price, price_no_tax, tax_rate, fee) VALUES
 (:id, :user_id, :product_id, :payment_type, :start_date, :end_date, :order_date, :price, :price_no_tax, :tax_rate, :fee);`,

		"updateorder": `UPDATE orders SET status = :status, info = :info, realized_at = :realized_at, price = :price WHERE id = :id;`,

		"productByName": `SELECT id, name, duration, free_duration, price::numeric::int, price_no_tax::numeric::int, fee, tax_rate, payment_type, payment_type_code,
		auto_renew, as_subscribe, item_name, item_unit, external_product_id, discount_duration, discount_price::numeric::int, discount_price_no_tax::numeric::int, discount_fee, bundle FROM
		products WHERE name = $1 AND payment_type = $2 AND active = true;`,

		"productByID": `SELECT id, name, duration, free_duration, price::numeric::int, price_no_tax::numeric::int, fee, tax_rate, payment_type, payment_type_code,
		auto_renew, as_subscribe, item_name, item_unit, external_product_id, discount_duration, discount_price::numeric::int, discount_price_no_tax::numeric::int, discount_fee, bundle FROM
		products WHERE id = $1;`,

		"failorder": `UPDATE orders SET status = :status, info = :info WHERE id = :id AND user_id = :user_id AND payment_type = :payment_type`,

		"failrestorder": `UPDATE orders SET status = :status, info = :info, canceled_at = NOW() WHERE status IS NULL AND user_id = :user_id AND payment_type = :payment_type`,

		"order": `SELECT id, user_id, product_id, price::numeric::int, payment_type,
 start_date, end_date, order_date,
 status, created_at, realized_at, canceled_at,
 price_no_tax::numeric::int, tax_rate, invoice, fee, external_order_id, info FROM orders WHERE id = $1;`,

		"reneworder": `SELECT
				o.id,
				o.user_id,
				o.product_id,
				o.price::numeric::int,
				o.payment_type,
				o.start_date,
				o.end_date,
				o.order_date,
				o.status,
				o.created_at,
				o.realized_at,
				o.canceled_at,
				o.price_no_tax::numeric::int,
				o.tax_rate,
				o.invoice,
				o.fee,
				o.external_order_id,
				o.info,
				p.name AS product_name
			FROM orders o
			LEFT JOIN products p ON o.product_id = p.id
			WHERE
				o.user_id = $1
				AND o.payment_type = $2
				AND o.order_date > Date(NOW()) - interval '1 day'
				AND o.order_date <= Date(NOW()) + interval '1 day'
				AND o.status IS NULL
			ORDER BY o.order_date
			LIMIT 1;`,

		"nextorder": `SELECT id, user_id, product_id, price::numeric::int, payment_type,
 start_date, end_date, order_date,
 status, created_at, realized_at, canceled_at,
 price_no_tax::numeric::int, tax_rate, invoice, fee, external_order_id, info FROM orders WHERE
 user_id = $1 AND payment_type = $2 AND
 order_date >= $3 AND status IS NULL ORDER BY order_date LIMIT 1;`,

		"getfamily": `SELECT user_id FROM payment_info WHERE family_id = $1`,

		"user": `SELECT
			u.id, u.email, u.phone, u.gender, u.birthday, date_part('epoch', u.expired_at)::int as expired_at,
			u.created_at, u.role, u.auto_renew, u.type, u.created_by,
			payment.email as payment_email, payment.credit_card_token_value, payment.credit_card_token_term,
			payment.credit_card_6no, payment.credit_card_4no,
			payment.caring_code, payment.recipient, payment.recipient_address, payment.carrier_type, payment.carrier_value,
			payment.family_id,
			EXISTS (
				SELECT 1
				FROM orders
				WHERE user_id = $1 AND status IN ('ok', 'cancel', 'in_progress', 'refund')
				AND price > 0::money LIMIT 1
			) AS as_subscribe
		FROM users u
		LEFT JOIN payment_info payment
		ON payment.user_id = u.id
		WHERE u.id = $1`,

		"saveNotification": `
		   INSERT INTO server_notifications (user_id, product_id, order_id, payment_type, notification_type, created_at, is_in_billing_retry_period, expires_date, grace_period_expires_date)
		   VALUES (:user_id, :product_id, :order_id, :payment_type, :notification_type, NOW(), :is_in_billing_retry_period, :expires_date, :grace_period_expires_date);`,

		"extendGracePeriod": `UPDATE orders SET order_date = order_date + interval '7 day', status = NULL, id = $2 WHERE id = $1;`,

		"graced": `SELECT COUNT(1) FROM server_notifications WHERE payment_type='credit_card' AND user_id=$1 AND DATE(grace_period_expires_date) = DATE($2);`,

		"latestorder": `SELECT id, user_id, product_id, price::numeric::int, payment_type,
 start_date, end_date, order_date,
 status, created_at, realized_at, canceled_at,
 price_no_tax::numeric::int, tax_rate, invoice, fee, external_order_id, info FROM orders WHERE
 user_id = $1
 AND payment_type = $2
 AND (order_date < (NOW() + interval '8 days'))
 AND status IS NULL
 ORDER BY order_date DESC LIMIT 1`,

		"updateNotification": `UPDATE server_notifications SET created_at=(created_at - interval '10 days') WHERE user_id=$1 AND payment_type=$2;`,
	}
)

//type modifyUser struct {
//ID        string `db:"id" identity:"true"`
//AutoRenew bool   `db:"auto_renew"`
//Reason    string `db:"reason"`
//}

// CreditCardUnsubscribe unsubscribe
func CreditCardUnsubscribe(userID, reason string, consoleUser dbmeta.ConsoleUser) (err error) {
	db := kkapp.App.DbUser.Master()
	_, err = db.Exec(sqlcredit["stoporder"], userID)
	if err != nil {
		return
	}
	// send amplitude
	event, _ := amplitude.NewAccountTransactionCancelled(userID, "credit_card", reason)
	go event.Send()
	return err
}

// credit card config
var (
	creditCardConfig = ccConfig{
		// debug false (prod)
		false: creditCardConfigObj{
			MerchantID:        "MS3857712",
			GatewayURL:        "https://core.newebpay.com/API/CreditCard",
			GatewayCancelURL:  "https://core.newebpay.com/API/CreditCard/Cancel",
			GatewayHashKey:    "0voaziENd6FxNOse5S1Fmjin3wTIa60a",
			GatewayHashIV:     "BolbHZYU5EsTtU8z",
			InvoiceMerchantID: "********",
			InvoiceURL:        "https://inv.ezpay.com.tw/API/invoice_issue",
			InvoiceSearchURL:  "https://inv.ezpay.com.tw/API/invoice_search",
			InvoiceHashKey:    "cMZ0CU4LLcFTM0U2WDhN4R8UUNJnwGKI",
			InvoiceHashIV:     "tKI5ENRqZwkMxePu",
			PaymentErrorTopic: "arn:aws:sns:ap-northeast-1:************:kktv-prod-payment-error-notification",
			PaymentErrorSlack: "#kktv-log-prod-payment-err",
		},
		// debug true (test, stage)
		true: creditCardConfigObj{
			MerchantID:        "MS3217035",
			GatewayURL:        "https://ccore.newebpay.com/API/CreditCard",
			GatewayCancelURL:  "https://ccore.newebpay.com/API/CreditCard/Cancel",
			GatewayHashKey:    "RIMyU4QSf2F8D7ic53rNtyXNy6YvJnqB",
			GatewayHashIV:     "6d91h0Uw2zFGHXTN",
			InvoiceMerchantID: "31576568",
			InvoiceURL:        "https://cinv.ezpay.com.tw/API/invoice_issue",
			InvoiceSearchURL:  "https://cinv.ezpay.com.tw/API/invoice_search",
			InvoiceHashKey:    "6vIvMoUM73VcNRt4vcHlN9bAtXyEt7vz",
			InvoiceHashIV:     "Dd5qBZcJ27VQZoVu",
			PaymentErrorTopic: "arn:aws:sns:ap-northeast-1:************:kktv-test-payment-error-notification",
			PaymentErrorSlack: "#kktv-log-test-payment-err",
		},
	}
)

// handle credit card config at same package
type creditCardConfigObj struct {
	MerchantID        string
	GatewayURL        string
	GatewayCancelURL  string
	GatewayHashKey    string
	GatewayHashIV     string
	InvoiceURL        string
	InvoiceSearchURL  string
	InvoiceMerchantID string
	InvoiceHashKey    string
	InvoiceHashIV     string
	PaymentErrorTopic string
	PaymentErrorSlack string
}

// credit card config
// debug true or false config
type ccConfig map[bool]creditCardConfigObj

// spaResult spagateway response.Result
type spaResult struct {
	MerchantID      string `json:"MerchantID"`
	Amt             int64  `json:"Amt"`
	TradeNo         string `json:"TradeNo"`
	MerchantOrderNo string `json:"MerchantOrderNo"`
	RespondCode     string `json:"RespondCode"`
	Auth            string `json:"Auth"`
	AuthDate        string `json:"AuthDate"`
	AuthTime        string `json:"AuthTime"`
	Card6No         string `json:"Card6No"`
	Card4No         string `json:"Card4No"`
	Exp             string `json:"Exp"`
	ECI             string `json:"ECI"`
	IP              string `json:"IP"`
	EscrowBank      string `json:"EscrowBank"`
	TokenLife       string `json:"TokenLife"`
	TokenValue      string `json:"TokenValue"`
	CheckCode       string `json:"CheckCode"`
}

// invoiceResult ezpay response.Result
type invoiceResult struct {
	MerchantID      string      `json:"MerchantID"`
	InvoiceTransNo  string      `json:"InvoiceTransNo"`
	MerchantOrderNo string      `json:"MerchantOrderNo"`
	TotalAmt        interface{} `json:"TotalAmt"`
	InvoiceNumber   string      `json:"InvoiceNumber"`
	RandomNum       string      `json:"RandomNum"`
	CheckCode       string      `json:"CheckCode"`
	CreateTime      string      `json:"CreateTime"`
	BarCode         string      `json:"BarCode"`
	QRcodeL         string      `json:"QRcodeL"`
	QRcodeR         string      `json:"QRcodeR"`
}

// spaResponse spagateway response
type spaResponse struct {
	Status  string          `json:"Status"`
	Message string          `json:"Message"`
	Result  json.RawMessage `json:"Result"`
}

// invoiceResponse ezpay invoice response
type invoiceResponse struct {
	Status  string `json:"Status"`
	Message string `json:"Message"`
	Result  string `json:"Result"`
}

// CreditCardPost api client post request
type CreditCardPost struct {
	Email              string `json:"email"`
	CardNo             string `json:"card_no"`
	CardExp            string `json:"card_exp"`
	CardCvc            string `json:"card_cvc"`
	CaringCode         string `json:"caring_code"`
	Recipient          string `json:"recipient"`
	RecipientAddress   string `json:"recipient_address"`
	CarrierType        string `json:"carriers_type"`
	CarrierValue       string `json:"carriers_value"`
	ProductName        string `json:"product_name"`
	RedeemCode         string `json:"redeem_code"`
	QualificationProof string `json:"qualification_proof"`
}

// CreditCardPut api client put request
type CreditCardPut struct {
	CardCvc string `json:"card_cvc"`
	CardExp string `json:"card_exp"`
	CardNo  string `json:"card_no"`
}

// CreditCardUser map json response for user information
type CreditCardUser struct {
	ID                   string      `db:"id" json:"id"`
	Email                null.String `db:"email"`
	Phone                null.String `db:"phone"`
	Gender               null.String `db:"gender"`
	Birthday             null.String `db:"birthday"`
	Role                 string      `db:"role" json:"role"`
	Type                 string      `db:"type" json:"type"`
	CreatedAt            null.Time   `db:"created_at" json:"createdAt"`
	CreatedBy            null.String `db:"created_by" json:"createdBy"`
	ExpiredAt            int64       `db:"expired_at" json:"expiredAt"`
	AutoRenew            bool        `db:"auto_renew" json:"autoRenew"`
	AsSubscribe          bool        `db:"as_subscribe"`
	PaymentEmail         null.String `db:"payment_email"`
	CreditCard6No        null.String `db:"credit_card_6no"`
	CreditCard4No        null.String `db:"credit_card_4no"`
	CreditCardTokenValue null.String `db:"credit_card_token_value"`
	CreditCardTokenTerm  null.String `db:"credit_card_token_term"`
	CaringCode           null.String `db:"caring_code"`
	Recipient            null.String `db:"recipient"`
	RecipientAddress     null.String `db:"recipient_address"`
	CarrierType          null.String `db:"carrier_type"`
	CarrierValue         null.String `db:"carrier_value"`
	TaxID                null.String `db:"tax_id"`
	FamilyID             null.String `db:"family_id"`

	CardNo             string
	CardExp            string
	CardCvc            string
	ProductName        string
	DurationUnit       string
	DurationInt        int
	QualificationProof string // keep it just for send event to Amplitude transaction complete event
	TransactionOrder   *dbuser.Order
	Product            *Product
	SpaResult          spaResult
	VeryFirstOrder     bool
}

// ValidProduct test product
func (user *CreditCardUser) ValidProduct() (product *Product, ok bool) {
	db := kkapp.App.DbUser.Slave()
	product = new(Product)
	err := db.Get(product, sqlcredit["productByName"], user.ProductName, creditCardPaymentType)
	if err != nil {
		log.Println("[ERROR] product", err)
		return nil, false
	}
	user.Product = product

	// persistance duration at user instance after ValidProduct
	user.DurationUnit, user.DurationInt = kkutil.ParseDuration(user.Product.Duration)
	return product, true
}

// LoadProductByID test product
func (user *CreditCardUser) LoadProductByID(id int64) (err error) {
	var product *Product
	db := kkapp.App.DbUser.Slave()
	product = new(Product)
	err = db.Get(product, sqlcredit["productByID"], id)
	if err != nil {
		log.Println("[ERROR] product", err)
		return err
	}
	user.ProductName = product.Name
	user.Product = product
	// persistance duration at user instance after ValidProduct
	user.DurationUnit, user.DurationInt = kkutil.ParseDuration(user.Product.Duration)
	return nil
}

// IsReadyToCreateOrder check before create order
func (user *CreditCardUser) IsReadyToCreateOrder() (ok bool) {
	ok = true

	if !user.PaymentEmail.Valid ||
		user.CardNo == "" || user.CardCvc == "" || user.CardExp == "" ||
		user.ProductName == "" {
		ok = false
	}
	return ok
}

// CreateOrder create order
func (user *CreditCardUser) CreateOrder(orderDate, startDate, endDate time.Time, price int64, isDiscountOrder bool) (order *dbuser.Order, err error) {
	log.Println("[INFO] create order", user.ID, user.Product)
	order = new(dbuser.Order)
	order.OrderDate = orderDate
	order.StartDate = startDate
	order.EndDate = endDate

	order.ProductID = user.Product.ID
	order.PaymentType = user.Product.PaymentType
	order.UserID = user.ID
	order.ID = dbuser.GenerateOrderID(user.Product.PaymentTypeCode, order.OrderDate)

	order.Price = price
	order.PriceNoTax = user.Product.PriceNoTax
	order.Fee = user.Product.Fee
	order.TaxRate = user.Product.TaxRate

	// 如果是「優惠週期」的「信用卡」訂單，則傳入價格為優惠週期的價格，用優惠週期的價格計算免稅價及手續費
	if order.PaymentType == creditCardPaymentType && isDiscountOrder && order.Price > 0 {
		order.PriceNoTax = user.Product.DiscountPriceNoTax
		order.TaxRate = user.Product.TaxRate
		// credit card order current fee fixed at 2%
		order.Fee = user.Product.DiscountFee
		if order.Fee == 0 {
			// miniumn transaction fee for credit card
			order.Fee = 1
		}
	}

	err = user.InsertOrder(order)
	if err != nil {
		zlog.Error("credit card: fail to insert order").Err(err).Interface("order", order).Send()
		err = errors.New("Database error")
		return nil, err
	}
	return order, err
}

// CreateNextOrder create the normal price next pendding order
func (user *CreditCardUser) CreateNextOrder(orderDate time.Time) (order *dbuser.Order, err error) {
	log.Println("[INFO] create next order in normal price", user.ID, user.Product)
	endDate := kkutil.AddDuration(orderDate, user.DurationUnit, user.DurationInt)
	return user.CreateOrder(orderDate, orderDate, endDate, user.Product.Price, false)
}

// NewOrder for user who have no previous order
func (user *CreditCardUser) NewOrder() (order *dbuser.Order, err error) {
	var orderDate, startDate, endDate time.Time
	var lastOrderEndDate time.Time // remember the last order EndDate

	if !user.IsReadyToCreateOrder() {
		err = errors.New("Invalid parameters")
		return
	}

	if user.Product == nil {
		if _, ok := user.ValidProduct(); !ok {
			err = errors.New("Invalid product name")
			return
		}
	}

	log.Println("[INFO] new order", user.ID, user.Product)
	loc, _ := time.LoadLocation("Asia/Taipei")
	now := time.Now().Truncate(time.Second)

	// prepare orderDate, startDate, endDate
	orderDate = now
	if user.ExpiredAt < now.Unix() {
		// user expired_at long time ago
		startDate = time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, loc).AddDate(0, 0, 1).UTC()
	} else {
		startDate = time.Unix(user.ExpiredAt, 0)
	}

	////////////////////////////////////////////////////////////////////////////////
	//   FIXME,  VERY IMPORTANT, family plan member expired_at count on order EndDate
	// if it's a family plan order, the start
	if user.Product.Bundle != nil && user.Product.Bundle.Family > 1 {
		startDate = time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, loc).AddDate(0, 0, 1).UTC()
	}

	durationUnit, durationInt := user.DurationUnit, user.DurationInt
	freeDurationUnit, freeDurationInt := kkutil.ParseDuration(user.Product.FreeDuration)
	discountDurationUnit, discountDurationInt := kkutil.ParseDuration(user.Product.DiscountDuration)

	createDiscountOrder := func() (order *dbuser.Order, err error) {
		// count repeat times
		if durationUnit == discountDurationUnit {
			count := discountDurationInt / durationInt
			if count*durationInt != discountDurationInt {
				// the discountDuration Int not times of durationInt
				// product setup error
				return nil, errors.New("Invalid product name")
			}
			// repeat discount order
			lastOrderEndDate = startDate
			for i := 0; i < count; i++ {
				// repeat discount order
				if i == 0 {
					endDate = kkutil.AddDuration(startDate, durationUnit, durationInt)
					order, err = user.CreateOrder(orderDate, startDate, endDate, user.Product.DiscountPrice, true)
					lastOrderEndDate = order.EndDate
				} else {
					// repeat discount order
					var discountOrder *dbuser.Order
					endDate = kkutil.AddDuration(lastOrderEndDate, durationUnit, durationInt)
					discountOrder, err = user.CreateOrder(lastOrderEndDate, lastOrderEndDate, endDate, user.Product.DiscountPrice, true)
					lastOrderEndDate = discountOrder.EndDate
				}

				if err != nil {
					return nil, err
				}
			}
			// end of durationUnit == discountDurationUnit
		} else {
			// durationUnit != discountDurationUnit
			// we only add one order for this discountDuration
			endDate = kkutil.AddDuration(startDate, discountDurationUnit, discountDurationInt)
			order, err = user.CreateOrder(orderDate, startDate, endDate, user.Product.DiscountPrice, true)
			if err != nil {
				return nil, err
			}
		}
		return order, err
	}

	////////////////////////////////////////
	// 方案適用首購優惠
	if user.Product.AsSubscribe {
		// 如果使用者從未訂購
		if !user.AsSubscribe {
			// 如果方案有設定 免費週期 或 優惠週期
			// 優惠週期和免費週期互斥，只能設定其中一項
			if freeDurationInt > 0 {
				// free duration order
				endDate = kkutil.AddDuration(startDate, freeDurationUnit, freeDurationInt)
				order, err = user.CreateOrder(orderDate, startDate, endDate, 0, false)
			} else if discountDurationInt > 0 {
				// discount order
				order, err = createDiscountOrder()
			}

			if err != nil {
				log.Println("[ERROR]", err)
				err = errors.New("Database error")
				return nil, err
			}

			if order != nil {
				// got first order created
				return order, err
			}
		}
	} else { // 方案不適用首購優惠
		// 如果方案有設定優惠週期
		if discountDurationInt > 0 {
			// discount order
			order, err = createDiscountOrder()
			if err != nil {
				log.Println("[ERROR]", err)
				err = errors.New("Database error")
				return nil, err
			}

			if order != nil {
				// got first order created
				return order, err
			}
		}
	}

	////////////////////////////////////////
	// general order
	endDate = kkutil.AddDuration(startDate, durationUnit, durationInt)
	order, err = user.CreateOrder(orderDate, startDate, endDate, user.Product.Price, false)
	if err != nil {
		log.Println("[ERROR]", err)
		err = errors.New("Database error")
		return nil, err
	}
	return order, err
}

// CheckCreateNextOrder for user next order
func (user *CreditCardUser) CheckCreateNextOrder(order *dbuser.Order) (nextOrder *dbuser.Order, err error) {
	if order == nil {
		return nil, fmt.Errorf("order is nil")
	}
	if user.Product.AutoRenew {
		if user.AutoRenew {
			// check if next order exist
			db := kkapp.App.DbUser.Slave()
			nextOrder = new(dbuser.Order)
			err = db.Get(nextOrder, sqlcredit["nextorder"], user.ID, creditCardPaymentType, order.EndDate)
			if err == sql.ErrNoRows {
				if nextOrder, err = user.CreateNextOrder(order.EndDate); err != nil {
					zlog.Warn("CheckCreateNextOrder: failed to create next order").Str("userID", user.ID).Str("orderID", order.ID).Send()
				}
			}
		} else {
			zlog.Warn("CheckCreateNextOrder: user wouldn't auto renew the order").Str("userID", user.ID).Str("orderID", order.ID).Send()
		}
	}
	return
}

// GetOrder get the renew order
func (user *CreditCardUser) GetOrder() (order *dbuser.Order, err error) {
	// get latest order
	db := kkapp.App.DbUser.Slave()
	order = new(dbuser.Order)
	err = db.Get(order, sqlshare["order"], user.ID, creditCardPaymentType)
	if err != nil {
		return nil, err
	}
	return order, nil
}

// GetRenewOrder get the renew order
func (user *CreditCardUser) GetRenewOrder() (order *dbuser.Order, err error) {
	// get latest order
	db := kkapp.App.DbUser.Slave()
	order = new(dbuser.Order)
	err = db.Get(order, sqlcredit["reneworder"], user.ID, creditCardPaymentType)
	if err != nil {
		return nil, err
	}
	return order, nil
}

// InsertOrder insert the user order
func (user *CreditCardUser) InsertOrder(order *dbuser.Order) (err error) {
	// insert the new order
	db := kkapp.App.DbUser.Master()
	err = retryIfSqlDuplicatedKey(func(newID string) error {
		order.ID = newID
		_, err = db.NamedExec(sqlcredit["createorder"], order)
		return err
	}, order.ID, func(isMaxRetry bool) {
		if isMaxRetry {
			notifyOrderMaxRetryFail("Fail to new order", order)
		}
	})
	return
}

// CancelOrder cancel the user order
func (user *CreditCardUser) CancelOrder(isRenewOrder bool) {
	// get latest order
	db := kkapp.App.DbUser.Master()
	log.Println("[INFO] cancel order", user.ID)
	res, err := db.Exec(sqlshare["cancel"], user.ID, creditCardPaymentType)
	if err != nil {
		log.Println("[ERROR] cancel order", err)
	}

	rowCnt, err := res.RowsAffected()
	if err != nil {
		log.Println("[ERROR] cancel order", err)
	}

	if rowCnt > 0 && isRenewOrder {
		log.Printf("[INFO] Order affected = %d\n", rowCnt)
		user.CancelledEvent()
	}
	return
}

// FailOrder fail the user order
func (user *CreditCardUser) FailOrder(order *dbuser.Order, isRenewOrder bool) {
	// get latest order
	var err error
	order.Status = null.NewString("fail", true)

	db := kkapp.App.DbUser.Master()
	log.Println("[INFO] fail order", order.UserID)
	_, err = db.NamedExec(sqlcredit["failrestorder"], order)
	if err != nil {
		log.Println("[ERROR] fail order", err)
	}

	if isRenewOrder {
		_, err = db.Exec(`UPDATE users SET auto_renew = false, updated_at = NOW() WHERE id = $1;`, user.ID)
		if err != nil {
			log.Println("[ERROR] fail order", err)
		}
		user.CancelledEvent()
	}
	return
}

// Extend the user order
func (user *CreditCardUser) Extend(order *dbuser.Order) (err error) {
	db := kkapp.App.DbUser.Master()
	now := time.Now()

	// extend this order
	if order.OrderDate.Unix() < now.AddDate(0, 0, 2).Unix() {
		log.Println("[INFO] extend order order_date", user.ID, order)
		_, err = db.Exec(sqlshare["extend"], order.ID)
		if err != nil {
			return err
		}
	}

	// extend the user expired time if the user expired_at smaller than time.Now().AddDate(0,0,2).Unix()
	if user.ExpiredAt < now.AddDate(0, 0, 2).Unix() {
		log.Println("[INFO] extend user expired_at", user.ID, order)
		expiredDate := now.AddDate(0, 0, 2).UTC()
		if err = updateUser(db, user.ID, usermodel.RolePremium.String(), usermodel.MembershipPremiumOnly, expiredDate, true, usermodel.TypeGeneral.String()); err != nil {
			return err
		}
	}
	return nil
}

// ** 寬限期：
// ** 1. 在系統扣款失敗觸發寬限期時，系統會直接修改原失敗訂單的 id 及 status 變成一張新訂單來讓 auto renew 重新扣款，
// **    並且會把用戶的效期延長 7 天，此時會是沒有訂單有效的狀態，更新完的訂單要在 7 天後才會重新嘗試扣款。
// ** 2. 上述第一次扣款失敗的訂單編號可以在 server_notifications 裡面找到。
// ** 3. 如果是家庭方案的用戶，當觸發寬限期時，主帳號、子帳號都會被延長 7 天，但是此時因為訂單不成立所以家庭方案也並不成立，
// **    CMS 應無法查到用戶的家庭方案資訊，因為家庭方案訂單並未扣款成功，且寬限期內的時間並不在訂單的 order_date, end_date 區間內。
// ** 4. 如果用戶訂購家庭方案，原帳號扣款失敗進入寬限期，接著用戶又用其他帳號訂購家庭方案，想要加入原帳號底下的子帳號到新的家庭方案，
// **    假如在原帳號的寬限期內會無法成功加入，因為子帳號在原帳號的寬限期內被延長 7 天效期，所以仍為 premium 身份，除非將子帳號改為 expired 身份，
// **    才可被加入新的家庭方案群組
// ExtendGracePeriod extend the date of order by giving it grace period
func (user *CreditCardUser) ExtendGracePeriod(order *dbuser.Order) (err error) {
	db := kkapp.App.DbUser.Master()

	// update failed info to order
	order.Status = null.NewString("", false)
	_, err = db.NamedExec(sqlcredit["failorder"], order)
	if err != nil {
		zlog.Error("credit card: make failorder error").Err(err).Interface("order", order).Send()
	}

	// Must to generate new order ID for 3rd party payment API
	newOrderID := dbuser.GenerateOrderID(user.Product.PaymentTypeCode, order.OrderDate)
	err = retryIfSqlDuplicatedKey(func(newID string) error {
		_, err = db.Exec(sqlcredit["extendGracePeriod"], order.ID, newID)
		return err
	}, newOrderID, func(isMaxRetry bool) {
		if isMaxRetry {
			notifyOrderMaxRetryFail("Fail to extend grace period", order)
		}
	})
	if err != nil {
		zlog.Error("credit card: fail to extend grace period").Err(err).Interface("order", order).Send()
		return err
	}

	// extend the user expired date if expired_at before grace period date (7 days after)
	graceDate := order.OrderDate.AddDate(0, 0, 7)
	if user.ExpiredAt < graceDate.Unix() {
		newExpiredDate := graceDate.Add(time.Hour * 2).UTC()
		err = updateUser(db, user.ID, usermodel.RolePremium.String(), usermodel.MembershipPremiumOnly,
			newExpiredDate, true, usermodel.TypeGeneral.String())
		if err != nil {
			return fmt.Errorf("fail to update user: %w", err)
		}
	}

	// grace period for family member
	fmMembers := []string{}
	if user.Product.Bundle != nil && user.Product.Bundle.Family > 1 {
		fmMembers, err = user.GetFamily()
		if err != nil {
			zlog.Warn("CreditCardUser: ExtendGracePeriod: fail to get family").Str("user_id", user.ID).Err(err).Send()
		}

		if len(fmMembers) > 0 {
			_memberExpiredDate := graceDate.Add(time.Hour * 2).UTC()
			zlog.Info("going to update family member for grace period").
				Str("user_id", user.ID).Strs("family", fmMembers).
				Time("expired_at", _memberExpiredDate).Bool("auto_renew", user.Product.AutoRenew).Send()
			userRepo := wrapper.NewUserService(db)
			_, err = userRepo.BatchUpdateByFields(fmMembers, map[usermodel.UsersField]interface{}{
				usermodel.UserFieldRole:       model.UserRolePremium,
				usermodel.UserFieldExpiredAt:  _memberExpiredDate,
				usermodel.UserFieldAutoRenew:  user.Product.AutoRenew,
				usermodel.UserFieldMembership: usermodel.MembershipPremiumOnly,
			})
			if err != nil {
				return fmt.Errorf("fail to update family member: %w", err)
			}
		}
	}
	return nil
}

func notifyOrderMaxRetryFail(title string, o *dbuser.Order) {
	slackChannel := creditCardConfig[kkapp.App.Debug].PaymentErrorSlack
	slackMsg := slack.Attachment{
		Color: "danger",
		Title: title,
		Text:  "max retry times reached",
		Fields: []slack.AttachmentField{
			{Title: "user id", Value: o.UserID},
			{Title: "payment type", Value: o.PaymentType},
			{Title: "product id", Value: strconv.Itoa(int(o.ProductID))},
		},
	}
	sns.NewPaymentSlack(slackChannel, "<!here> Fail to create/update order", slackMsg)
}

// GetLatestOrder get the latest order
func (user *CreditCardUser) GetLatestOrder() (order *dbuser.Order, err error) {
	// get latest order
	db := kkapp.App.DbUser.Slave()
	order = new(dbuser.Order)
	err = db.Get(order, sqlcredit["latestorder"], user.ID, creditCardPaymentType)
	if err != nil {
		return nil, err
	}
	return order, nil
}

// GetFamily fetch the family member user_id
func (user *CreditCardUser) GetFamily() (members []string, err error) {
	db := kkapp.App.DbUser.Slave()
	err = db.Select(&members, sqlcredit["getfamily"], user.ID)
	return members, err
}

// CancelledEvent the user
func (user *CreditCardUser) CancelledEvent() {
	log.Println(user.Product)
	event, _ := amplitude.NewAccountTransactionCancelled(user.ID, creditCardPaymentType, "auto renewal failed")
	log.Println("[INFO] amplitude event", user.ID)
	event.Send()

	// edm cancel event
	if ematicSrv := wrapper.NewEmaticService(); ematicSrv.IsClientExist() {
		go ematicSrv.Cancel(user.Email.String, time.Unix(user.ExpiredAt, 0))
	}
}

// SendTransactionEvent the user
func (user *CreditCardUser) SendTransactionEvent(triggerCondition string) {
	var ep amplitude.EventProperties
	durationUnit, durationInt := user.DurationUnit, user.DurationInt
	if user.TransactionOrder.Price > 0 {
		// paid
		switch durationUnit {
		case "day":
			ep = amplitude.EventProperties{
				OrderNumber:     user.TransactionOrder.ID,
				Plan:            user.Product.Name,
				PaymentType:     user.Product.PaymentType,
				ObtainedVipDays: durationInt,
			}
		case "mon":
			ep = amplitude.EventProperties{
				OrderNumber:       user.TransactionOrder.ID,
				Plan:              user.Product.Name,
				PaymentType:       user.Product.PaymentType,
				ObtainedVipMonths: durationInt,
			}
		case "year":
			ep = amplitude.EventProperties{
				OrderNumber:       user.TransactionOrder.ID,
				Plan:              user.Product.Name,
				PaymentType:       user.Product.PaymentType,
				ObtainedVipMonths: 12,
			}
		}
	} else {
		// free trial
		switch durationUnit {
		case "day":
			ep = amplitude.EventProperties{
				OrderNumber:      user.TransactionOrder.ID,
				Plan:             user.Product.Name,
				PaymentType:      user.Product.PaymentType,
				GivenFreeVipDays: durationInt,
			}
		case "mon":
			ep = amplitude.EventProperties{
				OrderNumber:        user.TransactionOrder.ID,
				Plan:               user.Product.Name,
				PaymentType:        user.Product.PaymentType,
				GivenFreeVipMonths: durationInt,
			}
		case "year":
			ep = amplitude.EventProperties{
				OrderNumber:        user.TransactionOrder.ID,
				Plan:               user.Product.Name,
				PaymentType:        user.Product.PaymentType,
				GivenFreeVipMonths: 12,
			}
		}
	}
	if user.QualificationProof != "" {
		ep.QualificationProof = user.QualificationProof
	}
	event, _ := amplitude.NewAccountTransactionCompleted(user.ID, triggerCondition, int(user.TransactionOrder.Price), ep)
	event.Send()
	zlog.Info("sent transaction complete event to Amplitude").
		Str("user_id", user.ID).Interface("order", user.TransactionOrder).Interface("event", ep).Send()

	// edm paid event
	// if user.Email.Valid {
	// 	kkapp.App.Ematic.Paid(user.Email.String, time.Unix(user.ExpiredAt, 0).Format(ematicagent.EmaticDateFormat))
	// }

}

// Authorize post to spagateway
func (user *CreditCardUser) Authorize(order *dbuser.Order, useTokenValue bool) (kkresp model.KKResp, err error) {
	var postData map[string]interface{}
	var response spaResponse
	var amt int64

	log.Println("[INFO] SPAGATEWAY Authorize", order)

	if order.Price == 0 {
		amt = 1
	} else {
		amt = order.Price
	}
	postData = make(map[string]interface{})

	// postData
	postData["TimeStamp"] = time.Now().Unix()
	postData["Version"] = "1.0"
	postData["MerchantOrderNo"] = order.ID
	postData["Amt"] = amt
	postData["ProdDesc"] = user.Product.ItemName
	postData["PayerEmail"] = user.PaymentEmail.String

	if useTokenValue {
		postData["TokenValue"] = user.CreditCardTokenValue.String
		postData["TokenTerm"] = user.CreditCardTokenTerm.String
		postData["TokenSwitch"] = "on"
	} else {
		user.CreditCardTokenTerm = null.NewString(RandomString(20), true)
		postData["CardNo"] = user.CardNo
		postData["Exp"] = user.CardExp
		postData["CVC"] = user.CardCvc
		postData["TokenSwitch"] = "get"
		postData["TokenTerm"] = user.CreditCardTokenTerm.String
	}

	urlValues := MapToURLValues(postData)

	merchantID := creditCardConfig[kkapp.App.Debug].MerchantID
	hashKey := creditCardConfig[kkapp.App.Debug].GatewayHashKey
	hashIV := creditCardConfig[kkapp.App.Debug].GatewayHashIV

	// payLoad
	// send request to spagateway
	payLoad := url.Values{}
	payLoad.Add("MerchantID_", merchantID)
	payLoad.Add("Pos_", "JSON")
	payLoad.Add("PostData_", Encrypt(urlValues.Encode(), hashKey, hashIV))

	spaurl := creditCardConfig[kkapp.App.Debug].GatewayURL
	req, _ := http.NewRequest("POST", spaurl, strings.NewReader(payLoad.Encode()))
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	resp, err := curl.Do(req)

	if err != nil {
		// handle error, cancel fail rollback
		log.Println("[ERROR]", err)
		err = errors.New("Network error")
		return
	}

	statuscode := resp.StatusCode
	body, err := ioutil.ReadAll(resp.Body)

	if err != nil {
		log.Println("[ERROR]", err)
		err = errors.New("Network error")
		return
	}
	resp.Body.Close()

	err = json.Unmarshal(body, &response)
	if err != nil {
		log.Println("[ERROR]", err)
	}
	log.Println("[INFO] spagateway response body:", string(body))
	log.Println("[INFO] spagateway response statusCode:", statuscode)

	kkresp = model.MakeOk()
	// fail
	if response.Status != "SUCCESS" {
		log.Println("[ERROR] Payment - Credit card transaction failed")
		kkresp.Status.Subtype = response.Status
		kkresp.Status.Message = response.Message
		kkresp.Status.Type = "FAIL"

		return
	}

	// checkcode
	var result spaResult

	err = json.Unmarshal(response.Result, &result)

	checkData := url.Values{}
	checkData.Add("Amt", fmt.Sprint(result.Amt))
	checkData.Add("MerchantID", result.MerchantID)
	checkData.Add("MerchantOrderNo", result.MerchantOrderNo)
	checkData.Add("TradeNo", result.TradeNo)

	if !CheckCode(checkData.Encode(), result.CheckCode, hashKey, hashIV) {
		err = errors.New("Transaction check code is inconsistence")
		return
	}
	// save spaResult for latter use
	user.SpaResult = result

	// payment info update
	if result.TokenValue != "" {
		// only update token_value when have data
		user.CreditCardTokenValue = null.NewString(result.TokenValue, true)
	}
	user.CreditCard6No = null.NewString(result.Card6No, true)
	user.CreditCard4No = null.NewString(result.Card4No, true)

	// save order info
	order.Info = null.NewString(string(response.Result), true)
	return
}

// DeAuthorize post to spagateway
func (user *CreditCardUser) DeAuthorize(order *dbuser.Order) (kkresp model.KKResp, err error) {
	var postData map[string]interface{}
	var response spaResponse
	var amt int64

	log.Println("[INFO] SPAGATEWAY DeAuthorize", order)
	postData = make(map[string]interface{})

	if order.Price == 0 {
		amt = 1
	} else {
		amt = order.Price
	}

	// postData
	postData["RespondType"] = "JSON"
	postData["Version"] = "1.0"
	postData["Amt"] = amt
	postData["MerchantOrderNo"] = order.ID
	postData["IndexType"] = 1
	postData["TimeStamp"] = time.Now().Unix()

	urlValues := MapToURLValues(postData)

	merchantID := creditCardConfig[kkapp.App.Debug].MerchantID
	hashKey := creditCardConfig[kkapp.App.Debug].GatewayHashKey
	hashIV := creditCardConfig[kkapp.App.Debug].GatewayHashIV

	// payLoad
	// send request to spagateway
	payLoad := url.Values{}
	payLoad.Add("MerchantID_", merchantID)
	payLoad.Add("PostData_", Encrypt(urlValues.Encode(), hashKey, hashIV))

	spaurl := creditCardConfig[kkapp.App.Debug].GatewayCancelURL
	req, _ := http.NewRequest("POST", spaurl, strings.NewReader(payLoad.Encode()))
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	resp, err := curl.Do(req)

	if err != nil {
		// handle error, cancel fail rollback
		log.Println("[ERROR]", err)
		err = errors.New("Network error")
		return
	}

	statuscode := resp.StatusCode
	body, err := ioutil.ReadAll(resp.Body)

	if err != nil {
		log.Println("[ERROR]", err)
		err = errors.New("Network error")
		return
	}
	resp.Body.Close()

	json.Unmarshal(body, &response)

	log.Println("[INFO] spagateway response body:", string(body))
	log.Println("[INFO] spagateway response statusCode:", statuscode)

	kkresp = model.MakeOk()
	// fail
	if response.Status != "SUCCESS" {
		log.Println("[ERROR] Payment - Deauthorize credit card failed")
		kkresp.Status.Subtype = response.Status
		kkresp.Status.Message = response.Message
		kkresp.Status.Type = "FAIL"
	}
	// checkcode
	var result spaResult
	json.Unmarshal(response.Result, &result)

	checkData := url.Values{}
	checkData.Add("Amt", fmt.Sprint(result.Amt))
	checkData.Add("MerchantID", result.MerchantID)
	checkData.Add("MerchantOrderNo", result.MerchantOrderNo)
	checkData.Add("TradeNo", result.TradeNo)

	if !CheckCode(checkData.Encode(), result.CheckCode, hashKey, hashIV) {
		err = errors.New("Deauthorize check code is inconsistence")
		return
	}
	return
}

// SendDeAuthorizeError send sns
func (user *CreditCardUser) SendDeAuthorizeError(order *dbuser.Order, err error) {
	topic := creditCardConfig[kkapp.App.Debug].PaymentErrorTopic

	subject := fmt.Sprintf("信用卡取消授權失敗 %s", order.ID)
	message := fmt.Sprintf(`
Order ID: %s
User ID: %s
Reason: Deauthorize credit card failed
%s`, order.ID, order.UserID, err.Error())

	sns.NewPaymentSNS(subject, message, topic)

	slackChannel := creditCardConfig[kkapp.App.Debug].PaymentErrorSlack
	slackMsg := slack.Attachment{
		Color: "#36a64f",
		Title: subject,
		Text:  message,
	}
	sns.NewPaymentSlack(slackChannel, subject, slackMsg)
}

func (user *CreditCardUser) saveNotification(resp *model.KKResp, order *dbuser.Order, allowGrace bool) {
	//藍新授權失敗回傳訊息 寫入 NotificationType
	//TRA10016-信用卡授權失敗_掛失卡
	//TRA10016-信用卡授權失敗_交易失敗
	//TRA10016-信用卡授權失敗_卡片過期
	//TRA10016-信用卡授權失敗_帳號不符
	//TRA10016-信用卡授權失敗_拒絕交易
	//TRA10016-信用卡授權失敗_無效交易
	//TRA10016-信用卡授權失敗_系統錯誤
	//TRA10016-信用卡授權失敗_餘額不足
	//TRA10016-信用卡授權失敗_卡號保護中
	//TRA10016-信用卡授權失敗_請查詢銀行
	//TRA10016-信用卡授權失敗_請重試交易
	//TRA10016-信用卡授權失敗_刷卡機號有誤
	//TRA10016-信用卡授權失敗_無此交易型態
	//TRA10067-不接受卡號直接授權
	//TRA10076-查無此Token
	//TRA10100-Token付款人綁定資料不可空白
	//TRA10107-拒絕交易_拒絕卡號

	n := new(NotificationLog)
	n.UserID = user.ID
	n.OrderID = null.NewString(order.ID, true)
	n.ProductID = strconv.Itoa(int(order.ProductID))
	n.PaymentType = "credit_card"
	n.NotificationType = fmt.Sprintf("%s-%s", resp.Status.Subtype.(string), resp.Status.Message.(string)) //refer to comments above
	n.ExpiresDate = time.Unix(user.ExpiredAt, 0)

	if allowGrace {
		n.IsInBillingRetryPeriod = true
		n.GracePeriodExpiresDate = order.OrderDate
		n.GracePeriodExpiresDate = n.GracePeriodExpiresDate.AddDate(0, 0, 7)
	}

	db := kkapp.App.DbUser.Master()
	_, err := db.NamedExec(sqlcredit["saveNotification"], n)
	if err != nil {
		log.Printf("\n[ERROR] - Save Notification - Credit card ['%s'] failed. Error:%s\n", n.NotificationType, err.Error())
	}
}

func (user *CreditCardUser) sendFailedEmail(resp *model.KKResp, order *dbuser.Order) {
	var mailData email.CreditCardFailedEmailData
	mailData.ErrorCode = resp.Status.Subtype.(string)
	mailData.ErrorMessage = resp.Status.Message.(string)
	mailData.ExpireDate = time.Unix(user.ExpiredAt, 0)
	if len(user.CreditCard6No.String) > 4 {
		mailData.PaymentType = fmt.Sprintf("%s **** **** %s", user.CreditCard6No.String[0:4], user.CreditCard4No.String)
	}
	mail := email.NewCreditCardFailedEmail(mailData)

	client := email.NewClient()
	toAddress := user.PaymentEmail.String
	go client.Send(mail, toAddress)
}

func (user *CreditCardUser) sendGracedEmail(resp *model.KKResp, order *dbuser.Order) {
	var mailData email.CreditCardGracedEmailData
	mailData.ErrorCode = resp.Status.Subtype.(string)
	mailData.ErrorMessage = resp.Status.Message.(string)
	mailData.ExpireDate = time.Unix(order.OrderDate.AddDate(0, 0, 7).Unix(), 0)
	if len(user.CreditCard6No.String) > 4 {
		mailData.PaymentType = fmt.Sprintf("%s **** **** %s", user.CreditCard6No.String[0:4], user.CreditCard4No.String)
	}
	mail := email.NewCreditCardGracedEmail(mailData)

	client := email.NewClient()
	toAddress := user.PaymentEmail.String
	go client.Send(mail, toAddress)
}

// SendUpdateDBError send sns
func (user *CreditCardUser) SendUpdateDBError(order *dbuser.Order, err error) {
	topic := creditCardConfig[kkapp.App.Debug].PaymentErrorTopic

	subject := fmt.Sprintf("信用卡交易更新資料庫失敗 %s", order.ID)
	message := fmt.Sprintf(`
Order ID: %s
User ID: %s
Reason: Update database error
%s`, order.ID, order.UserID, err.Error())

	sns.NewPaymentSNS(subject, message, topic)

	slackChannel := creditCardConfig[kkapp.App.Debug].PaymentErrorSlack
	slackMsg := slack.Attachment{
		Color: "#36a64f",
		Title: subject,
		Text:  message,
	}
	sns.NewPaymentSlack(slackChannel, subject, slackMsg)
}

// SendPaymentError send sns
func (user *CreditCardUser) SendPaymentError(order *dbuser.Order, err error) {
	// deal with order might be nil
	if order == nil {
		order = &dbuser.Order{}
		order.ID = ""
	}

	//SNS
	topic := creditCardConfig[kkapp.App.Debug].PaymentErrorTopic
	subject := fmt.Sprintf("訂單處理錯誤 %s", order.ID)
	message := fmt.Sprintf(`
Order ID: %s
User ID: %s
%s`, order.ID, user.ID, err.Error())

	sns.NewPaymentSNS(subject, message, topic)

	//Slack
	slackChannel := creditCardConfig[kkapp.App.Debug].PaymentErrorSlack
	slackMsg := slack.Attachment{
		Color: "#36a64f",
		Title: "信用卡" + subject,
		Text:  message,
	}
	sns.NewPaymentSlack(slackChannel, subject, slackMsg)
}

// SendRenewError send sns
func (user *CreditCardUser) SendRenewError(order *dbuser.Order, err error) {
	topic := creditCardConfig[kkapp.App.Debug].PaymentErrorTopic

	subject := fmt.Sprintf("[RENEW] CreditCard 更新訂單資訊失敗 %s", order.ID)
	message := fmt.Sprintf(`
Order ID: %s
User ID: %s
%s`, order.ID, order.UserID, err.Error())

	sns.NewPaymentSNS(subject, message, topic)

	slackChannel := creditCardConfig[kkapp.App.Debug].PaymentErrorSlack
	slackMsg := slack.Attachment{
		Color: "#36a64f",
		Title: subject,
		Text:  message,
	}
	sns.NewPaymentSlack(slackChannel, subject, slackMsg)
}

// SendInvoiceError send sns
func (user *CreditCardUser) SendInvoiceError(order *dbuser.Order, err error) {
	topic := creditCardConfig[kkapp.App.Debug].PaymentErrorTopic

	subject := fmt.Sprintf("發票開立錯誤 %s", order.ID)
	message := fmt.Sprintf(`
Order ID: %s
User ID: %s
%s`, order.ID, order.UserID, err.Error())

	sns.NewPaymentSNS(subject, message, topic)

	slackChannel := creditCardConfig[kkapp.App.Debug].PaymentErrorSlack
	slackMsg := slack.Attachment{
		Color: "#36a64f",
		Title: subject,
		Text:  message,
	}
	sns.NewPaymentSlack(slackChannel, subject, slackMsg)
}

// AllowGracePeriod give grace period if match conditions
func (user *CreditCardUser) AllowGracePeriod(message string, order *dbuser.Order) (result bool) {
	sdb := kkapp.App.DbUser.Slave()

	// if the order_date has been extended before
	// the cnt will be greater than 0
	var cnt int64
	err := sdb.Get(&cnt, sqlcredit["graced"], user.ID, order.OrderDate)
	if err != nil {
		log.Printf("[ERROR] AllowGracePeriod - query graced servernotification failed. error:%s\n", err)
		return false
	}

	if cnt < 1 {
		return true
	}

	return false
}

// Realize the user order
func (user *CreditCardUser) Realize(order *dbuser.Order, isRenewOrder bool) (err error) {
	if order == nil {
		return errors.New("Invalid parameters")
	}
	log.Println("[INFO] Start Realize", order)

	user.TransactionOrder = order
	duration := order.EndDate.Sub(order.StartDate)

	var expiredDate time.Time

	loc, _ := time.LoadLocation("Asia/Taipei")
	now := time.Now().In(loc)
	saferNow := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, loc).AddDate(0, 0, 1)

	if user.ExpiredAt < saferNow.Unix() {
		expiredDate = saferNow.Add(duration).UTC()
	} else {
		expiredDate = time.Unix(user.ExpiredAt, 0).Add(duration).UTC()
	}

	user.ExpiredAt = expiredDate.Unix()

	order.Status = null.String{NullString: sql.NullString{String: "ok", Valid: true}}
	order.RealizedAt = null.Time{Time: time.Now().UTC().Truncate(time.Second), Valid: true}

	// for family order
	fmMembers := []string{}
	if user.Product.Bundle != nil && user.Product.Bundle.Family > 1 {
		fmMembers, err = user.GetFamily()
		if err != nil {
			log.Println("[ERROR] family", err)
		}
	}

	// 1. transaction start
	// 2. update order && if order is family plan update the member user info
	// 3. update user info
	// 4. update payment_info
	// 5. commit or rollback
	db := kkapp.App.DbUser.Master()
	var tx *sqlx.Tx
	tx, err = db.Beginx()

	defer func() {
		if err != nil {
			zlog.Error("CreditCardUser: realize error").Str("user_id", user.ID).Err(err).Send()
			tx.Rollback()
		}
		commitErr := tx.Commit()
		if commitErr != nil {
			zlog.Error("CreditCardUser: realize: commit error").Str("user_id", user.ID).Err(commitErr).Send()
		}
	}()

	_, err = tx.NamedExec(sqlcredit["updateorder"], order)
	if err != nil {
		return
	}
	zlog.Info("CreditCardUser: realize: updated order").Interface("order", order).Send()

	if isRenewOrder {
		// should not update to user.AutoRenew (keep current value)
		zlog.Info("CreditCardUser: Realize: update user for renew order").Time("expired_at", expiredDate).
			Bool("auto_renew", user.AutoRenew).Str("user_id", user.ID).Send()
		err = updateUser(tx, user.ID, usermodel.RolePremium.String(), usermodel.MembershipPremiumOnly,
			expiredDate, user.AutoRenew, usermodel.TypeGeneral.String())
		if err != nil {
			return
		}

		// it's a family plan update family member expired date
		if len(fmMembers) > 0 {
			// use realize order.EndDate plus one day as safer member expired date
			_memberExpiredDate := order.EndDate.AddDate(0, 0, 1).UTC()
			log.Println("[INFO] update family member for renew order", _memberExpiredDate, user.Product.AutoRenew, fmMembers)
			userRepo := wrapper.NewUserService(tx)
			_, err = userRepo.BatchUpdateByFields(fmMembers, map[usermodel.UsersField]interface{}{
				usermodel.UserFieldRole:       model.UserRolePremium,
				usermodel.UserFieldExpiredAt:  _memberExpiredDate,
				usermodel.UserFieldAutoRenew:  user.Product.AutoRenew,
				usermodel.UserFieldMembership: usermodel.MembershipPremiumOnly,
			})
		}
	} else {
		zlog.Info("CreditCardUser: Realize: update user for new order").Time("expired_at", expiredDate).
			Bool("auto_renew", user.Product.AutoRenew).Str("user_id", user.ID).Send()
		err = updateUser(tx, user.ID, usermodel.RolePremium.String(), usermodel.MembershipPremiumOnly,
			expiredDate, user.Product.AutoRenew, usermodel.TypeGeneral.String())
		// update current user instance to latest AutoRenew status
		user.AutoRenew = user.Product.AutoRenew
	}

	if err != nil {
		return
	}

	paymentInfo := new(dbuser.PaymentInfo)
	paymentInfo.UserID = user.ID
	paymentInfo.Email = user.PaymentEmail
	paymentInfo.PaymentType = null.StringFrom(creditCardPaymentType)
	paymentInfo.CaringCode = user.CaringCode
	paymentInfo.CarrierType = user.CarrierType
	paymentInfo.CarrierValue = user.CarrierValue
	paymentInfo.Recipient = user.Recipient
	paymentInfo.RecipientAddress = user.RecipientAddress
	paymentInfo.CreditCard4NO = user.CreditCard4No
	paymentInfo.CreditCard6NO = user.CreditCard6No
	paymentInfo.CreditCardTokenTerm = user.CreditCardTokenTerm
	paymentInfo.CreditCardTokenValue = user.CreditCardTokenValue
	zlog.Info("realize credit card order: upsert payment_info").Interface("payment_info", paymentInfo).Send()
	_, err = tx.NamedExec(sqlshare["upsertpayment"], paymentInfo)
	return
}

// SearchInvoice
func (user *CreditCardUser) SearchInvoice(order *dbuser.Order) (response invoiceResponse, err error) {
	var postData map[string]interface{}

	log.Println("[INFO] Search INVOICE", order)

	postData = make(map[string]interface{})

	// postData
	postData["RespondType"] = "JSON"
	postData["Version"] = "1.1"
	postData["TimeStamp"] = time.Now().Unix()
	postData["SearchType"] = "1"
	postData["MerchantOrderNo"] = order.ID
	postData["TotalAmt"] = order.Price

	urlValues := MapToURLValues(postData)

	merchantID := creditCardConfig[kkapp.App.Debug].InvoiceMerchantID
	hashKey := creditCardConfig[kkapp.App.Debug].InvoiceHashKey
	hashIV := creditCardConfig[kkapp.App.Debug].InvoiceHashIV

	payLoad := url.Values{}
	payLoad.Add("MerchantID_", merchantID)
	payLoad.Add("PostData_", Encrypt(urlValues.Encode(), hashKey, hashIV))

	invoiceURL := creditCardConfig[kkapp.App.Debug].InvoiceSearchURL
	req, _ := http.NewRequest("POST", invoiceURL, strings.NewReader(payLoad.Encode()))
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	resp, err := curl.Do(req)

	if err != nil {
		// handle error, cancel fail rollback
		log.Println("[ERROR]", err)
		err = errors.New("Network error")
		return
	}

	statuscode := resp.StatusCode
	body, err := ioutil.ReadAll(resp.Body)

	if err != nil {
		log.Println("[ERROR]", err)
		err = errors.New("Network error")
		return
	}
	resp.Body.Close()

	json.Unmarshal(body, &response)

	log.Println("[INFO] ezpay response body:", string(body))
	log.Println("[INFO] ezpay response statusCode:", statuscode)
	return
}

// CancelInvoice
func CancelInvoice(invoiceNumber string) (err error) {
	var postData map[string]interface{}
	var response invoiceResponse

	postData = make(map[string]interface{})

	// postData
	postData["RespondType"] = "JSON"
	postData["Version"] = "1.0"
	postData["TimeStamp"] = time.Now().Unix()
	postData["InvoiceNumber"] = invoiceNumber
	postData["InvalidReason"] = "canceled"

	urlValues := MapToURLValues(postData)

	merchantID := creditCardConfig[kkapp.App.Debug].InvoiceMerchantID
	hashKey := creditCardConfig[kkapp.App.Debug].InvoiceHashKey
	hashIV := creditCardConfig[kkapp.App.Debug].InvoiceHashIV

	// payLoad send request to spagateway
	payLoad := url.Values{}
	payLoad.Add("MerchantID_", merchantID)
	payLoad.Add("PostData_", Encrypt(urlValues.Encode(), hashKey, hashIV))

	invoiceURL := creditCardConfig[kkapp.App.Debug].InvoiceURL
	invoiceCancelURL := strings.Replace(invoiceURL, "invoice_issue", "invoice_invalid", 1)
	req, _ := http.NewRequest("POST", invoiceCancelURL, strings.NewReader(payLoad.Encode()))
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	resp, err := curl.Do(req)
	if err != nil {
		return
	}

	statuscode := resp.StatusCode
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return
	}
	resp.Body.Close()

	zlog.Info("ezpay response").
		Int("status_code", statuscode).
		Str("body", string(body)).
		Send()

	err = json.Unmarshal(body, &response)
	if err != nil {
		return
	}

	if response.Status != "SUCCESS" {
		err = errors.New(response.Message)
		return
	}

	db := kkapp.App.DbUser.Master()
	sql := `UPDATE invoices SET canceled_at = NOW() WHERE invoice_number = $1;`
	_, err = db.Exec(sql, invoiceNumber)
	if err != nil {
		return
	}

	return
}

// CreateInvoice
func (user *CreditCardUser) CreateInvoice(order *dbuser.Order) (kkresp model.KKResp, err error) {
	var postData map[string]interface{}
	var response invoiceResponse
	var recipient, recipientAddress, buyerEmail string

	log.Println("[INFO] INVOICE", order)
	if user.Product == nil {
		err = user.LoadProductByID(order.ProductID)
		if err != nil {
			return
		}
	}
	if user.Recipient.Valid {
		recipient = user.Recipient.String
	} else {
		recipient = " " // at least one space
	}

	if user.RecipientAddress.Valid {
		recipientAddress = user.RecipientAddress.String
	}

	if user.PaymentEmail.Valid {
		buyerEmail = user.PaymentEmail.String
	} else {
		buyerEmail = "<EMAIL>"
	}

	postData = make(map[string]interface{})

	// postData
	postData["RespondType"] = "JSON"
	postData["Version"] = "1.4"
	postData["TimeStamp"] = time.Now().Unix()
	postData["MerchantOrderNo"] = order.ID
	postData["Status"] = "1"
	postData["BuyerName"] = recipient
	postData["BuyerEmail"] = buyerEmail
	postData["BuyerAddress"] = recipientAddress
	postData["TaxType"] = "1"
	postData["TaxRate"] = order.TaxRate
	postData["Amt"] = order.PriceNoTax
	postData["TaxAmt"] = order.Price - order.PriceNoTax
	postData["TotalAmt"] = order.Price
	postData["ItemName"] = user.Product.ItemName
	postData["ItemCount"] = 1
	postData["ItemUnit"] = user.Product.ItemUnit

	var printFlag string
	if user.TaxID.Valid {
		// not support yet
		postData["Category"] = "B2B"
		postData["PrintFlag"] = "Y"
		postData["ItemPrice"] = order.PriceNoTax
		postData["ItemAmt"] = order.PriceNoTax
		printFlag = "Y"
	} else {
		postData["Category"] = "B2C"
		postData["PrintFlag"] = "N"
		postData["ItemPrice"] = order.Price
		postData["ItemAmt"] = order.Price
		printFlag = "N"
	}

	if user.CaringCode.Valid {
		postData["LoveCode"] = user.CaringCode.String
	} else if user.CarrierType.Valid {
		var carrierValue string
		if user.CarrierValue.Valid {
			carrierValue = strings.TrimSpace(strings.ToUpper(user.CarrierValue.String))
		}

		switch user.CarrierType.String {
		case "0": // 手機條碼載具
			if carrierValue == "" || !reMobileBardCode.MatchString((carrierValue)) {
				postData["PrintFlag"] = "Y"
				printFlag = "Y"
			}

		case "1": // 自然人憑證載具
			if carrierValue == "" || !reCitizenPersonalCertificate.MatchString((carrierValue)) {
				postData["PrintFlag"] = "Y"
				printFlag = "Y"
			}
		case "2": // 智付寶載具
			if user.PaymentEmail.Valid {
				carrierValue = user.PaymentEmail.String
			} else if user.Phone.Valid {
				carrierValue = user.Phone.String
			} else {
				if len(user.ID) > 50 {
					carrierValue = user.ID[0:50]
				} else {
					carrierValue = user.ID
				}
			}
		default:
			postData["PrintFlag"] = "Y"
			printFlag = "Y"
		}

		if printFlag == "N" {
			postData["CarrierType"] = user.CarrierType.String
			postData["CarrierNum"] = carrierValue
		}
	} else {
		// 當 Category=B2C 時,若 CarrierType、LoveCode 參數皆為空值,則此參數必填 Y
		postData["PrintFlag"] = "Y"
	}

	var comment string
	switch order.PaymentType {
	case "credit_card":
		if user.CreditCard4No.Valid {
			comment = fmt.Sprintf("信用卡末四碼: %s", user.CreditCard4No.String)
		}
	case "cvs_code":
		comment = fmt.Sprintf("訂單編號: %s", order.ID)
	}
	postData["Comment"] = comment

	urlValues := MapToURLValues(postData)

	merchantID := creditCardConfig[kkapp.App.Debug].InvoiceMerchantID
	hashKey := creditCardConfig[kkapp.App.Debug].InvoiceHashKey
	hashIV := creditCardConfig[kkapp.App.Debug].InvoiceHashIV

	// payLoad send request to spagateway
	payLoad := url.Values{}
	payLoad.Add("MerchantID_", merchantID)
	payLoad.Add("PostData_", Encrypt(urlValues.Encode(), hashKey, hashIV))

	invoiceURL := creditCardConfig[kkapp.App.Debug].InvoiceURL
	req, _ := http.NewRequest("POST", invoiceURL, strings.NewReader(payLoad.Encode()))
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	resp, err := curl.Do(req)

	if err != nil {
		// handle error, cancel fail rollback
		log.Println("[ERROR]", err)
		err = errors.New("Network error")
		return
	}

	statuscode := resp.StatusCode
	body, err := ioutil.ReadAll(resp.Body)

	if err != nil {
		log.Println("[ERROR]", err)
		err = errors.New("Network error")
		return
	}
	resp.Body.Close()

	json.Unmarshal(body, &response)

	log.Println("[INFO] ezpay response body:", string(body))
	log.Println("[INFO] ezpay response statusCode:", statuscode)

	kkresp = model.MakeOk()
	// fail
	if response.Status != "SUCCESS" && response.Status != "LIB10003" {
		log.Println("[ERROR] Payment - create invoice failed")
		kkresp.Status.Subtype = response.Status
		kkresp.Status.Message = response.Message
		kkresp.Status.Type = "FAIL"
		return
	}

	var result invoiceResult
	if response.Message == "開立成功(此發票已重覆開立)" || response.Status == "LIB10003" {
		// already had this order invoice
		// let's fetch the result
		response, err = user.SearchInvoice(order)
		// if search fail
		if response.Status != "SUCCESS" {
			log.Println("[ERROR] Payment - create invoice failed")
			kkresp.Status.Subtype = response.Status
			kkresp.Status.Message = response.Message
			kkresp.Status.Type = "FAIL"
			return kkresp, err
		}

		// got the invice
		json.Unmarshal([]byte(response.Result), &result)
	} else if response.Status == "SUCCESS" {
		// checkcode
		err = json.Unmarshal([]byte(response.Result), &result)
		if err != nil {
			log.Println("[ERROR] invoice json format", err)
		}
		checkData := url.Values{}
		checkData.Add("InvoiceTransNo", result.InvoiceTransNo)
		checkData.Add("MerchantID", result.MerchantID)
		checkData.Add("MerchantOrderNo", result.MerchantOrderNo)
		checkData.Add("RandomNum", result.RandomNum)
		checkData.Add("TotalAmt", fmt.Sprintf("%v", result.TotalAmt))
		if !CheckCode(checkData.Encode(), result.CheckCode, hashKey, hashIV) {
			err = errors.New("Transaction check code is inconsistence")
			return
		}
	}

	// not validate invoice
	if result.InvoiceNumber == "" {
		log.Println("[ERROR] Payment - create invoice failed")
		kkresp.Status.Subtype = response.Status
		kkresp.Status.Message = response.Message
		kkresp.Status.Type = "FAIL"
		return kkresp, err
	}

	// insert invoice
	var invoice dbuser.Invoice
	invoice.OrderID = order.ID
	invoice.TotalAmount = fmt.Sprintf("%v", result.TotalAmt)
	invoice.InvoiceNumber = result.InvoiceNumber
	invoice.RandomNumber = result.RandomNum
	invoice.Original = null.NewString(response.Result, true)
	invoiceTime, err := time.Parse("2006-01-02 15:04:05-07", fmt.Sprintf("%s+08", result.CreateTime))
	if err == nil {
		invoice.CreatedAt = null.NewTime(invoiceTime.UTC(), true)
	} else {
		invoice.CreatedAt = null.NewTime(time.Now().Truncate(time.Second).UTC(), true)
	}
	db := kkapp.App.DbUser.Master()
	_, err = db.NamedExec(sqlshare["insertinvoice"], invoice)
	return
}

func (user *CreditCardUser) CheckCreditCardValid() error {
	var err error
	if len(user.Product.Bundle.Prefix) > 0 && user.CardNo != "" {
		// have credit card prefix rule need to check, any one match
		var pass bool
		for _, prefixNum := range user.Product.Bundle.Prefix {
			if strings.HasPrefix(user.CardNo, prefixNum) {
				pass = true
				break
			}
		}
		if !pass {
			log.Printf("[ERROR] userid:%s - invalid card number of bundled prefix \n", user.ID)
			err = errors.New("Invalid credit card")
			return err
		}
	}
	return nil
}

func (user *CreditCardUser) IsQualified(qualificationProof string) (string, bool) {
	// product has email domain restrictions
	qual := user.Product.Bundle.Qualification
	if qual == "email_domain" && len(user.Product.Bundle.EmailDomains) > 0 {
		zlog.Info("credit card payment: validate email domain").
			Str("product_name", user.Product.Name).
			Str("user_id", user.ID).
			Str("qualification_proof", qualificationProof).Send()
		for _, domainSuffix := range user.Product.Bundle.EmailDomains {
			emailSplited := strings.Split(qualificationProof, "@")
			// the length that email splited by "@" should be 2
			if len(emailSplited) != 2 {
				return "", false
			}
			userEmailDomain := emailSplited[1]
			if strings.HasSuffix(userEmailDomain, domainSuffix) {
				return qualificationProof, true
			}
		}
	} else if qual == "phone" {
		num, err := validator.NormalizeTaiwanMobileNum(qualificationProof)
		return num, err == nil
	}
	return "", true
}

// Deal the user order
// 1. a new order with a new expiration date
// 2. update user state such as expired, membership, auto_renew
// 3. send amplitude event to tell if the user is renewed or upgraded
func (user *CreditCardUser) Deal(order *dbuser.Order) (resp model.KKResp, err error) {

	var useTokenValue bool
	var isRenewOrder bool

	if order == nil {
		order, err = user.GetOrder()
	}

	if err == nil {
		// repeat order found, renew
		// there is a pendding order renew it
		err = user.LoadProductByID(order.ProductID)
		if err != nil {
			log.Printf("[ERROR] userid:%s - invalid product name \n", user.ID)
			err = errors.New("Invalid product name")
			user.SendPaymentError(order, err)
			return
		}
		useTokenValue = true
		if user.CardNo == "" {
			// make sure no credit card number
			isRenewOrder = true
		}
	}

	// if not load product yet
	if user.Product == nil && user.ProductName != "" {
		if _, ok := user.ValidProduct(); !ok {
			log.Printf("[ERROR] userid:%s - invalid product name \n", user.ID)
			err = errors.New("Invalid product name")
			user.SendPaymentError(order, err)
			return
		}
	}
	// no exist order
	if errors.Is(err, sql.ErrNoRows) {
		// create new order
		order, err = user.NewOrder()
		if err != nil {
			log.Printf("[ERROR] userid:%s - new order failed - err:%v \n", user.ID, err)
			user.SendPaymentError(order, err)
			return
		}
	}

	if order == nil {
		log.Printf("[ERROR] userid:%s - new order failed - database error \n", user.ID)
		err = errors.New("Database error")
		user.SendPaymentError(order, err)
		return
	}

	////////////////////////////////////////
	// authorize order
	if !(useTokenValue && order.Price == 0) {
		// repeat free order do not need to authorize
		resp, err = user.Authorize(order, useTokenValue)
		message, _ := resp.Status.Message.(string) // _ avoid cast panic
		if useTokenValue && resp.Status.Subtype == "TRA10075" && strings.Contains(message, "已存在相同訂單編號") &&
			strings.Contains(message, order.ID) {
			// must renew order and realized already
			log.Printf("[INFO] userid:%s - credit card transaction order had been realized - order id:%s \n", user.ID, order.ID)
		} else if err != nil || resp.Status.Type == "FAIL" {
			if err != nil && err.Error() == "Network error" {
				log.Printf("[ERROR] userid:%s - network error - err:%v \n", user.ID, err)
				if isRenewOrder {
					// network error, extend order date only if it's an renew order
					user.Extend(order)
				} else {
					// credit card not renew meet network error
					// fail this order
					user.FailOrder(order, isRenewOrder)
				}
				user.SendPaymentError(order, err)
				return
			}

			// authorize fail
			log.Printf("[INFO] userid:%s - credit card authorize failed - response:%#v, err:%v \n", user.ID, resp, err)
			respBytes, _ := json.Marshal(resp)
			order.Info = null.NewString(string(respBytes), true)
			if isRenewOrder {
				allowGrace := user.AllowGracePeriod(message, order)
				log.Printf("[INFO] userID:%s orderID:%s allowGrace:%v", user.ID, order.ID, allowGrace)
				user.saveNotification(&resp, order, allowGrace)

				if allowGrace {
					err = user.ExtendGracePeriod(order)
					if err == nil {
						user.sendGracedEmail(&resp, order)
						log.Printf("[INFO] userID:%s orderID:%s send grace mail \n", user.ID, order.ID)
						return
					}
				}

				user.sendFailedEmail(&resp, order)
			}

			// finally fail order
			user.FailOrder(order, isRenewOrder)
			err = errors.New("Invalid credit card")
			return
		}
	}

	////////////////////////////////////////
	// if price == 0 and very first order deauthorize
	if order.Price == 0 && !useTokenValue {
		resp, err = user.DeAuthorize(order)
		if err != nil || resp.Status.Type == "FAIL" {
			log.Printf("[ERROR] userID:%s orderID:%s deauthorize failed \n", user.ID, order.ID)
			return resp, err
		}
	}

	////////////////////////////////////////
	// realize
	err = user.Realize(order, isRenewOrder)
	if err != nil {
		err = errors.New("Database error")
		log.Printf("[ERROR] userID:%s orderID:%s database error \n", user.ID, order.ID)
		return
	}

	// paytype
	// work with kkapp/model/playback/playback.go
	if user.Product != nil && strings.HasPrefix(user.Product.Name, "product.") {
		// set expire playback key
		playback.NewPaytype(user.ID, user.Product.Name, order.EndDate.Unix())
	}

	////////////////////////////////////////
	// order had realized, send event
	if useTokenValue {
		// repeated
		user.SendTransactionEvent("repeated")
	} else {
		// new order
		user.SendTransactionEvent("upgraded")
	}

	////////////////////////////////////////
	// send subscribe email, not useTokenValue
	if !useTokenValue {
		// new order
		var mailData email.SubscribeEmailData
		mailData.ProductName = user.Product.ItemName
		mailData.ItemUnit = user.Product.ItemUnit
		mailData.Price = fmt.Sprintf("%d", user.Product.Price)
		if user.Product.AutoRenew {
			mailData.IsAutoRenew = true
		}
		if order.Price == 0 && user.Product.AutoRenew {
			mailData.IsTrialAndAutoRenew = true
		}
		mailData.OrderID = order.ID
		mailData.OrderDate = order.OrderDate.In(datetimer.LocationTaipei)
		mailData.CancelDate = order.EndDate.AddDate(0, 0, -3)
		mailData.ExpireDate = time.Unix(user.ExpiredAt, 0)
		if len(user.CreditCard6No.String) > 4 {
			mailData.PaymentType = fmt.Sprintf("%s **** **** %s", user.CreditCard6No.String[0:4], user.CreditCard4No.String)
		}
		mail := email.NewSubscribeEmail(mailData)

		client := email.NewClient()
		toAddress := user.PaymentEmail.String
		go client.Send(mail, toAddress)
	}

	////////////////////////////////////////
	//  next order
	if nextOrder, err := user.CheckCreateNextOrder(order); err != nil {
		zlog.Error("kkapp kkpayment credit card deal: fail to create nextOrder").Err(err).Str("userID", user.ID).Str("orderID", order.ID).Send()
	} else if nextOrder != nil { // no nextOrder if the product is not autoRenew
		zlog.Info("kkapp kkpayment credit card deal: check nextOrder").
			Str("next order ID", nextOrder.ID).
			Str("order ID", order.ID).
			Str("userID", order.UserID).
			Send()

	}

	data := make(map[string]interface{})
	data["order_id"] = order.ID
	resp.Data = data

	////////////////////////////////////////
	// invoice
	if order.Price > 0 {
		invoiceResp, invoiceErr := user.CreateInvoice(order)
		if invoiceErr != nil || invoiceResp.Status.Type == "FAIL" {
			errBytes, _ := json.Marshal(invoiceResp)
			errStr := string(errBytes)
			if err != nil {
				log.Printf("[ERROR] userID:%s orderID:%s create invoice error:%#v \n", user.ID, order.ID, invoiceErr)
				errStr = fmt.Sprintf("%s\n%s", errStr, invoiceErr.Error())
			}
			user.SendInvoiceError(order, errors.New(errStr))
		}

		// Send facebook conversion events
		if kkapp.App.Env == "prod" && !isRenewOrder {
			db := kkapp.App.DbUser.Slave().Unsafe()
			productPackageService := wrapper.NewProductPackageService(db, nil)

			productName := user.Product.Name
			if productPackage, err := productPackageService.GetByProductID(order.ProductID); err != nil {
				zlog.Warn("failed to get product package by product id").
					Str("user_id", user.ID).
					Int64("product_id", order.ProductID).Send()
			} else if productPackage != nil {
				productName = productPackage.PackageName()
			}

			email := user.Email.String
			if email == "" {
				email = user.PaymentEmail.String
			}

			data := facebook.PurchaseEventData{
				OrderNumber:  order.ID,
				OrderPrice:   order.Price,
				ProductPrice: user.Product.Price,
				ProductID:    user.Product.Name,
				ProductName:  productName,
				Email:        email,
				Phone:        user.Phone.String,
				Gender:       user.Gender.String,
				Birthday:     user.Birthday.String,
				Quantity:     1,
			}
			var conversionEvents facebook.ConversionEvents
			conversionEvents.Append(facebook.BuildPurchaseEvent(data))
			go facebook.SendConversionEvents(conversionEvents)

		}
	}

	log.Printf("[INFO] userID:%s orderID:%s resp:%#v \n", user.ID, order.ID, resp)
	return
}

// NewCreditCardUser get an CreditCard user via userID
func NewCreditCardUser(userID string) (user *CreditCardUser, err error) {
	user = new(CreditCardUser)
	db := kkapp.App.DbUser.Slave()
	err = db.Get(user, sqlcredit["user"], userID)
	if err != nil {
		return nil, err
	}
	return user, nil
}

// CreditCardRenew renew a credit_card user
func CreditCardRenew(userinfo model.UserInfo) {
	var err error
	var order *dbuser.Order
	var ccUser *CreditCardUser

	// error handle
	defer func() {
		if err != nil {
			zlog.Error("CreditCardRenew: error occurred").Err(err).Str("userID", userinfo.Id).Send()
			if ccUser.TransactionOrder == nil {
				return
			}
			ccUser.SendRenewError(ccUser.TransactionOrder, err)
		}
	}()

	ccUser, err = NewCreditCardUser(userinfo.Id)
	if err != nil {
		err = fmt.Errorf("cannot get user: %w", err)
		return
	}

	order, err = ccUser.GetRenewOrder()
	if err != nil || order == nil {
		err = fmt.Errorf("no pending order to renew: %w", err)
		return
	}

	go requestBillingDecide(userinfo.Id, userinfo.PaymentType, order.ProductName.String)

	var resp model.KKResp
	resp, err = ccUser.Deal(order)
	if err != nil {
		err = fmt.Errorf("creditCard deal error: %w", err)
		return
	}
	zlog.Info("CreditCardRenew: success").Str("userID", userinfo.Id).Interface("resp", resp).Send()
}

func requestBillingDecide(userID string, paymentType string, productName string) payment.BillingDecideResultType {
	var deciderParams = map[string]string{}
	deciderParams["payment_type"] = paymentType
	deciderParams["identifier"] = userID
	deciderParams["product_identifier"] = productName

	detail, result := payment.Decide(deciderParams)
	if result == payment.PAYMENT_CONFLICTS_ERROR || result == payment.USER_ALREADY_SUBSCRIBED_ERROR {
		zlog.Error("Payment conflict").Fields(detail).Send()
	} else if result == payment.REQUEST_ERROR {
		zlog.Error("Decider request error").Fields(deciderParams).Send()
	} else if result == payment.INTERNAL_SERVER_ERROR {
		zlog.Error("Decider internal server error").Send()
	}
	return result
}

// PostCreditCard for /v3/payment/creditcard post handler for android create order
func PostCreditCard(w http.ResponseWriter, r *http.Request) {
	var err error
	var payLoad CreditCardPost
	var couponUser *CouponUser

	response := model.MakeOk()

	defer func() {
		if err != nil {
			log.Println("[ERROR] creditcard fail", err)
			errStr := err.Error()
			response.Status.Type = "FAIL"

			if code, ok := errorCode[errStr]; ok {
				response.Status.Subtype = code
				response.Status.Message = errStr
			} else {
				response.Status.Subtype = "Unknow"
				response.Status.Message = errStr
			}
			kkapp.App.Render.JSON(w, http.StatusBadRequest, response)
		} else {
			kkapp.App.Render.JSON(w, http.StatusOK, response)
		}
	}()

	access := r.Context().Value(middleware.KeyAccessUser).(modelmw.AccessUser)
	userid := access.UserID

	jsdecoder := json.NewDecoder(r.Body)
	if err = jsdecoder.Decode(&payLoad); err != nil {
		err = errors.New("Invalid empty data")
		return
	}

	// data check
	if payLoad.Email == "" || payLoad.CardNo == "" || payLoad.CardExp == "" || payLoad.CardCvc == "" || payLoad.ProductName == "" {
		err = errors.New("Invalid parameters")
		return
	}

	var donateInvoice, saveToCarrier bool

	if payLoad.CaringCode != "" {
		donateInvoice = true
	}

	// 0=手機條碼載具
	// 1=自然人憑證條碼載具
	// 2=智付寶載具
	if payLoad.CarrierType != "" {
		if payLoad.CarrierType != "0" && payLoad.CarrierType != "1" && payLoad.CarrierType != "2" {
			err = errors.New("Invalid carrier type")
			return
		}

		if payLoad.CarrierValue != "" {
			saveToCarrier = true
		} else if payLoad.Recipient != "" && payLoad.RecipientAddress != "" {
			saveToCarrier = true
		}
	}

	if !donateInvoice && !saveToCarrier {
		err = errors.New("Must to have one of carrier or caring code")
		return
	}

	// user check
	ccUser, err := NewCreditCardUser(userid)
	if err != nil {
		log.Println("[ERROR] NewCreditCardUser", err)
		err = errors.New("Database error")
		return
	}

	ccUser.PaymentEmail = null.NewString(payLoad.Email, true)
	ccUser.CardNo = payLoad.CardNo
	ccUser.CardCvc = payLoad.CardCvc
	ccUser.CardExp = payLoad.CardExp
	ccUser.ProductName = payLoad.ProductName
	ccUser.CaringCode = null.NewString("", false)

	if payLoad.Recipient != "" {
		ccUser.Recipient = null.NewString(payLoad.Recipient, true)
	}

	if payLoad.RecipientAddress != "" {
		ccUser.RecipientAddress = null.NewString(payLoad.RecipientAddress, true)
	}

	if payLoad.CarrierType != "" {
		ccUser.CarrierType = null.NewString(payLoad.CarrierType, true)
	}

	if payLoad.CarrierValue != "" {
		ccUser.CarrierValue = null.NewString(payLoad.CarrierValue, true)
	}

	if payLoad.CaringCode != "" {
		ccUser.CaringCode = null.NewString(payLoad.CaringCode, true)
	}

	// product check
	if _, ok := ccUser.ValidProduct(); !ok {
		log.Printf("[ERROR] userid:%s - invalid product name \n", ccUser.ID)
		err = errors.New("Invalid product name")
		return
	}

	// already subscribed
	if ccUser.AutoRenew && ccUser.Product.AutoRenew {
		log.Printf("[ERROR] userid:%s - user has subscribed \n", ccUser.ID)
		err = errors.New("User has subscribed")
		return
	}

	// [BEGIN] FIXME: decider needs to implement prevent user from purchasing in case user has subscribed
	// invoke decision to check whether user's payment has conflict
	//result := requestBillingDecide(userid, usermodel.PaymentInfoTypeCreditCard.String(), ccUser.Product.Name)
	//if result == payment.PAYMENT_CONFLICTS_ERROR || result == payment.USER_ALREADY_SUBSCRIBED_ERROR {
	//	err = errors.New("User has subscribed")
	//	return
	//}
	permissionService := kkapp.App.PermissionService
	err = permissionService.Grant(&permission.RequestPromoteToVIP{
		UserID:        userid,
		BillingClient: kkapp.App.BillingClient,
	})
	if kktverror.IsInternalErr(err) {
		err = errors.New("Internal error")
		return
	} else if err != nil {
		err = errors.New("User has subscribed")
		return
	}
	//[END]

	// handle product NeedRedeemCode
	if ccUser.Product.Bundle.NeedRedeemCode {
		if payLoad.RedeemCode == "" {
			log.Printf("[ERROR] userid:%s - empty bundled redeem code \n", ccUser.ID)
			err = errors.New("Invalid parameters")
			return
		}

		// new instance with miniumn value
		// save a user query
		couponUser = new(CouponUser)
		couponUser.ID = ccUser.ID
		couponUser.Role = ccUser.Role
		couponUser.Type = ccUser.Type
		couponUser.ExpiredAt = ccUser.ExpiredAt
		couponUser.AutoRenew = ccUser.AutoRenew
		couponUser.CouponCode = payLoad.RedeemCode

		// check redeem code
		err = couponUser.ValidateCode()
		if err != nil {
			log.Printf("[ERROR] userid:%s - check coupon code:%s failed - err:%s \n", ccUser.ID, couponUser.CouponCode, err)
			return
		}

		// check same product_id
		if couponUser.CouponCodeGroup.ProductID.Int64 != ccUser.Product.ID {
			log.Printf("[ERROR] userid:%s - not same product - coupon product id:%v creditcard product id:%v \n", ccUser.ID, couponUser.CouponCodeGroup.ProductID.Int64, ccUser.Product.ID)
			err = errors.New("Invalid product name")
			return
		}
	}

	// check credit card prefix
	if err = ccUser.CheckCreditCardValid(); err != nil {
		return
	}

	// if product bundle set any rules for user to purchase the product,
	// and need user to enter qualification proof on payment page
	if ccUser.Product.NeedQualificationCheck() {
		qualificationProof := strings.TrimSpace(payLoad.QualificationProof)
		if qualificationProof == "" {
			err = errors.New("Qualification proof is empty")
			return
		}

		qualification, pass := ccUser.IsQualified(qualificationProof)
		if !pass {
			zlog.Error("credit card payment: not qualified for purchasing the product").
				Str("product_name", ccUser.Product.Name).
				Str("user_id", ccUser.ID).
				Str("qualification_proof", qualificationProof).Send()
			err = errors.New("Not qualified for purchasing the product")
			return
		}
		ccUser.QualificationProof = qualification
	}

	rid := uuid.New().String()
	broadcaster := broadcasting.GetBroadcaster()
	broadcaster.SyncEmit(events.SignalUserWillBeUpdated, &events.UserWillBeUpdatedEvent{
		RequestID: rid, UserID: userid,
	})
	// purchase with credit card
	response, err = ccUser.Deal(nil)
	if err != nil {
		//Send Amplitude events for family plan - failed
		go amplitude.SendAccountPurchaseFamilyPlan(ccUser.ID, ccUser.Product.Name, ccUser.Product.Bundle.Family, err)

		return
	}

	//Send Amplitude events for family plan - success
	go amplitude.SendAccountPurchaseFamilyPlan(ccUser.ID, ccUser.Product.Name, ccUser.Product.Bundle.Family)

	// handle product NeedRedeemCode
	if ccUser.Product.Bundle.NeedRedeemCode {
		// user purchased success, apply it
		log.Println("[INFO] coupon purchase", couponUser.ID, couponUser.CouponCode)
		couponUser.Deal()
	}

	broadcaster.Emit(events.SignalUserUpdated, &events.UserUpdatedEvent{
		RequestID: rid, UserID: userid,
		UpdateReason: "upgrade via credit card payment",
		ModifiedBy:   usermodel.AuditModifierTypeUser.String(),
		ModifierID:   userid,
	})
}

// PutCreditCard for /v3/payment/creditcard PUT handler for update user's creditcard data
func PutCreditCard(w http.ResponseWriter, r *http.Request) {
	var err error
	var payLoad CreditCardPut
	var userid string

	response := model.MakeOk()
	user := r.Context().Value("user")

	defer func() {
		if err != nil {
			log.Println("[ERROR] putcreditcard - update creditcard failed", err)
			errStr := err.Error()
			response.Status.Type = "FAIL"

			if code, ok := errorCode[errStr]; ok {
				response.Status.Subtype = code
				response.Status.Message = errStr
			} else {
				response.Status.Subtype = "Unknow"
				response.Status.Message = errStr
			}
			kkapp.App.Render.JSON(w, http.StatusBadRequest, response)
		} else {
			kkapp.App.Render.JSON(w, http.StatusOK, response)
		}
	}()

	if user != nil {
		if userModel, ok := user.(model.JwtUser); ok && !userModel.IsGuest() {
			userid = userModel.Sub
		}
	}

	if userid == "" {
		log.Println("[ERROR] update creditcard - userid is empty")
		err = errors.New("Invalid parameters")
		return
	}

	jsdecoder := json.NewDecoder(r.Body)
	if err = jsdecoder.Decode(&payLoad); err != nil {
		log.Println("[ERROR] update creditcard - json decode failed")
		err = errors.New("Invalid empty data")
		return
	}

	//data check
	if payLoad.CardNo == "" || payLoad.CardExp == "" || payLoad.CardCvc == "" {
		log.Println("[ERROR] update creditcard - cardcvc or cardexp or cardno is empty")
		err = errors.New("Invalid parameters")
		return
	}

	ccUser, err := NewCreditCardUser(userid)
	if err != nil {
		log.Println("[ERROR] update creditcard - NewCreditCardUser", err)
		err = errors.New("Database error")
		return
	}

	//check payment email
	//check auto_renew is true
	if ccUser.AutoRenew != true || !ccUser.PaymentEmail.Valid {
		log.Println("[ERROR] update creditcard - auto_renew is false or payment email is empty")
		err = errors.New("Invalid parameters")
		return
	}

	//assign value
	ccUser.CardNo = payLoad.CardNo
	ccUser.CardCvc = payLoad.CardCvc
	ccUser.CardExp = payLoad.CardExp

	//https://github.com/KKTV/kktv-payment-gateway/blob/16adafc6cf37524295f00f89c59b393ab556a7f0/app/payment/payment.js#L89
	product := new(Product)
	product.ItemName = "KKTV 1 month"
	ccUser.Product = product

	//TODO payload might have RecipientAddress, carrier_type, carrier_value

	//authorize
	isUseToken := false
	order := new(dbuser.Order)

	//fake ID for authorization only
	idstr := "FKT%s%s"
	datestr := time.Now().UTC().Format("20060102")
	rNum := RandomNumber(7)
	fakeID := fmt.Sprintf(idstr, datestr, rNum)

	order.ID = fakeID
	order.Price = 0
	resp, err := ccUser.Authorize(order, isUseToken)

	if err != nil || resp.Status.Type == "FAIL" {
		log.Printf("[INFO] credit card authorize failed - userid:%s order id:%s \n", ccUser.ID, order.ID)
		err = errors.New("authorize failed")
		return
	}

	//deauthorize
	if order.Price == 0 && !isUseToken {
		resp, err = ccUser.DeAuthorize(order)
		if err != nil || resp.Status.Type == "FAIL" {
			log.Printf("[INFO] credit card deauthorize failed - userID:%s orderID:%s\n", ccUser.ID, order.ID)
			err = errors.New("deauthorize failed")
			return
		}
	}

	//finally update payment_info
	paymentInfo := new(dbuser.PaymentInfo)
	paymentInfo.UserID = ccUser.ID
	paymentInfo.Email = ccUser.PaymentEmail
	paymentInfo.PaymentType = null.String{NullString: sql.NullString{String: creditCardPaymentType, Valid: true}}
	paymentInfo.CaringCode = ccUser.CaringCode
	paymentInfo.CarrierType = ccUser.CarrierType
	paymentInfo.CarrierValue = ccUser.CarrierValue
	paymentInfo.Recipient = ccUser.Recipient
	paymentInfo.RecipientAddress = ccUser.RecipientAddress
	paymentInfo.CreditCard4NO = ccUser.CreditCard4No
	paymentInfo.CreditCard6NO = ccUser.CreditCard6No
	paymentInfo.CreditCardTokenTerm = ccUser.CreditCardTokenTerm
	paymentInfo.CreditCardTokenValue = ccUser.CreditCardTokenValue
	paymentInfo.FamilyID = ccUser.FamilyID

	log.Println("[INFO] upsert payment_info", paymentInfo)
	db := kkapp.App.DbUser.Master()
	_, err = db.NamedExec(sqlshare["upsertpayment"], paymentInfo)
	if err != nil {
		log.Println("[ERROR] update payment_info failed", ccUser.ID, err)
		err = errors.New("deauthorize failed")
		return
	}

	_, err = db.Exec(sqlcredit["updateNotification"], ccUser.ID, creditCardPaymentType)
	if err != nil {
		log.Println("[ERROR] remove user's server_notifications failed", ccUser.ID, err)
		err = errors.New("Database error")
		return
	}
}
