package kkpayment_test

import (
	"testing"

	"github.com/KKTV/kktv-api-v3/kkapp/kkpayment"
)

func TestGetSubscriptionMessage(t *testing.T) {
	got := kkpayment.GetSubscriptionMessage("0975123456", "0", "0", "tstar.duration12month129")
	if got != "會員帳號：0975123456\n點擊連結進行註冊，享受追劇樂趣！\nhttps://kktv.me/p/0975123456" {
		t.Errorf("\n\nGetSubscriptionMessage(\"0975123456\", \"0\", \"0\", \"tstar.duration12month129\") = %s;\n\nWant:\n會員帳號：0975123456\n點擊連結進行註冊，享受追劇樂趣！\nhttps://kktv.me/p/0975123456\n\n", got)
	}

	got = kkpayment.GetSubscriptionMessage("0975123456", "0", "0", "tstar.duration24months100student")
	if got != "會員帳號：0975123456\n點擊連結進行註冊，享受追劇樂趣！\nhttps://kktv.me/p/0975123456" {
		t.Errorf("\n\nGetSubscriptionMessage(\"0975123456\", \"0\", \"0\", \"tstar.duration24months100student\") = %s;\n\nWant:\n會員帳號：0975123456\n點擊連結進行註冊，享受追劇樂趣！\nhttps://kktv.me/p/0975123456\n\n", got)
	}

	got = kkpayment.GetSubscriptionMessage("0975123456", "0", "0", "tstar.duration24months139student")
	if got != "會員帳號：0975123456\n點擊連結進行註冊，享受追劇樂趣！\nhttps://kktv.me/p/0975123456" {
		t.Errorf("\n\nGetSubscriptionMessage(\"0975123456\", \"0\", \"0\", \"tstar.duration24months139student\") = %s;\n\nWant:\n會員帳號：0975123456\n點擊連結進行註冊，享受追劇樂趣！\nhttps://kktv.me/p/0975123456\n\n", got)
	}

	got = kkpayment.GetSubscriptionMessage("0975123456", "1", "0", "tstar.duration12month129")
	if got != "會員帳號：0975123456\n點擊連結進行註冊，享受追劇樂趣！\nhttps://kktv.me/p/0975123456" {
		t.Errorf("\n\nGetSubscriptionMessage(\"0975123456\", \"1\", \"0\", \"tstar.duration12month129\") = %s;\n\nWant:\n會員帳號：0975123456\n點擊連結進行註冊，享受追劇樂趣！\nhttps://kktv.me/p/0975123456\n\n", got)
	}

	got = kkpayment.GetSubscriptionMessage("0975123456", "0", "0", "")
	if got != "會員帳號：0975123456\n點擊連結進行註冊，享受追劇樂趣！\nhttps://kktv.me/p/0975123456" {
		t.Errorf("\n\nGetSubscriptionMessage(\"0975123456\", \"0\", \"0\", \"\") = %s;\n\nWant:\n會員帳號：0975123456\n點擊連結進行註冊，享受追劇樂趣！\nhttps://kktv.me/p/0975123456\n\n", got)
	}
}
