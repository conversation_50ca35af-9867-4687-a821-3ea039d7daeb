package email

import (
	"fmt"
	"strings"
	"time"

	"github.com/KKTV/kktv-api-v3/pkg/datetimer"
)

var (
	EmailTemplateFrame = []string{`<!DOCTYPE html>
<html>
<head>
<title>KKTV Email</title>

<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="X-UA-Compatible" content="IE=edge" />
<style type="text/css">
    /* CLIENT-SPECIFIC STYLES */
    body, table, td, a{-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%;}
    /* Prevent WebKit and Windows mobile changing default text sizes */
    table, td{mso-table-lspace: 0pt; mso-table-rspace: 0pt;}
    /* Remove spacing between tables in Outlook 2007 and up */
    img{-ms-interpolation-mode: bicubic;}
    /* Allow smoother rendering of resized image in Internet Explorer */

    /* RESET STYLES */
    img{border: 0; height: auto; line-height: 100%; outline: none; text-decoration: none;}
    table{border-collapse: collapse !important;}
    body{height: 100% !important; margin: 0 !important; padding: 0 !important; width: 100% !important;
        font-family: Helvetica,Arial,'Microsoft Jhenghei','Heiti TC','PingFang TC',sans-serif;}

    .list {
        margin: 20px 0 0 10px;
    }
    /* iOS BLUE LINKS */
    a[x-apple-data-detectors] {
        color: inherit !important;
        text-decoration: none !important;
        font-size: inherit !important;
        font-family: inherit !important;
        font-weight: inherit !important;
        line-height: inherit !important;
    }

    /* MOBILE STYLES */
    @media screen and (max-width: 525px) {

        /* ALLOWS FOR FLUID TABLES */
        .wrapper {
          width: 100% !important;
            max-width: 100% !important;
        }

        /* ADJUSTS LAYOUT OF LOGO IMAGE */
        .logo img {
          margin: 0 auto !important;
        }

        /* USE THESE CLASSES TO HIDE CONTENT ON MOBILE */
        .mobile-hide {
          display: none !important;
        }

        .img-max {
          max-width: 100% !important;
          width: 100% !important;
          height: auto !important;
        }

        /* FULL-WIDTH TABLES */
        .responsive-table {
          width: 100% !important;
        }

        /* UTILITY CLASSES FOR ADJUSTING PADDING ON MOBILE */
        .padding {
          padding: 10px 5% 15px 5% !important;
        }
        .list {
            margin: 10px 0 20px 35px;
        }
        .footer {
            padding: 0 8%;
        }
        .extra-info {
            padding: 20px 5% 15px 5% !important;

        .padding-meta {
          padding: 30px 5% 0px 5% !important;
          text-align: center;
        }

        .padding-copy {
             padding: 10px 5% 10px 5% !important;
          text-align: center;
        }

        .no-padding {
          padding: 0 !important;
        }

        .section-padding {
          padding: 50px 15px 50px 15px !important;
        }

        /* ADJUST BUTTONS ON MOBILE */
        .mobile-button-container {
            margin: 0 auto;
            width: 100% !important;
        }

        .mobile-button {
            padding: 15px !important;
            border: 0 !important;
            font-size: 16px !important;
            display: block !important;
        }

    }

    /* ANDROID CENTER FIX */
	div[style*="margin: 16px 0;"] { margin: 0 !important; }

</style>
</head>
<body style="margin: 0 !important; padding: 0 !important;">
<table border="0" cellpadding="0" cellspacing="0" width="100%">
    <tr>
        <td bgcolor="#ffffff" align="center">
            <!--[if (gte mso 9)|(IE)]>
            <table align="center" border="0" cellspacing="0" cellpadding="0" width="500">
            <tr>
            <td align="center" valign="top" width="500">
            <![endif]-->
            <table border="0" cellpadding="0" cellspacing="0" width="100%" style="max-width: 500px;" class="wrapper">
                <tr>
                    <td align="center" valign="top" style="padding: 20px 0;" class="logo">
                        <a href="https://www.kktv.me" target="_blank">
                            <img alt="Logo" src="https://images.kktv.com.tw/emails/<EMAIL>" width="138" height="30" style="display: block; font-family: Helvetica, Arial, sans-serif; color: #ffffff; font-size: 16px;" border="0">
                           <!-- <img alt="Logo" src="kktv-icon.png" width="60" height="60" style="display: block; font-family: Helvetica, Arial, sans-serif; color: #ffffff; font-size: 16px;" border="0"> -->
                        </a>
                    </td>
                </tr>
            </table>
            <!--[if (gte mso 9)|(IE)]>
            </td>
            </tr>
            </table>
            <![endif]-->
        </td>
    </tr>
    <tr>
        <td bgcolor="#F9F9F9" align="center" style="padding: 40px 15px 40px 15px; border-top:solid 1px #e6e6e6;border-bottom:solid 1px #e6e6e6;" class="section-padding">
            <!--[if (gte mso 9)|(IE)]>
            <table align="center" border="0" cellspacing="0" cellpadding="0" width="500">
            <tr>
            <td align="center" valign="top" width="500">
            <![endif]-->
            <table border="0" cellpadding="0" cellspacing="0" width="100%" style="max-width: 500px;" class="responsive-table">
                <tr>
                    <td>
                        <table width="100%" border="0" cellspacing="0" cellpadding="0">
                            `, `
                        </table>
                    </td>
                </tr>
            </table>
            <!--[if (gte mso 9)|(IE)]>
            </td>
            </tr>
            </table>
            <![endif]-->
        </td>
    </tr>
    <tr>
        <td bgcolor="#ffffff" align="center" style="padding: 20px 0px;">
            <!--[if (gte mso 9)|(IE)]>
            <table align="center" border="0" cellspacing="0" cellpadding="0" width="500">
            <tr>
            <td align="center" valign="top" width="500">
            <![endif]-->
            <table width="100%" border="0" cellspacing="0" cellpadding="0" align="center" style="max-width: 500px;" class="responsive-table">
                <tr>
                    <td align="left" style="font-size: 12px; line-height: 18px; font-family: Helvetica, Arial, sans-serif; color:#666666;" class="footer">
						因為您是 KKTV VIP 會員，所以我們發送這封帳戶相關信件給您。 您可以隨時前往<a href="https://www.kktv.me/account" target="_blank" style="color: #666666;">帳號資訊</a>頁面變更設定。
                        請不要直接回覆這封信件，如果您需要協助，請前往<a href="https://help.kkbox.com/tw/zh-tw?p=kktv" target="_blank" style="color: #666666;">常見問題</a>。
                    </td>
                </tr>
            </table>
            <!--[if (gte mso 9)|(IE)]>
            </td>
            </tr>
            </table>
            <![endif]-->
        </td>
    </tr>
    <tr>
        <td bgcolor="#ffffff" align="center" style="padding: 20px 0px;">
            <!--[if (gte mso 9)|(IE)]>
            <table align="center" border="0" cellspacing="0" cellpadding="0" width="500">
            <tr>
            <td align="center" valign="top" width="500">
            <![endif]-->
            <table width="100%" border="0" cellspacing="0" cellpadding="0" align="center" style="max-width: 500px;" class="responsive-table">
                <tr>
                    <td align="center" style="font-size: 12px; line-height: 18px; font-family: Helvetica, Arial, sans-serif; color:#666666;" class="footer">
                        <a href="https://www.kktv.me" target="_blank" style="color: #666666; text-decoration: none;">KKTV</a>
                        <span style="font-family: Arial, sans-serif; font-size: 12px; color: #444444;">&nbsp;&nbsp;|&nbsp;&nbsp;</span>
                        <span style="color: #666666; text-decoration: none;">Copyright © {{.CopyrightYear}}, All rights reserved.<span>
                    </td>
                </tr>
            </table>
            <!--[if (gte mso 9)|(IE)]>
            </td>
            </tr>
            </table>
            <![endif]-->
        </td>
    </tr>
</table>

</body>
</html>`,
	}
)

type GeneralData struct {
	CopyrightYear string
}

func CopyrightYear() string {
	return fmt.Sprintf("%d", time.Now().In(datetimer.LocationTaipei).Year())
}

func GetGeneralData() GeneralData {
	return GeneralData{
		CopyrightYear: CopyrightYear(),
	}
}

func GenerateTemplate(bodyTrs []string) string {
	trs := []string{}
	for _, tr := range bodyTrs {
		trs = append(trs, "<tr><td><table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\">"+tr+"</table></td></tr>")
	}
	return EmailTemplateFrame[0] + strings.Join(trs, "") + EmailTemplateFrame[1]
}

type SubscribeEmail struct {
	Subject  string
	Template string
	Data     SubscribeEmailData
}

type SubscribeEmailData struct {
	GeneralData
	IsTrialAndAutoRenew bool
	ExpireDate          time.Time
	ItemUnit            string
	Price               string
	CancelDate          time.Time
	ProductName         string
	OrderID             string
	OrderDate           time.Time
	IsAutoRenew         bool
	PaymentType         string
}

// Subscribe Email
func NewSubscribeEmail(data SubscribeEmailData) *SubscribeEmail {
	return &SubscribeEmail{
		Subject: "KKTV VIP 訂閱確認",
		Data:    data,
	}
}

func (e *SubscribeEmail) GetTemplate() string {
	bodyTrs := []string{
		`<tr><td align="left" style="padding: 20px 0 0 0; font-size: 16px; line-height: 25px; color: #666666; font-family: Helvetica,Arial,'Microsoft Jhenghei','Heiti TC','PingFang TC',sans-serif;" class="padding">
            親愛的 VIP 會員您好，
        </td></tr>`,
		`<tr><td align="left" style="padding: 20px 0 0 0; font-size: 16px; line-height: 25px; color: #666666; font-family: Helvetica,Arial,'Microsoft Jhenghei','Heiti TC','PingFang TC',sans-serif;" class="padding">
            感謝訂閱 KKTV VIP，您的 KKTV VIP 會員資格已經開始了，可以馬上開始
            <span>
                <a style="font-size: 16px; color: #B40F42; text-decoration: none;" href="https://www.kktv.me" target="_blank">登入使用，盡情追劇 &rarr;</a>
            </span>
        </td></tr>`,
		`<tr><td align="left" style="padding: 20px 0 0 0; font-size: 16px; line-height: 25px; color: #666666; font-family: Helvetica,Arial,'Microsoft Jhenghei','Heiti TC','PingFang TC',sans-serif;" class="padding">
            {{if .IsTrialAndAutoRenew}}在
            <span>{{.ExpireDate.Year}}</span> 年
            <span>{{.ExpireDate.Format "01"}}</span> 月
            <span>{{.ExpireDate.Format "02"}}</span> 日試用到期前，
            您不會被收取任何費用。在試用到期後，訂閱自動更新時，您需要支付每期 NT$ {{.Price}} 的訂閱費用。
            請留意，若您有取消訂閱之需求，請於
            <span>{{.CancelDate.Year}}</span> 年
            <span>{{.CancelDate.Format "01"}}</span> 月
            <span>{{.CancelDate.Format "02"}}</span> 日前送出申請，我們將於會員身分到期日前三日開始進行續訂作業。{{end}}
            <br/>
            <br/>
        </td></tr>`,
		`<tr><td align="left" style="display:inline-block; padding: 0px 0 0 10px; border-left:solid 1px #ccc; font-size: 14px; line-height: 21px; color: #666666; font-family: Helvetica,Arial,'Microsoft Jhenghei','Heiti TC','PingFang TC',sans-serif;" class="list">
            產品： <span>{{.ProductName}}</span><br/>
            訂單號碼：<span>{{.OrderID}}</span><br/>
            訂購日期：<span>{{.OrderDate.Year}}</span> 年 <span>{{.OrderDate.Format "01"}}</span> 月 <span>{{.OrderDate.Day}}</span> 日 <br/>
            價格： {{if .IsTrialAndAutoRenew}}試用期後，{{end}}{{if .IsAutoRenew}}每期的費用為 ${{.Price}}<br/>{{else}}計次型 ${{.Price}}<br/>{{end}}
            付款方式：<span>{{.PaymentType}}</span> <br/>
        </td></tr>`,
		`<tr><td align="left" style="padding: 20px 0 0 0; font-size: 16px; line-height: 25px; color: #666666; font-family: Helvetica,Arial,'Microsoft Jhenghei','Heiti TC','PingFang TC',sans-serif;" class="padding">
            盡情享受追劇樂趣!<br/>
            KKTV 團隊 敬上
        </td></tr>`,
	}

	return GenerateTemplate(bodyTrs)
}

func (e *SubscribeEmail) GetSubject() string {
	return e.Subject
}

func (e *SubscribeEmail) GetData() interface{} {
	e.Data.GeneralData = GetGeneralData()
	return e.Data
}

type CreditCardFailedEmail struct {
	Subject  string
	Template string
	Data     CreditCardFailedEmailData
}

type CreditCardFailedEmailData struct {
	GeneralData
	ExpireDate   time.Time
	ErrorCode    string
	ErrorMessage string
	PaymentType  string
}

// Credit Card Failed Email
func NewCreditCardFailedEmail(data CreditCardFailedEmailData) *CreditCardFailedEmail {
	return &CreditCardFailedEmail{
		Subject: "KKTV VIP 續訂－信用卡扣款失敗通知",
		Data:    data,
	}
}

func (e *CreditCardFailedEmail) GetTemplate() string {
	bodyTrs := []string{
		`<tr><td align="left" style="padding: 20px 0 0 0; font-size: 16px; line-height: 25px; color: #666666; font-family: Helvetica,Arial,'Microsoft Jhenghei','Heiti TC','PingFang TC',sans-serif;" class="padding">
            親愛的 KKTV VIP 會員您好，
         </td></tr>`,
		`<tr><td align="left" style="padding: 20px 0 0 0; font-size: 16px; line-height: 25px; color: #666666; font-family: Helvetica,Arial,'Microsoft Jhenghei','Heiti TC','PingFang TC',sans-serif;" class="padding">
            您訂閱的 KKTV VIP 因信用卡扣款異常，故您的會員資格將於
            <span>{{.ExpireDate.Year}}</span> 年
            <span>{{.ExpireDate.Format "01"}}</span> 月
            <span>{{.ExpireDate.Format "02"}}</span> 日到期，請洽詢發卡銀行。
            <br/>
            <br/>
        </td></tr>`,
		`<tr><td align="left" style="display:inline-block; padding: 0px 0 0 10px; border-left:solid 1px #ccc; font-size: 14px; line-height: 21px; color: #666666; font-family: Helvetica,Arial,'Microsoft Jhenghei','Heiti TC','PingFang TC',sans-serif;" class="list">
            錯誤代碼：<span>{{.ErrorCode}}</span><br/>
            錯誤訊息：<span>{{.ErrorMessage}}</span><br/>
            付款方式：<span>{{.PaymentType}}</span><br/>
        </td></tr>`,
		`<tr><td align="left" style="padding: 20px 0 0 0; font-size: 16px; line-height: 25px; color: #666666; font-family: Helvetica,Arial,'Microsoft Jhenghei','Heiti TC','PingFang TC',sans-serif;" class="padding">
            KKTV 團隊 敬上
        </td></tr>`,
	}

	return GenerateTemplate(bodyTrs)
}

func (e *CreditCardFailedEmail) GetSubject() string {
	return e.Subject
}

func (e *CreditCardFailedEmail) GetData() interface{} {
	e.Data.GeneralData = GetGeneralData()
	return e.Data
}

type CreditCardGracedEmail struct {
	Subject  string
	Template string
	Data     CreditCardGracedEmailData
}

type CreditCardGracedEmailData struct {
	GeneralData
	ExpireDate   time.Time
	ErrorCode    string
	ErrorMessage string
	PaymentType  string
}

// Credit Card Graced Email
func NewCreditCardGracedEmail(data CreditCardGracedEmailData) *CreditCardGracedEmail {
	return &CreditCardGracedEmail{
		Subject: "KKTV VIP 續訂－信用卡扣款失敗通知",
		Data:    data,
	}
}

func (e *CreditCardGracedEmail) GetTemplate() string {
	bodyTrs := []string{
		`<tr><td align="left" style="padding: 20px 0 0 0; font-size: 16px; line-height: 25px; color: #666666; font-family: Helvetica,Arial,'Microsoft Jhenghei','Heiti TC','PingFang TC',sans-serif;" class="padding">
            親愛的 KKTV VIP 會員您好，
         </td></tr>`,
		`<tr><td align="left" style="padding: 20px 0 0 0; font-size: 16px; line-height: 25px; color: #666666; font-family: Helvetica,Arial,'Microsoft Jhenghei','Heiti TC','PingFang TC',sans-serif;" class="padding">
            您訂閱的 KKTV 因信用卡扣款失敗，即將在
            <span>{{.ExpireDate.Year}}</span> 年
            <span>{{.ExpireDate.Format "01"}}</span> 月
            <span>{{.ExpireDate.Format "02"}}</span> 日被取消訂閱，
            請立即至帳戶頁面
            <a href="https://www.kktv.me/account" target="_blank" style="font-weight: bold; color: #2669f0;">
                更新付款資訊
            </a>
            ，以免影響您的權益及優惠。
            <br/>
            <br/>
        </td></tr>`,
		`<tr><td align="left" style="display:inline-block; padding: 0px 0 0 10px; border-left:solid 1px #ccc; font-size: 14px; line-height: 21px; color: #666666; font-family: Helvetica,Arial,'Microsoft Jhenghei','Heiti TC','PingFang TC',sans-serif;" class="list">
            錯誤代碼：<span>{{.ErrorCode}}</span><br/>
            錯誤訊息：<span>{{.ErrorMessage}}</span><br/>
            付款方式：<span>{{.PaymentType}}</span><br/>
        </td></tr>`,
		`<tr><td align="left" style="padding: 20px 0 0 0; font-size: 16px; line-height: 25px; color: #666666; font-family: Helvetica,Arial,'Microsoft Jhenghei','Heiti TC','PingFang TC',sans-serif;" class="padding">
            KKTV 團隊 敬上
        </td></tr>`,
	}

	return GenerateTemplate(bodyTrs)
}

func (e *CreditCardGracedEmail) GetSubject() string {
	return e.Subject
}

func (e *CreditCardGracedEmail) GetData() interface{} {
	e.Data.GeneralData = GetGeneralData()
	return e.Data
}
