package kkpayment_test

import (
	"testing"

	"github.com/KKTV/kktv-api-v3/kkapp/kkpayment"
)

func TestNormalizePhoneNumber(t *testing.T) {
	got := kkpayment.NormalizePhoneNumber("0975123456")
	if got != "+886975123456" {
		t.<PERSON><PERSON><PERSON>("NormalizePhoneNumber(0975123456) = %s; want +886975123456", got)
	}

	// test illegal format, should return empty string
	got = kkpayment.NormalizePhoneNumber("a975b2356")
	if got != "" {
		t.Errorf("NormalizePhoneNumber(\"a975b2356\") = %s; want \"\"", got)
	}
}
