package kkpayment

// IAP
// https://developer.apple.com/library/archive/releasenotes/General/ValidateAppStoreReceipt/Chapters/ValidateRemotely.html#//apple_ref/doc/uid/TP40010573-CH104-SW5
// https://developer.apple.com/library/archive/releasenotes/General/ValidateAppStoreReceipt/Chapters/ReceiptFields.html#//apple_ref/doc/uid/TP40010573-CH106-SW1

import (
	"bytes"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/amplitude"
	"github.com/KKTV/kktv-api-v3/kkapp/kkutil"
	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kkapp/model/dbuser"
	kktvsns "github.com/KKTV/kktv-api-v3/kkapp/sns"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/broadcasting"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/wrapper"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/wrapper/middleware"
	"github.com/KKTV/kktv-api-v3/pkg/log"
	usermodel "github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/KKTV/kktv-api-v3/pkg/model/events"
	mwmodel "github.com/KKTV/kktv-api-v3/pkg/model/middleware"
	"github.com/KKTV/kktv-api-v3/pkg/render"
	"github.com/KKTV/kktv-api-v3/pkg/secret"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/sns"
	"github.com/google/uuid"
	"github.com/jmoiron/sqlx"
	"github.com/slack-go/slack"
	"gopkg.in/guregu/null.v3"
)

var (
	sandboxURL     = "https://sandbox.itunes.apple.com/verifyReceipt" // sandbox URL
	productionURL  = "https://buy.itunes.apple.com/verifyReceipt"     // production URL
	curl           = &http.Client{Timeout: time.Duration(time.Second * 20)}
	iapPaymentType = "iap"

	iapGracePeriodDays = 17 // a safe range for iap grace period in days

	reReceipt = regexp.MustCompile(`(?s)"latest_receipt_info":\{(.*?)\}`)

	sqliap = map[string]string{
		"order": `SELECT id, user_id, product_id, price::numeric::int, payment_type,
 start_date, end_date, order_date,
 status, created_at, realized_at, canceled_at,
 price_no_tax::numeric::int, tax_rate, invoice, fee, external_order_id, info FROM orders WHERE
 user_id = $1 AND payment_type = 'iap' AND
 order_date > Date(NOW()) - interval '4 day' AND status IS NULL ORDER BY created_at DESC LIMIT 1;`,

		"createorder": `INSERT INTO orders
 (id, user_id, product_id, payment_type, start_date, end_date, order_date, price, price_no_tax, tax_rate, fee, external_order_id ) VALUES
 (:id, :user_id, :product_id, :payment_type, :start_date, :end_date, :order_date, :price, :price_no_tax, :tax_rate, :fee, :external_order_id);`,

		"updateorder": `UPDATE orders
			SET
				status = :status,
				info = :info,
				realized_at = :realized_at,
				external_order_id = :external_order_id,
				start_date = :start_date,
				end_date = :end_date,
				price = :price,
				price_no_tax = :price_no_tax,
				fee = :fee,
				product_id = :product_id
			WHERE id = :id;`,

		"user": `SELECT u.id, u.email, date_part('epoch', u.expired_at)::int as expired_at,
 u.created_at, u.role, u.auto_renew, u.type, u.created_by,
 payment.iap_receipt_data, payment.iap_receipt_data_hash, date_part('epoch', payment.iap_latest_expires_date)::int as iap_latest_expires_date,
 payment.iap_latest_transaction_id FROM users u LEFT join payment_info payment ON
 payment.user_id = u.id WHERE id = $1`,

		"paymentuser": `SELECT u.id, u.email, u.phone, date_part('epoch', u.expired_at)::int as expired_at,
 u.created_at, u.role, u.auto_renew, u.type, u.created_by,
 payment.iap_receipt_data, payment.iap_receipt_data_hash, date_part('epoch', payment.iap_latest_expires_date)::int as iap_latest_expires_date,
 payment.iap_latest_transaction_id FROM users u LEFT join payment_info payment ON
 payment.user_id = u.id WHERE payment.iap_receipt_data_hash = $1`,

		"count_intro_offer_period_orders": `SELECT
			count(*)
		FROM orders
		WHERE payment_type = 'iap'
		AND product_id = $1
		AND info->>'original_transaction_id' = $2
		AND status = 'ok'`,
	}

	iapcfg = iapConfig{
		// debug false (prod)
		false: appleIAPConfigObj{
			PaymentErrorTopic: "arn:aws:sns:ap-northeast-1:312530604583:kktv-prod-payment-error-notification",
			PaymentErrorSlack: "#kktv-log-prod-payment-err",
		},
		// debug true (test, stage)
		true: appleIAPConfigObj{
			PaymentErrorTopic: "arn:aws:sns:ap-northeast-1:312530604583:kktv-test-payment-error-notification",
			PaymentErrorSlack: "#kktv-log-test-payment-err",
		},
	}
)

type appleIAPConfigObj struct {
	PaymentErrorTopic string
	PaymentErrorSlack string
}

type iapConfig map[bool]appleIAPConfigObj

// IAP for apple appstore verify receipt
type IAP struct {
	// Only used for receipts that contain auto-renewable subscriptions.
	ReceiptData string `json:"receipt-data"`
	Password    string `json:"password,omitempty"`
	// Only used for iOS7 style app receipts that contain auto-renewable or non-renewing subscriptions.
	// If value is true, response includes only the latest renewal transaction for any subscriptions.
	ExcludeOldTransactions bool `json:"exclude-old-transactions,omitempty"`
	Response               IAPResponse
	ResponseString         string
}

type IAPReceipts []IAPReceipt

func (a IAPReceipts) Len() int {
	return len(a)
}
func (a IAPReceipts) Swap(i, j int) {
	a[i], a[j] = a[j], a[i]
}
func (a IAPReceipts) Less(i, j int) bool {
	return a[i].PurchaseDateMs < a[j].PurchaseDateMs
}

// IAPReceipt from apple store
type IAPReceipt struct {
	// AppItemID               string `json:"app_item_id"`
	// Bid                     string `json:"bid"`
	// Bvrs                    string `json:"bvrs"`
	ExpiresDate             string `json:"expires_date"`
	ExpiresDateMs           string `json:"expires_date_ms"`
	ExpiresDateFormatted    string `json:"expires_date_formatted"`
	ExpiresDateFormattedPst string `json:"expires_date_formatted_pst"`
	IsInIntroOfferPeriod    string `json:"is_in_intro_offer_period"`
	IsTrialPeriod           string `json:"is_trial_period"`
	// ItemID                  string `json:"item_id"`
	OriginalPurchaseDate    string      `json:"original_purchase_date"`
	OriginalPurchaseDateMs  string      `json:"original_purchase_date_ms"`
	OriginalPurchaseDatePst string      `json:"original_purchase_date_pst"`
	OriginalTransactionID   string      `json:"original_transaction_id"`
	ProductID               string      `json:"product_id"`
	PurchaseDate            string      `json:"purchase_date"`
	PurchaseDateMs          string      `json:"purchase_date_ms"`
	PurchaseDatePst         string      `json:"purchase_date_pst"`
	Quantity                string      `json:"quantity"`
	TransactionID           string      `json:"transaction_id"`
	UniqueIdentifier        string      `json:"unique_identifier"`
	UniqueVendorIdentifier  string      `json:"unique_vendor_identifier"`
	WebOrderLineItemID      string      `json:"web_order_line_item_id"`
	InApp                   IAPReceipts `json:"in_app"`
}

// IAPResponse from apple store
type IAPResponse struct {
	AutoRenewProductID       string      `json:"auto_renew_product_id"`
	AutoRenewStatus          int64       `json:"auto_renew_status"`
	ExpirationIntent         string      `json:"expiration_intent"`
	IsInBillingRetryPeriod   string      `json:"is_in_billing_retry_period"`
	GracePeriodExpiresDate   string      `json:"grace_period_expires_date"`
	GracePeriodExpiresDateMs string      `json:"grace_period_expires_date_ms"`
	LatestReceipt            string      `json:"latest_receipt"`
	LatestExpiredReceiptInfo IAPReceipt  `json:"latest_expired_receipt_info"`
	LatestReceiptInfos       IAPReceipts `json:"latest_receipt_info"`
	LatestReceiptInfo        IAPReceipt
	Receipt                  IAPReceipt `json:"receipt"`
	Status                   int64      `json:"status"`
	PendingRenewalInfo       []struct {
		AutoRenewProductID     string `json:"auto_renew_product_id"`
		AutoRenewStatus        string `json:"auto_renew_status"`
		ExpirationIntent       string `json:"expiration_intent"`
		IsInBillingRetryPeriod string `json:"is_in_billing_retry_period"`
		OriginalTransactionID  string `json:"original_transaction_id"`
		ProductID              string `json:"product_id"`
	} `json:"pending_renewal_info"`
}

// Verify the receipt
func (i *IAP) Verify() (err error) {
	var topic string
	defer func() {
		if err != nil {
			log.Warn("IAPVerify: "+topic).Err(err).Interface("iap", i).Send()
		}
	}()

	jsBytes, _ := json.Marshal(i)

	sendRequest := func(url string, payLoad []byte) (*http.Response, error) {
		r, _ := http.NewRequest("POST", url, bytes.NewBuffer(payLoad))
		r.Header.Set("Content-Type", "application/json; charset=utf-8")
		return curl.Do(r)
	}

	resp, err := sendRequest(productionURL, jsBytes)
	if err != nil {
		topic = "sent request"
		return
	}

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		topic = "read body"
		return
	}

	// Transform old receipt to array
	i.ResponseString = string(body)
	if len(reReceipt.FindSubmatch(body)) > 1 {
		body = reReceipt.ReplaceAll(body, []byte(`"latest_receipt_info":[{$1}]`))
	}

	err = json.Unmarshal(body, &i.Response)
	log.Info("IAPVerify: response").Str("response", i.ResponseString).Send()
	if err != nil {
		topic = "response unmarshal"
		return
	}

	// When sandbox receipt send to production, got 21007 status code
	if i.Response.Status == 21007 {
		resp, err := sendRequest(sandboxURL, jsBytes)
		if err != nil {
			topic = "sent sandbox request"
			return err
		}

		body, err := ioutil.ReadAll(resp.Body)
		if err != nil {
			topic = "read sandbox response request"
			return err
		}

		// Transform old receipt to array
		i.ResponseString = string(body)
		if len(reReceipt.FindSubmatch(body)) > 1 {
			body = reReceipt.ReplaceAll(body, []byte(`"latest_receipt_info":[{$1}]`))
		}

		err = json.Unmarshal(body, &i.Response)
		log.Info("IAPVerify: sandbox response").Str("response", string(body)).Send()
		if err != nil {
			topic = "sandbox response unmarshal"
			return err
		}
	}

	var allReceipts IAPReceipts

	if len(i.Response.Receipt.InApp) > 0 {
		allReceipts = append(allReceipts, i.Response.Receipt.InApp...)
	}

	if len(i.Response.LatestReceiptInfos) > 0 {
		allReceipts = append(allReceipts, i.Response.LatestReceiptInfos...)
	}

	// latest_receipt_info 內的 receipt 按產生時間 asc 排序，取最後一筆
	if len(allReceipts) > 0 {
		sort.Sort(allReceipts)
		lastestIndex := len(allReceipts) - 1
		log.Info("IAPVerify: process Latest Receipt").Str("latest receipt txID", allReceipts[lastestIndex].TransactionID).Send()
		i.Response.LatestReceiptInfo = allReceipts[lastestIndex]
	}

	return err
}

// NewIAP get an IAP
func NewIAP(receiptData, password string) (iap *IAP, err error) {
	iap = new(IAP)
	iap.ReceiptData = receiptData
	iap.Password = password
	iap.Response.Status = -1
	err = iap.Verify()

	if err != nil {
		return nil, err
	}

	// subscribe user => latest_receipt_info
	// one time purchase => receipt
	// if latest_receipt_info is empty && receipt had validated transaction_id
	// put receipt to latest_receipt_info

	if iap.Response.LatestReceiptInfo.OriginalTransactionID == "" && iap.Response.Receipt.OriginalTransactionID != "" {
		iap.Response.LatestReceiptInfo = iap.Response.Receipt
		iap.Response.LatestReceipt = iap.ReceiptData
	}

	return iap, nil
}

// IAPPost api client post request
type IAPPost struct {
	UserID      string `json:"user_id,omitempty"`
	ReceiptData string `json:"receipt_data,omitempty"`
}

// IAPUser map json response for user information
type IAPUser struct {
	ID                     string      `db:"id" json:"id"`
	Email                  null.String `db:"email"`
	Phone                  null.String `db:"phone"`
	Role                   string      `db:"role" json:"role"`
	Type                   string      `db:"type" json:"type"`
	CreatedAt              null.Time   `db:"created_at" json:"createdAt"`
	CreatedBy              null.String `db:"created_by" json:"createdBy"`
	ExpiredAt              int64       `db:"expired_at" json:"expiredAt"`
	AutoRenew              bool        `db:"auto_renew" json:"autoRenew"`
	IapReceiptData         null.String `db:"iap_receipt_data"`
	IapReceiptDataHash     null.String `db:"iap_receipt_data_hash"`
	IapLatestExpiresDate   null.Int    `db:"iap_latest_expires_date"`
	IapLatestTransactionID null.String `db:"iap_latest_transaction_id"`
	IAPResponse            IAPResponse
	IAPResponseString      string
	ExpirationIntent       string
	TransactionOrder       *dbuser.Order
	Product                *Product
	VeryFirstOrder         bool
}

func (user *IAPUser) isOrderIntroPrice() bool {
	return user.Product.DiscountPrice > 0 &&
		user.IAPResponse.LatestReceiptInfo.IsInIntroOfferPeriod == "true"
}

// ValidProduct test product
func (user *IAPUser) ValidProduct() (product *Product, ok bool) {
	var err error
	var productID string
	db := kkapp.App.DbUser.Slave()
	product = new(Product)
	switch {
	case user.IAPResponse.AutoRenewProductID != "":
		productID = user.IAPResponse.AutoRenewProductID

	case user.IAPResponse.Receipt.ProductID != "":
		productID = user.IAPResponse.Receipt.ProductID

	case user.IAPResponse.LatestReceiptInfo.ProductID != "":
		productID = user.IAPResponse.LatestReceiptInfo.ProductID

	}

	if productID == "" {
		log.Error("IAPUser: ValidProduct: not found from IAPResponse").Send()
		return nil, false
	}
	err = db.Get(product, sqlshare["product"], productID, iapPaymentType)
	if err != nil {
		log.Error("IAPUser: ValidProduct: getProduct").Str("productID", productID).Str("paymentType", iapPaymentType).Err(err).Send()
		return nil, false
	}
	user.Product = product
	return product, true
}

// VerifyPaymentInfo verify payment_info, you should had validated Receipt for subscribe purchase
func (user *IAPUser) VerifyPaymentInfo() (err error) {

	paymentuser := new(IAPUser)
	db := kkapp.App.DbUser.Slave()
	err = db.Get(paymentuser, sqliap["paymentuser"], user.IAPResponse.LatestReceiptInfo.OriginalTransactionID)

	if errors.Is(err, sql.ErrNoRows) {
		// this order is first time purchase if order does not exist
		return nil
	} else if err != nil {
		return err
	}

	if paymentuser.ID != user.ID {
		// the OrderID is not bind with this user
		var username string
		if paymentuser.Email.Valid {
			username = paymentuser.Email.String
		} else if paymentuser.Phone.Valid {
			username = paymentuser.Phone.String
		} else {
			username = paymentuser.ID
		}
		err = errors.New("Receipt data has been used. original_account: " + username)
	}

	// transaction ID had been used
	receipt := user.IAPResponse.LatestReceiptInfo
	if receipt.TransactionID == user.IapLatestTransactionID.String {
		// this user had claim this receipt
		// default
		err = errors.New("Order had been created")

		if len(user.IAPResponse.PendingRenewalInfo) > 0 {
			pendingRenew := user.IAPResponse.PendingRenewalInfo[0]
			switch pendingRenew.ExpirationIntent {
			case "1":
				err = errors.New("The customer voluntarily canceled their subscription")
			case "2":
				err = errors.New("Billing error; for example, the customer's payment information was no longer valid")
			case "3":
				err = errors.New("The customer did not agree to a recent price increase")
			case "4":
				err = errors.New("The product was not available for purchase at the time of renewal")
			case "5":
				err = errors.New("Unknown error")
			}

			// hints for ExpirationIntent after VerifyPaymentInfo
			user.ExpirationIntent = pendingRenew.ExpirationIntent
		}

		// check if out off grace period
		if receipt.ExpiresDateMs != "" {
			if expiresDate, err := strconv.ParseInt(receipt.ExpiresDateMs, 10, 64); err == nil {
				_now := time.Now()
				_graceTime := time.Unix(expiresDate/1000, 0).AddDate(0, 0, iapGracePeriodDays)
				if _now.After(_graceTime) {
					err = errors.New("Expired too long")
					return err
				}
			}
		}
	}

	return err
}

// HasSubscribed test user subscribed already
// must had valid user.Product
func (user *IAPUser) HasSubscribed() bool {
	if user.Product != nil && user.Product.AutoRenew && user.AutoRenew {
		return true
	}
	return false
}

// AssignOrderReceiptDate for the subscription user
func (user *IAPUser) AssignOrderReceiptDate(order *dbuser.Order) (err error) {
	// get latest order
	var expiresDate int64

	errParam := errors.New("Invalid parameters")

	if order == nil {
		return
	}

	receipt := user.IAPResponse.LatestReceiptInfo
	nowTimeMills := time.Now().Unix() * 1000
	purchaseDate, err := strconv.ParseInt(receipt.PurchaseDateMs, 10, 64)
	if err != nil {
		log.Error("AssignOrderReceiptDate: ParseInt").Str("PurchaseDateMs", receipt.PurchaseDateMs).Err(err).Send()
		return errParam
	}

	if receipt.ExpiresDateMs != "" {
		expiresDate, err = strconv.ParseInt(receipt.ExpiresDateMs, 10, 64)
		if err != nil {
			log.Error("AssignOrderReceiptDate: ParseInt").Str("ExpiresDateMs", receipt.ExpiresDateMs).Err(err).Send()
			return err
		}
	} else if receipt.ExpiresDate != "" {
		expiresDate, err = strconv.ParseInt(receipt.ExpiresDate, 10, 64)
		if err != nil {
			log.Error("AssignOrderReceiptDate: ParseInt").Str("ExpiresDate", receipt.ExpiresDate).Err(err).Send()
			return errParam
		}
		user.IAPResponse.LatestReceiptInfo.ExpiresDateMs = fmt.Sprintf("%d", expiresDate)
	}

	if expiresDate == 0 && !user.Product.AutoRenew {
		// no expired date in receipt
		var _expiredTime time.Time
		_purchaseTime := time.Unix(purchaseDate/1000, 0)
		{
			unit, durationInt := kkutil.ParseDuration(user.Product.Duration)
			if durationInt != 0 {
				_expiredTime = kkutil.AddDuration(_purchaseTime, unit, durationInt)
			}

		}
		{
			unit, durationInt := kkutil.ParseDuration(user.Product.FreeDuration)
			if durationInt != 0 {
				_expiredTime = kkutil.AddDuration(_expiredTime, unit, durationInt)
			}
		}

		if _expiredTime.IsZero() {
			return errParam
		}
		expiresDate = _expiredTime.Unix() * 1000
	}

	log.Info("AssignOrderReceiptDate").Int64("nowTimeMills", nowTimeMills).Int64("purchaseDate", purchaseDate).Int64("expiresDate", expiresDate).Send()
	if expiresDate < nowTimeMills {
		// create order the expire time greater than now
		log.Info("AssignOrderReceiptDate: expired time greater than now").Send()
		err = errors.New("Order had been created")
		return err
	}

	var orderDate time.Time
	orderDate = time.Unix(purchaseDate/1000, 0)
	// expiresDate|purchaseDate => Millisecond
	// one day => millisecond 24 x 60 x 60 x 1000 = 86400000
	orderDays := (expiresDate - purchaseDate) / 86400000
	_intDays := int(orderDays)

	order.OrderDate = orderDate
	order.StartDate = orderDate
	order.EndDate = orderDate.AddDate(0, 0, _intDays)
	order.ExternalOrderID = null.String{NullString: sql.NullString{String: receipt.TransactionID, Valid: true}}
	return err
}

// NewOrder for the subscription user
func (user *IAPUser) NewOrder() (order *dbuser.Order, err error) {
	log.Info("IAPUser NewOrder").
		Str("user_id", user.ID).
		Send()
	// get latest order
	order = new(dbuser.Order)
	if user.Product == nil {
		_, validProduct := user.ValidProduct()
		if !validProduct {
			err = errors.New("Invalid product name")
			return nil, err
		}
	}

	log.Info("IAPUser: NewOrder").Str("userID", user.ID).Interface("product", user.Product).Send()
	order.TaxRate = user.Product.TaxRate
	order.ProductID = user.Product.ID
	order.PaymentType = user.Product.PaymentType
	order.UserID = user.ID

	// iap 回傳的最後收據資訊
	receipt := user.IAPResponse.LatestReceiptInfo

	// 設定訂單價格預設值
	order.Price = user.Product.Price
	order.PriceNoTax = user.Product.PriceNoTax
	order.Fee = user.Product.Fee

	// 如果產品有設定優惠價
	if user.isOrderIntroPrice() {
		// TODO：應該將邏輯攤平出去整個邏輯重構，解決目前不合理的 function reuse
		// 因為這個 function 同時被好幾個地方 reuse，包含預建訂單也會呼叫到這裡，
		// 所以判斷 is_in_intro_offer_period 之後，需再判斷目前要建的這張訂單是否還在優惠週期內，
		// 例如：產品付費週期設定 1 個月，優惠週期也設定 1 個月，優惠價設定為 80 元，
		// 則第一個月訂單應為優惠價 80，但在預建下個週期的訂單時，應該判斷 original_transaction_id 的訂單數量是否已經大於等於優惠期數量，
		// 如果大於等於優惠期數量時，訂單應設定為一般價

		// 轉換產品付費週期、優惠週期單位
		discountDurationUnit, discountDurationInt := kkutil.ParseDuration(user.Product.DiscountDuration)
		durationUnit, _ := kkutil.ParseDuration(user.Product.Duration)

		// 只有當付費週期單位跟優惠週期單位相同時才套用優惠價
		if durationUnit == discountDurationUnit {
			var count int64
			db := kkapp.App.DbUser.Slave()
			// 因 user 及其 payment_info 可以被 revoke，所以相同 original_transaction_id 有可能出現在多位 user 的訂單紀錄中
			// 例如：用戶過去訂閱過 iap，取消訂閱後，透過刪除帳號功能將原本的帳號刪除，隔了一段時間後在 app store 上重新訂閱 iap 方案，
			// 此時 Apple 提供的 original transaction id 有機會與先前相同，因此用戶新建立的帳號產生訂單時，
			// 新訂單所紀錄的 original_transaction_id 與先前已刪除帳號之 iap 訂單的 original_transaction_id 有可能會相同
			err = db.Get(&count, sqliap["count_intro_offer_period_orders"], user.Product.ID, receipt.OriginalTransactionID)
			if err != nil {
				return nil, err
			}

			// 如果 相同產品 且 original_transaction_id 且 status ok 查出來的筆數 < 優惠週期，則此訂單價格應為優惠價
			if count < int64(discountDurationInt) {
				order.Price = user.Product.DiscountPrice
				order.PriceNoTax = user.Product.DiscountPriceNoTax
				order.Fee = user.Product.DiscountFee
			}

		}
	}

	err = user.AssignOrderReceiptDate(order)

	if err != nil {
		return nil, err
	}
	order.ID = dbuser.GenerateOrderID(user.Product.PaymentTypeCode, order.OrderDate)

	return order, nil
}

// GetUnrealizedOrCreateFirstOrder get renew order or create first order if renew order not found
func (user *IAPUser) GetUnrealizedOrCreateFirstOrder() (order *dbuser.Order, err error) {
	log.Info("IAPUser: GetOrder").
		Str("user_id", user.ID).
		Send()
	// get latest order
	db := kkapp.App.DbUser.Slave()
	order = new(dbuser.Order)
	err = db.Get(order, sqliap["order"], user.ID)
	if err != nil {
		if !errors.Is(err, sql.ErrNoRows) {
			log.Error("IAPUser: GetUnrealizedOrCreateFirstOrder: getOrder").Str("userID", user.ID).Err(err).Send()
			return nil, err
		}

		// user has no existent order
		order, err = user.NewOrder()
		log.Info("IAPUser: CreateNewOrder").Str("userID", user.ID).Interface("order", order).Send()
		if err != nil {
			log.Error("IAPUser: CreateNewOrder").Str("userID", user.ID).Interface("order", order).Err(err).Send()
			return nil, err
		}

		err = user.InsertOrder(order)
		if err != nil {
			return nil, err
		}
	}

	// found an error in which the product ID of the existent renewal order is different from the latest receipt
	if user.Product == nil {
		_, validProduct := user.ValidProduct()
		if !validProduct {
			err = errors.New("Invalid product name")
			return nil, err
		}
	}

	if order.ProductID != user.Product.ID {
		order.Price = user.Product.Price
		order.PriceNoTax = user.Product.PriceNoTax
		order.Fee = user.Product.Fee
		order.TaxRate = user.Product.TaxRate
		order.ProductID = user.Product.ID
	}

	receipt := user.IAPResponse.LatestReceiptInfo
	if receipt.OriginalTransactionID == receipt.TransactionID {
		// first order
		user.VeryFirstOrder = true
	}

	return order, nil
}

// InsertOrder insert the user order
func (user *IAPUser) InsertOrder(order *dbuser.Order) (err error) {
	log.Info("IAPUser InsertOrder").
		Interface("order", order).
		Send()
	// insert the new order
	db := kkapp.App.DbUser.Master()
	_, err = db.NamedExec(sqliap["createorder"], order)
	return
}

// CancelOrder cancel the user order
func (user *IAPUser) CancelOrder() {
	// get latest order
	db := kkapp.App.DbUser.Master()
	log.Info("IAPUser: CancelOrder: Start").Str("userID", user.ID).Send()
	res, err := db.Exec(sqlshare["cancel"], user.ID, iapPaymentType)
	if err != nil {
		log.Warn("IAPUser: CancelOrder: updateOrder").Interface("user", user).Err(err).Send()
	}

	rowCnt, err := res.RowsAffected()
	if err != nil {
		log.Warn("IAPUser: CancelOrder: updateOrder: RowsAffected").Interface("user", user).Err(err).Send()
	}

	if rowCnt > 0 {
		log.Info("IAPUser: CancelOrder: updateOrder").Int64("Order affected", rowCnt).Send()
		// identify event reason
		switch user.ExpirationIntent {
		case "1", "3":
			user.CancelledEvent()
		case "2", "4", "5":
			user.FailedEvent()
		default:
			user.FailedEvent()
		}

	}
	_, err = db.Exec(`UPDATE users SET auto_renew = false, updated_at = NOW() WHERE id = $1;`, user.ID)
	if err != nil {
		log.Warn("IAPUser: CancelOrder: updateUser").Interface("user", user).Err(err).Send()
	}
}

// Extend the user order
func (user *IAPUser) Extend() (err error) {
	order, err := user.GetUnrealizedOrCreateFirstOrder()
	log.Info("IAPUser: Extend: GetOrder").Interface("user", user).Interface("order", order).Send()
	if err != nil {
		log.Error("IAPUser: Extend: GetOrder").Interface("user", user).Err(err).Send()
		return err
	}

	db := kkapp.App.DbUser.Master()
	now := time.Now()

	// extend this order
	if order.OrderDate.Unix() < now.AddDate(0, 0, 2).Unix() {
		log.Info("IAPUser: Extend Order").Str("userID", user.ID).Interface("order", order).Send()
		_, err = db.Exec(sqlshare["extend"], order.ID)
		if err != nil {
			log.Error("IAPUser: Extend Order").Str("userID", user.ID).Str("orderID", order.ID).Err(err).Send()
			return err
		}
	}

	// extend the user's expired_at if it is earlier than time.Now().AddDate(0,0,2).Unix()
	if user.ExpiredAt < now.AddDate(0, 0, 2).Unix() {
		log.Info("IAPUser: Extend User").Str("userID", user.ID).Interface("order", order).Send()
		expiredDate := now.AddDate(0, 0, 2).UTC()
		err = updateUser(db, user.ID, usermodel.RolePremium.String(), usermodel.MembershipPremiumOnly, expiredDate, true, usermodel.TypeGeneral.String())
		if err != nil {
			return err
		}
	}
	return nil
}

func (user *IAPUser) Renew(freeTrial, isOrderIntroPrice bool) (err error) {
	log.Info("IAPUser Renew").
		Str("user_id", user.ID).
		Bool("free_trial", freeTrial).
		Bool("is_order_intro_price", isOrderIntroPrice).
		Send()

	order, err := user.GetUnrealizedOrCreateFirstOrder()
	if err != nil {
		return err
	}

	if isOrderIntroPrice {
		order.Price = user.Product.DiscountPrice
		order.PriceNoTax = user.Product.DiscountPriceNoTax
		order.Fee = user.Product.DiscountFee
	}

	if freeTrial {
		order.Price = 0
	}

	// Start new order or renew order
	return user.Realize(order)
}

// CancelledEvent the user
func (user *IAPUser) CancelledEvent() {
	log.Info("IAPUser: CancelledEvent").Str("userID", user.ID).Interface("product", user.Product).Send()
	event, _ := amplitude.NewAccountTransactionCancelled(user.ID, iapPaymentType, "customer canceled")
	log.Info("IAPUser: CancelledEvent: amplitude event").Str("userID", user.ID).Send()
	event.Send()
}

// FailedEvent the user
func (user *IAPUser) FailedEvent() {
	log.Info("IAPUser: FailedEvent").Str("userID", user.ID).Interface("product", user.Product).Send()
	event, _ := amplitude.NewAccountTransactionCancelled(user.ID, iapPaymentType, "auto renewal failed")
	log.Info("IAPUser: FailedEvent: amplitude event").Str("userID", user.ID).Send()
	event.Send()

	// edm cancel event
	if ematicSrv := wrapper.NewEmaticService(); ematicSrv.IsClientExist() {
		go ematicSrv.Cancel(user.Email.String, time.Unix(user.ExpiredAt, 0))
	}
}

// SendTransactionEvent the user
func (user *IAPUser) SendTransactionEvent(triggerCondition string) {
	var ep amplitude.EventProperties
	durationUnit, durationInt := kkutil.ParseDuration(user.Product.Duration)
	if user.TransactionOrder.Price > 0 {
		// paid
		switch durationUnit {
		case "day":
			ep = amplitude.EventProperties{
				OrderNumber:     user.TransactionOrder.ID,
				Plan:            user.Product.Name,
				PaymentType:     user.Product.PaymentType,
				ObtainedVipDays: durationInt,
			}
		case "mon":
			ep = amplitude.EventProperties{
				OrderNumber:       user.TransactionOrder.ID,
				Plan:              user.Product.Name,
				PaymentType:       user.Product.PaymentType,
				ObtainedVipMonths: durationInt,
			}
		case "year":
			ep = amplitude.EventProperties{
				OrderNumber:       user.TransactionOrder.ID,
				Plan:              user.Product.Name,
				PaymentType:       user.Product.PaymentType,
				ObtainedVipMonths: 12,
			}
		}
	} else {
		// free trial
		switch durationUnit {
		case "day":
			ep = amplitude.EventProperties{
				OrderNumber:      user.TransactionOrder.ID,
				Plan:             user.Product.Name,
				PaymentType:      user.Product.PaymentType,
				GivenFreeVipDays: durationInt,
			}
		case "mon":
			ep = amplitude.EventProperties{
				OrderNumber:        user.TransactionOrder.ID,
				Plan:               user.Product.Name,
				PaymentType:        user.Product.PaymentType,
				GivenFreeVipMonths: durationInt,
			}
		case "year":
			ep = amplitude.EventProperties{
				OrderNumber:        user.TransactionOrder.ID,
				Plan:               user.Product.Name,
				PaymentType:        user.Product.PaymentType,
				GivenFreeVipMonths: 12,
			}
		}
	}
	event, _ := amplitude.NewAccountTransactionCompleted(user.ID, triggerCondition, int(user.TransactionOrder.Price), ep)
	log.Info("IAPUser: SendTransactionEvent").Str("userID", user.ID).Interface("order", user.TransactionOrder).Send()
	event.Send()

	// edm paid event
	// if user.Email.Valid {
	// 	kkapp.App.Ematic.Paid(user.Email.String, time.Unix(user.ExpiredAt, 0).Format(ematicagent.EmaticDateFormat))
	// }
}

// Verify the user iap order
func (user *IAPUser) Verify() (err error) {

	if !user.IapReceiptData.Valid {
		err = errors.New("Invalid receipt data")
		return
	}

	iap, err := NewIAP(user.IapReceiptData.String, secret.Values.IAPPassword)
	if err != nil {
		// could be network error, receipt data, json struct
		return errors.New("Network error")
	}

	user.IAPResponse = iap.Response
	user.IAPResponseString = iap.ResponseString

	// grace period
	if user.IAPResponse.IsInBillingRetryPeriod == "1" {
		err = errors.New("In grace period")
		return
	}

	// product
	if _, ok := user.ValidProduct(); !ok {
		err = errors.New("Invalid product name")
		return
	}

	// paymentinfo
	err = user.VerifyPaymentInfo()
	return err
}

// Realize the user order
func (user *IAPUser) Realize(order *dbuser.Order) (err error) {

	if order == nil {
		return errors.New("Invalid parameters")
	}

	err = user.AssignOrderReceiptDate(order)
	if err != nil {
		return err
	}
	log.Info("IAPUser: Realize: Start").Str("userID", user.ID).Interface("order", order).Send()

	user.TransactionOrder = order
	duration := order.EndDate.Sub(order.StartDate)
	var expiredDate time.Time
	var autoRenew bool = user.Product.AutoRenew

	loc, _ := time.LoadLocation("Asia/Taipei")
	now := time.Now().In(loc)
	saferNow := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, loc).AddDate(0, 0, 1)

	if user.ExpiredAt < saferNow.Unix() {
		expiredDate = saferNow.Add(duration).UTC()
	} else {
		expiredDate = time.Unix(user.ExpiredAt, 0).Add(duration).UTC()
	}
	user.ExpiredAt = expiredDate.Unix() // update the latest expired

	receipt := user.IAPResponse.LatestReceiptInfo
	expiresDate, _ := strconv.ParseInt(receipt.ExpiresDateMs, 10, 64)

	order.ExternalOrderID = null.String{NullString: sql.NullString{String: receipt.TransactionID, Valid: true}}
	order.Status = null.String{NullString: sql.NullString{String: "ok", Valid: true}}
	order.RealizedAt = null.Time{Time: time.Now().UTC().Truncate(time.Second), Valid: true}
	receiptBytes, _ := json.Marshal(user.IAPResponse.LatestReceiptInfo)
	order.Info = null.String{NullString: sql.NullString{String: string(receiptBytes), Valid: true}}

	paymentInfo := new(dbuser.PaymentInfo)
	paymentInfo.UserID = user.ID
	paymentInfo.Email = user.Email
	paymentInfo.Phone = user.Phone
	paymentInfo.IapReceiptData = user.IapReceiptData // original receipt data
	if user.IAPResponse.LatestReceipt != "" {
		paymentInfo.IapReceiptData = null.NewString(user.IAPResponse.LatestReceipt, true)
	}
	paymentInfo.IapReceiptDataHash = null.NewString(receipt.OriginalTransactionID, true) // original_transaction_id
	paymentInfo.IapLatestTransactionID = null.NewString(receipt.TransactionID, true)
	paymentInfo.IapLatestExpiresDate = null.NewTime(time.Unix(expiresDate/1000, 0), true)
	paymentInfo.PaymentType = null.NewString(iapPaymentType, true)

	user.IapLatestExpiresDate = null.NewInt(expiresDate/1000, true)

	// transaction start
	// 1. update order
	// 2. update user info
	// 3. update payment_info
	// 4. commit or rollback
	db := kkapp.App.DbUser.Master()
	var tx *sqlx.Tx
	tx, err = db.Beginx()

	defer func() {
		if err != nil {
			log.Error("IAPRealize: End").Interface("order", order).Err(err).Send()
			tx.Rollback()
		}
		commitErr := tx.Commit()
		log.Info("IAPRealize: commit").Err(commitErr).Send()
	}()

	// 1. update order
	log.Info("IAPRealize: updateOrder").Interface("order", order).Send()
	_, err = tx.NamedExec(sqliap["updateorder"], order)
	if err != nil {
		return
	}

	// 2. update user info
	log.Info("IAPRealize: updateUser").Str("userID", user.ID).Time("expiredDate", expiredDate).Bool("autoRenew", autoRenew).Send()
	err = updateUser(db, user.ID, usermodel.RolePremium.String(), usermodel.MembershipPremiumOnly, expiredDate, autoRenew, usermodel.TypeGeneral.String())
	if err != nil {
		return
	}

	// 3. update payment_info
	log.Info("IAPRealize: upsertPaymentInfo").Str("userID", user.ID).Interface("paymentInfo", paymentInfo).Send()
	_, err = tx.NamedExec(sqlshare["upsertpayment"], paymentInfo)
	if err != nil {
		return
	}
	return
}

// Deal refine the user order
func (user *IAPUser) Deal() (err error) {
	err = user.Verify()
	if err != nil {
		log.Warn("IAPDeal: verify user").Interface("user", user).Err(err).Send()
		user.SendPaymentError("", err)

		switch {
		case strings.Contains(err.Error(), "Network error"):
			err = user.Extend()
			return err
		case strings.Contains(err.Error(), "Order had been created") && user.IAPResponse.Status == 0:
			// user is still in subscription
			err = user.Extend()
			return err
		case strings.Contains(err.Error(), "In grace period"):
			// user is in grace period
			err = user.Extend()
			return err
		case strings.Contains(err.Error(), "Order had been created") && user.IAPResponse.Status == 21006:
			// user's subscription expired
		case err.Error() == "The customer voluntarily canceled their subscription":
			fallthrough
		case err.Error() == "Billing error; for example, the customer's payment information was no longer valid":
			fallthrough
		case err.Error() == "The customer did not agree to a recent price increase":
			fallthrough
		case err.Error() == "The product was not available for purchase at the time of renewal":
			fallthrough
		case err.Error() == "Unknown error":
			user.CancelOrder()
			return
		case err.Error() == "Expired too long":
			user.CancelOrder()
			return
		default:
			return
		}
	}

	status := user.IAPResponse.Status

	switch status {
	case 0:
		// success
	case 21000:
		err = errors.New("The App Store could not read the JSON object you provided")
	case 21002:
		err = errors.New("The data in the receipt-data property was malformed or missing")
	case 21003:
		err = errors.New("The receipt could not be authenticated")
	case 21004:
		err = errors.New("The shared secret you provided does not match the shared secret on file for your account")
	case 21005:
		err = errors.New("The receipt server is not currently available")
	case 21006:
		err = errors.New("This receipt is valid but the subscription has expired. When this status code is returned to your server, the receipt data is also decoded and returned as part of the response")
	case 21007:
		err = errors.New("This receipt is from the test environment, but it was sent to the production environment for verification. Send it to the test environment instead")
	case 21008:
		err = errors.New("This receipt is from the production environment, but it was sent to the test environment for verification. Send it to the production environment instead")
	case 21010:
		err = errors.New("This receipt could not be authorized. Treat this the same as if a purchase was never made")
	default:
		if status >= 21100 && status <= 21199 {
			err = errors.New("Internal data access error")
		} else {
			err = errors.New("An unknown error occurred")
		}
	}

	if err != nil {
		user.SendPaymentError("", err)

		if strings.Contains(err.Error(), "receipt server") {
			// only extend user when network error
			err = user.Extend()
			return err
		}

		// other error
		log.Warn("IAPDeal: cancel order: user has error").Interface("user", user).Err(err).Send()
		user.CancelOrder()
		return err
	}

	receipt := user.IAPResponse.LatestReceiptInfo

	freeTrial := receipt.IsTrialPeriod == "true"

	// Renew 包含建立第一張訂單或是 Renew 訂單
	err = user.Renew(freeTrial, user.isOrderIntroPrice())
	// error type
	// "Receipt data has been used. original_account: xxx"
	// "Order had been created"
	if err != nil {
		log.Warn("IAPUser: Deal").Err(err).Str("userID", user.ID).Send()
		user.SendPaymentError("[RENEW]", err)

		// switch case not send unsubcribe event
		switch {
		case strings.Contains(err.Error(), "Receipt data has been used"):
			// purchase fail
			return err
		case strings.Contains(err.Error(), "Order had been created"):
			// purchase, but the new order not claim from Apple store
			// we should wait for new order avaliable
			err = user.Extend()
			return err
		case strings.Contains(err.Error(), "orders_payment_type_external_order_id_index"):
			// insert order external_order_id conflict sould not send cancel event
			return err
		}

		return err
	}

	// clear the next order ExternalOrderID
	if user.Product.AutoRenew {
		// 建立新訂單
		nextOrder, err := user.NewOrder()
		nextOrder.ExternalOrderID = null.String{NullString: sql.NullString{String: "", Valid: false}}
		if err == nil {
			if user.TransactionOrder != nil {
				// orderDate := time.Unix(user.IapLatestExpiresDate.Int64, 0)
				nextOrder.OrderDate = user.TransactionOrder.EndDate
				nextOrder.StartDate = user.TransactionOrder.EndDate
				nextOrder.EndDate = nextOrder.StartDate.Add(user.TransactionOrder.EndDate.Sub(user.TransactionOrder.StartDate))
			}
			log.Info("IAPUser: Deal: insertNewOrder").Str("userID", user.ID).Interface("nextOrder", nextOrder).Send()
			user.InsertOrder(nextOrder)
		} else {
			user.SendPaymentError("[RENEW]", err)
			return err
		}
	}

	if user.VeryFirstOrder {
		// upgraded
		user.SendTransactionEvent("upgraded")
	} else {
		// repeated
		user.SendTransactionEvent("repeated")
	}
	return nil
}

// SendPaymentError send sns
func (user *IAPUser) SendPaymentError(orderType string, err error) {
	if strings.Contains(err.Error(), "Order had been created") {
		//ignore this message due to it appears frequently
		return
	}

	topic := iapcfg[kkapp.App.Debug].PaymentErrorTopic

	subject := fmt.Sprintf("IAP 訂單處理錯誤")
	if orderType != "" {
		subject = fmt.Sprintf("%s IAP 更新訂單資訊失敗 ", orderType)
	}

	message := fmt.Sprintf(`
User ID: %s
%s`, user.ID, err.Error())

	kktvsns.NewPaymentSNS(subject, message, topic)

	slackChannel := iapcfg[kkapp.App.Debug].PaymentErrorSlack
	slackMsg := slack.Attachment{
		Color: "#36a64f",
		Title: subject,
		Text:  message,
	}
	kktvsns.NewPaymentSlack(slackChannel, subject, slackMsg)
}

// NewIAPUser get an IAP user via userID
func NewIAPUser(userID string) (user *IAPUser, err error) {
	user = new(IAPUser)
	db := kkapp.App.DbUser.Slave()
	err = db.Get(user, sqliap["user"], userID)

	if err != nil {
		return nil, err
	}

	return user, nil
}

// IAPRenew renew a user order
// a new order with a new expire date
func IAPRenew(userinfo model.UserInfo) {
	var err error
	var iapuser *IAPUser

	// error handle
	defer func() {
		if err != nil {
			log.Warn("IAPRenew").Str("userID", userinfo.Id).Err(err).Send()
			if iapuser.TransactionOrder == nil {
				return
			}

			session := session.New()
			awsConfig := &aws.Config{
				Region: aws.String("ap-northeast-1"),
			}
			snsClient := sns.New(session, awsConfig)
			input := &sns.PublishInput{
				Subject:  aws.String(fmt.Sprintf("[RENEW] IAP 更新訂單資訊失敗 %s", iapuser.TransactionOrder.ID)),
				Message:  aws.String(err.Error()),
				TopicArn: aws.String(kkapp.App.SnsPaymentError),
			}
			snsClient.Publish(input)
		}
	}()

	iapuser, err = NewIAPUser(userinfo.Id)
	if err != nil {
		return
	}

	iapuser.Verify()

	if iapuser != nil && iapuser.Product != nil {
		go requestBillingDecide(userinfo.Id, userinfo.PaymentType, iapuser.Product.Name)
	}
	err = iapuser.Deal()
}

// PostIAP for /v3/payment/iap post handler for android create order
func PostIAP(w http.ResponseWriter, r *http.Request) {
	access := r.Context().Value(middleware.KeyAccessUser).(mwmodel.AccessUser)
	userID := access.UserID

	var err error
	var payLoad IAPPost

	response := model.MakeOk()
	defer func() {
		if err != nil {
			log.Warn("PostIAP: failed").Str("userID", userID).Err(err).Send()
			errStr := err.Error()
			response.Status.Type = "FAIL"

			// errors.New("Receipt data has been used. original_account: " + username)
			// errors.New("Invalid product name")
			// errors.New("Invalid parameters")
			// errors.New("Order had been created")
			// errors.New("Network error")
			// errors.New("Invalid receipt data")
			// errors.New("User has subscribed")

			if strings.Contains(errStr, "Receipt data has been used") {
				original := make(map[string]string)
				// wording format Receipt data has been used.
				// errors.New("Receipt data has been used. original_account: " + username)
				if strings.Contains(errStr, ": ") {
					userStr := strings.Split(errStr, ": ")[1]
					original["original_account"] = userStr
					response.Data = original
				}
				response.Status.Subtype = errorCode["Receipt data has been used"]
				response.Status.Message = errStr
			} else if code, ok := errorCode[errStr]; ok {
				response.Status.Subtype = code
				response.Status.Message = errStr
			} else {
				response.Status.Subtype = ""
				response.Status.Message = errStr
			}
			render.JSON(w, http.StatusBadRequest, response)
		} else {
			render.JSON(w, http.StatusOK, response)
		}
	}()

	jsDecoder := json.NewDecoder(r.Body)
	if err = jsDecoder.Decode(&payLoad); err != nil {
		err = errors.New("Invalid parameters")
		return
	}

	if payLoad.ReceiptData == "" {
		err = errors.New("Invalid parameters")
		return
	}

	log.Info("PostIAP").Str("userID", userID).Interface("Receipt", payLoad.ReceiptData).Send()

	iapUser, _ := NewIAPUser(userID)
	iapUser.IapReceiptData = null.NewString(payLoad.ReceiptData, true)
	iapUser.Verify()
	iapUser.ValidProduct()

	go requestBillingDecide(userID, "iap", iapUser.Product.Name)

	rid := uuid.New().String()
	broadcaster := broadcasting.GetBroadcaster()
	broadcaster.SyncEmit(events.SignalUserWillBeUpdated, &events.UserWillBeUpdatedEvent{
		RequestID: rid, UserID: userID,
	})
	err = iapUser.Deal()

	if err == nil {
		broadcaster.Emit(events.SignalUserUpdated, &events.UserUpdatedEvent{
			RequestID: rid, UserID: userID,
			UpdateReason: "upgrade via iap payment",
			ModifiedBy:   usermodel.AuditModifierTypeUser.String(),
			ModifierID:   userID,
		})
	}
}
