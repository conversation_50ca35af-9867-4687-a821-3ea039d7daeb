package kkpayment

import (
	"time"

	"gopkg.in/guregu/null.v3"
)

//NotificationLog for notification payload
type NotificationLog struct {
	ID                        string      `db:"id"`
	UserID                    string      `db:"user_id"`
	ProductID                 string      `db:"product_id"`
	OrderID                   null.String `db:"order_id"`
	PaymentType               string      `db:"payment_type"`
	NotificationType          string      `db:"notification_type"`
	CreatedAt                 time.Time   `db:"created_at"`
	Payload                   string      `db:"payload"`
	CancellationReason        string      `db:"cancellation_reason"`
	CanceledAt                time.Time   `db:"canceled_at"`
	IsInBillingRetryPeriod    bool        `db:"is_in_billing_retry_period"`
	ExpirationIntent          string      `db:"expiration_intent"`
	ExpiresDate               time.Time   `db:"expires_date"`
	GracePeriodExpiresDate    time.Time   `db:"grace_period_expires_date"`
	ExternalOrderID           string      `db:"external_order_id"`
	AutoRenewStatusChangeDate time.Time   `db:"auto_renew_status_change_date"`
	AutoRenewStatus           bool        `db:"auto_renew_status"`
}
