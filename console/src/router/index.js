import Vue from 'vue'
import Router from 'vue-router'
/* fix - Navigating to current location (XXX) is not allowed
 * https://www.cnblogs.com/lxk0301/p/11671256.html
 */
const routerPush = Router.prototype.push
Router.prototype.push = function push(location) {
  return routerPush.call(this, location).catch(error=> error)
}


// Containers
import DefaultContainer from '@/containers/DefaultContainer'

// Views
import Dashboard from '@/views/Dashboard'

// Views - Pages
// const Login =  import('@/views/pages/Login')
import Login from '@/views/Login'

// Users
import Users from '@/views/users/Users'
import User from '@/views/users/User'
import UserChangeLog from '@/views/users/UserChangeLog'
import Order from '@/views/users/Order'
import OrderDetail from '@/views/users/OrderDetail'
import OrderAuditLog from '@/views/users/OrderAuditLog'
import Token from '@/views/users/Token'
import Family from '@/views/users/Family'
import MOD from '@/views/users/MOD'
import Favorite from '@/views/users/Favorite'
import WatchHistory from '@/views/users/WatchHistory'
import UserAuditLog from '@/views/users/UserAuditLog'
// const Users = () => import('@/views/users/Users')
// const User = () => import('@/views/users/User')
// const Order = () => import('@/views/users/Order')
// const Token = () => import('@/views/users/Token')

// Products
import Products from '@/views/products/Products'


// Packages
import Packages from '@/views/packages/Packages'

// Content
import Encoder from '@/views/content/Encoder'
import EncoderBV from '@/views/content/EncoderBV'
import Titles from '@/views/content/Titles'
import Series from '@/views/content/Series'
import Publish from '@/views/content/Publish'
import Extra from '@/views/content/Extra'
import Browse from '@/views/content/Browse'
import TitleList from '@/views/content/TitleList'
import TitleListDetail from '@/views/content/TitleListDetail'
import HotKeyWord from '@/views/content/HotKeyWord'
import Announce from '@/views/content/Announce'
import Event from '@/views/content/Event'
import MetaTitleList from '@/views/content/MetaTitleList'
import Ads from '@/views/content/Ads'

// Remote Config
import RemoteConfig from '@/views/remoteconfig/RemoteConfig'

// TV Events
import TVEvents from '@/views/tvevents/TVEvents'

// Redeem
import Redeem from '@/views/redeem/Redeem'
import RedeemDetail from '@/views/redeem/RedeemDetail'

// ServiceStatus
import ServiceStatus from '@/views/servicestatus/ServiceStatus'

// Finance
import Income from '@/views/finance/income'

// Android Devices
import AndroidDevices from '@/views/android/devices'

// Auth
import auth from '@/auth'

// Titlelist
import Headline from '@/views/titlelist/headline'
import Choice from '@/views/titlelist/choice'
import Highlight from '@/views/titlelist/highlight'
import Ranking from '@/views/titlelist/ranking'
import Airing from '@/views/titlelist/airingList'

Vue.use(Router)

export default new Router({
  mode: 'hash', // https://router.vuejs.org/api/#mode
  linkActiveClass: 'open active',
  scrollBehavior: () => ({ y: 0 }),
  routes: [
    {
      path: '/',
      redirect: '/dashboard',
      beforeEnter: auth.requireAuth,
      name: 'Home',
      component: DefaultContainer,
      children: [
        {
          path: 'dashboard',
          name: 'Dashboard',
          component: Dashboard
        },
        {
          path: 'user',
          meta: { label: 'Users'},
          component: {
            render (c) { return c('router-view') }
          },
          children: [
            {
              path: '',
              component: Users,
            },
            {
              path: 'userchangelog',
              meta: { label: 'User 修改紀錄查詢'},
              name: 'UserChangeLog',
              component: UserChangeLog
            },
            {
              path: 'order',
              meta: { label: 'Order'},
              name: 'Order',
              component: Order
            },
            {
              path: 'order/:order_id',
              meta: { label: 'OrderDetail' },
              name: 'OrderDetail',
              component: OrderDetail
            },
            {
              path: 'order/audit_log/:order_id',
              meta: { label: 'OrderAuditLog' },
              name: 'OrderAuditLog',
              component: OrderAuditLog
            },
            {
              path: 'token',
              meta: { label: 'Token'},
              name: 'Token',
              component: Token
            },
            {
              path: 'family',
              meta: { label: 'Family'},
              name: 'Family',
              component: Family
            },
            {
              path: 'mod',
              meta: { label: 'MOD'},
              name: 'MOD',
              component: MOD
            },
            {
              path: 'favorite/:id',
              meta: { label: 'Favorite'},
              name: 'Favorite',
              component: Favorite
            },
            {
              path: 'watch_history/:id',
              meta: { label: 'WatchHistory'},
              name: 'WatchHistory',
              component: WatchHistory
            },
            {
              path: 'audit_log/:id',
              meta: { label: 'UserAuditLog' },
              name: 'UserAuditLog',
              component: UserAuditLog
            },
            {
              path: ':id',
              meta: { label: 'User Details'},
              name: 'User',
              component: User
            },
          ]
        },
        {
          path: 'redeem',
          meta: { label: 'Redeem'},
          component: {
            render (c) { return c('router-view') }
          },
          children: [
            {
              path: '',
              component: Redeem,
            },
            {
              path: 'detail',
              meta: { label: 'Redeem Detail'},
              name: 'RedeemDetail',
              component: RedeemDetail
            }
          ]
        },
        {
          path: 'servicestatus',
          name: 'ServiceStatus',
          component: ServiceStatus
        },
        {
          path: 'product',
          name: 'Products',
          component: Products
        },
        {
          path: 'package',
          name: 'Packages',
          component: Packages
        },
        {
          path: 'content',
          meta: { label: 'Content'},
          component: {
            render (c) { return c('router-view') }
          },
          children: [
            {
              path: 'title',
              meta: { label: 'Title'},
              name: 'Title',
              component: Titles
            },
            {
              path: 'series',
              meta: { label: 'Series'},
              name: 'Series',
              component: Series
            },
            {
              path: 'publish',
              meta: { label: 'Publish'},
              name: 'Publish',
              component: Publish
            },
            {
              path: 'extra',
              meta: { label: 'Extra'},
              name: 'Extra',
              component: Extra
            },
            {
              path: 'browse',
              meta: { label: 'Browse'},
              name: 'Browse',
              component: Browse
            },
            {
              path: 'titlelist',
              meta: { label: 'TitleList'},
              name: 'TitleList',
              component: TitleList
            },
            {
              path: 'hotkeyword',
              meta: { label: 'HotKeyWord'},
              name: 'HotKeyWord',
              component: HotKeyWord
            },
            {
              path: 'titlelist/:titlekey',
              meta: { label: 'TitleListDetail'},
              name: 'TitleListDetail',
              component: TitleListDetail
            },
            {
              path: 'announce',
              meta: { label: 'Announce'},
              name: 'Announce',
              component: Announce
            },
            {
              path: 'event',
              meta: { label: 'Event'},
              name: 'Event',
              component: Event
            },
            {
              path: 'metatitlelist/:list_type',
              meta: {
                label: 'TitleList'
              },
              name: 'MetaTitleList',
              component: MetaTitleList
            },
            {
              path: 'encoder',
              meta: { label: 'Encoder'},
              name: 'Encoder',
              component: Encoder
            },
            {
              path: 'encoderbv',
              meta: { label: 'Encoder BV'},
              name: 'Encoder BV',
              component: EncoderBV
            },
            {
              path: 'ads',
              meta: {
                label: 'Ads'
              },
              name: 'Ads',
              component: Ads
            },
          ]
        },
        {
          path: 'remoteconfig',
          name: 'RemoteConfig',
          component: RemoteConfig
        },
        {
          path: 'tvevents',
          name: 'TVEvents',
          component: TVEvents
        },
        {
          path: 'finance/income',
          name: 'Income Report',
          component: Income
        },
        {
          path: 'android/devices',
          name: 'Android Devices',
          component: AndroidDevices
        },
        {
          path: '/headline/:browseKey',
          meta: { label: 'Headline'},
          name: 'Headline',
          component: Headline
        },
        {
          path: '/choice/:browseKey',
          meta: { label: 'Choice'},
          name: 'Choice',
          component: Choice
        },
        {
          path: '/highlight/:browseKey',
          meta: { label: 'Highlight'},
          name: 'Highlight',
          component: Highlight
        },
        {
          path: '/ranking/:browseKey',
          meta: { label: 'Ranking'},
          name: 'Ranking',
          component: Ranking
        },
        {
          path: '/airing/:browseKey',
          meta: { label: 'Airing'},
          name: 'Airing',
          component: Airing
        }
      ]
    },
    {
      path: '/login',
      name: 'Login',
      component: Login
    }
  ]
})
