export default {
  items: [
    {
      name: 'Dashboard',
      url: '/dashboard',
      icon: 'icon-speedometer'
      // badge: {
      //   variant: 'primary',
      //   text: 'NEW'
      // }
    },
    // {
    //   title: true,
    //   name: 'Extras'
    // },
    {
      name: 'Customer',
      url: '/user',
      icon: 'icon-people',
      children: [
        {
          name: 'User',
          url: '/user',
          icon: 'icon-user'
        },
        {
          name: 'User修改紀錄查詢',
          url: '/user/userchangelog',
          icon: 'icon-user'
        },
        {
          name: 'Order',
          url: '/user/order',
          icon: 'icon-handbag'
        },
        {
          name: 'Token',
          url: '/user/token',
          icon: 'icon-badge'
        },
        {
          name: 'Family',
          url: '/user/family',
          icon: 'fa fa-users'
        },
        {
          name: 'M<PERSON>(查中華電信)',
          url: '/user/mod',
          icon: 'icon-badge'
        }
      ]
    },
    {
      name: 'Redeem',
      url: '/redeem',
      icon: 'icon-tag'
    },
    {
      name: 'ServiceStatus',
      url: '/servicestatus',
      icon: 'fa fa-cog'
    },
    {
      name: 'Product',
      url: '/product',
      icon: 'icon-present'
    },
    {
      name: 'Package',
      url: '/package',
      icon: 'fa fa-gift'
    },
    {
      name: 'Content',
      url: '/content',
      icon: 'icon-doc',
      children: [
        {
          name: 'Title',
          url: '/content/title',
          icon: 'icon-docs'
        },
        {
          name: 'Series',
          url: '/content/series',
          icon: 'icon-layers'
        },
        {
          name: 'Extra',
          url: '/content/extra',
          icon: 'fa fa-file-video-o'
        },
        {
          name: 'Publish',
          url: '/content/publish',
          icon: 'fa fa-calendar-check-o'
        },
        {
          name: 'Browse 選單',
          url: '/content/browse',
          icon: 'icon-list'
        },
        {
          name: 'Browse 片單',
          url: '/content/titlelist',
          icon: 'icon-list'
        },
        {
          name: 'HotKeyWord',
          url: '/content/hotkeyword',
          icon: 'icon-magnifier'
        },
        {
          name: 'Announce',
          url: '/content/announce',
          icon: 'icon-feed'
        },
        {
          name: 'Event',
          url: '/content/event',
          icon: 'icon-info'
        },
        {
          name: 'Headline 片單',
          url: '/content/metatitlelist/link,title',
          icon: 'icon-graph'
        },
        {
          name: 'Choice 片單',
          url: '/content/metatitlelist/choice',
          icon: 'icon-list'
        },
        {
          name: 'Highlight 片單',
          url: '/content/metatitlelist/highlight',
          icon: 'icon-list'
        },
        {
          name: 'Encoder',
          url: '/content/encoder',
          icon: 'icon-camrecorder'
        },
        {
          name: 'Encoder BV',
          url: '/content/encoderbv',
          icon: 'icon-camrecorder'
        },
        {
          name: 'Ads',
          url: '/content/ads',
          icon: 'icon-basket'
        }
      ]
    },
    {
      name: 'RemoteConfig',
      url: '/remoteconfig',
      icon: 'icon-present'
    },
    {
      name: 'TVEvents',
      url: '/tvevents',
      icon: 'fa fa-tv'
    },
    {
      name: 'Income Report',
      url: '/finance/income',
      icon: 'fa fa-table'
    },
    {
      name: 'Android Devices',
      url: '/android/devices',
      icon: 'fa fa-android'
    }
  ]
}
