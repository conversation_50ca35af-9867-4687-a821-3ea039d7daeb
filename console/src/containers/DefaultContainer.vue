<template>
  <div class="app">
    <AppHeader fixed>
      <SidebarToggler class="d-lg-none" display="md" mobile/>
      <b-link class="navbar-brand" to="#">
        <img class="navbar-brand-full" src="img/logo.png" width="100" height="35" alt="KKTV Logo">
        <img class="navbar-brand-minimized" src="img/logo-symbol.png" width="35" height="35" alt="KKTV Logo">
      </b-link>
      <SidebarToggler class="d-md-down-none" display="lg"/>
      <b-navbar-nav class="d-md-down-none">
        <b-nav-item class="px-3" to="/dashboard">Dashboard</b-nav-item>
      </b-navbar-nav>
      <b-navbar-nav class="ml-auto">
        <DefaultHeaderDropdownAccnt/>
      </b-navbar-nav>
      <AsideToggler class="d-none d-lg-block"/>
    </AppHeader>
    <div class="app-body">
      <AppSidebar fixed>
        <SidebarHeader/>
        <SidebarForm/>
        <SidebarNav :navItems="nav"></SidebarNav>
        <SidebarFooter/>
        <SidebarMinimizer/>
      </AppSidebar>
      <main class="main">
        <Breadcrumb :list="list"/>
        <div class="container-fluid">
          <router-view></router-view>
        </div>
      </main>
      <AppAside fixed>
        <!--aside-->
        <DefaultAside/>
      </AppAside>
    </div>
    <TheFooter>
      <!--footer-->
      <div>
        <a href="https://www.kktv.me">KKTV</a>
        <span class="ml-1">&copy; 2018-{{ new Date().getFullYear() }}</span>
      </div>
      <div class="ml-auto">
        Made with&nbsp;<i class="fa fa-heart" aria-hidden="true" style="color:red;"></i>
      </div>
    </TheFooter>
  </div>
</template>

<script>
import nav from '@/_nav'
import {
  Aside as AppAside,
  AsideToggler,
  Breadcrumb,
  Footer as TheFooter,
  Header as AppHeader,
  Sidebar as AppSidebar,
  SidebarFooter,
  SidebarForm,
  SidebarHeader,
  SidebarMinimizer,
  SidebarNav,
  SidebarToggler
} from '@coreui/vue'
import DefaultAside from './DefaultAside'
import DefaultHeaderDropdownAccnt from './DefaultHeaderDropdownAccnt'
import getFeaturedPageBrowses from "@/utils/browse";

export default {
  name: 'full',
  components: {
    AsideToggler,
    AppHeader,
    AppSidebar,
    AppAside,
    TheFooter,
    Breadcrumb,
    DefaultAside,
    DefaultHeaderDropdownAccnt,
    SidebarForm,
    SidebarFooter,
    SidebarToggler,
    SidebarHeader,
    SidebarNav,
    SidebarMinimizer
  },
  data() {
    this.getBrowses().then((result) => {
      let isBrowseNotLoaded = !nav.items.some(item => item.name === '精選');
      if (isBrowseNotLoaded) {
        const indexOfContent = nav.items.findIndex(item => item.name === 'Content');
        let insertPosition = indexOfContent + 1;
        result.forEach((item) => {
          nav.items.splice(insertPosition++, 0, item);
        })
      }
    })

    return {
      nav: nav.items,
    }
  },
  computed: {
    name() {
      return this.$route.name
    },
    list() {
      return this.$route.matched.filter((route) => route.name || route.meta.label)
    }
  },
  methods: {
    async getBrowses() {
      return await getFeaturedPageBrowses()
        .then((items) => {
          if (items) {
            return items.map((item) => {
              return {
                name: item.title,
                url: '/' + item.title,
                icon: 'fa fa-columns',
                children: this.setNavChildren(item.title, item.collection_type, item.collection_name),
              }
            })
          }
        })
    },
    setNavChildren(title, collectionType, collectionName) {
      const collectionKey = collectionType + ':' + collectionName
      let children = []
      children.push({
        name: 'Headline片單',
        url: '/headline/' + collectionKey,
      })
      // uncomment this when cms genre page done

      children.push({
        name: 'Choice片單',
        url: '/choice/' + collectionKey,
      })

      if (['genre', 'plan'].includes(collectionType)) {
        children.push({
          name: '排行榜',
          url: '/ranking/' + collectionKey,
        })
      }

      if (collectionKey === 'genre:featured' || collectionKey === 'genre:動漫') {
        children.push({
          name: 'Highlight片單',
          url: '/highlight/' + collectionKey,
        })
      }

      if (collectionKey === 'genre:動漫') {
        children.push({
          name: 'Airing 時間表',
          url: '/airing/' + collectionKey,
        })
      }
      return children
    },
  }
}
</script>

<style>
.sidebar-nav .nav-dropdown-items .nav-item {
  padding-left: 0.8rem;
}
.toast-body {
  white-space: pre-wrap;
}
</style>
