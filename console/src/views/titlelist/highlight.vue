<script>
import TitlelistBase, {getSize, validateBackgroundImage, validateOGImage} from "@/views/titlelist/Titlelist";

export default {
  name: 'TitleList',
  extends: TitlelistBase,
  props: {
    canSetDefaultTitlelist: {type: Boolean, default: false},
    listType: {type: String, default: 'highlight'},
    supportTypeList: {
      type: Array,
      default: () => ['highlight']
    }
  },
  data: () => {
    return {
      tableFields: [
        {key: 'action', label: ''},
        {key: 'enabled', label: '啟用'},
        {key: 'duration'},
        {key: 'image', label: 'image'},
        {key: 'topic', label: 'Topic / Caption / Summary'},
      ],
    }
  },
  methods: {
    newEmptyItem() {
      const today = new Date();
      const startOfNextMonth = new Date(today.getFullYear(), today.getMonth() + 1, 1, 0, 0, 0)
      const endOfNextMonth = new Date(today.getFullYear(), today.getMonth() + 2, 1, 0, 0, 0)

      const browseKey = this.browseKey
      return {
        'meta': {
          'title_id': ['empty_group_item'],
          'collections': [],
          'video_url': ''
          },
        'list_type': 'highlight',
        'dominant_color': '#000000',
        'visible_since': startOfNextMonth, 'visible_until': endOfNextMonth,
        'enabled': true,

      }
    },
    async validate(data) {
      let titlelist = data.info
      let record = data.record
      console.info('going to validate form:', titlelist)
      let errFields = []

      errFields.push(...this.commonValidation(titlelist))

      if (!titlelist.meta || !titlelist.meta.title_id || titlelist.meta.title_id.length === 0) {
        errFields.push('titleIDs')
      }

      if (!titlelist.dominant_color || !/^#([0-9A-F]{6})$/i.test(titlelist.dominant_color)) {
        errFields.push('dominantColor')
      }

      if (titlelist.meta.description) {
        if (titlelist.meta.description.length > 42) {
          errFields.push('desc')
        }
      }

      if (data.ogImg) {
        let choiceOgImageSize = await getSize(data.ogImg).then(imageSize => imageSize)
        if (!validateOGImage(choiceOgImageSize)) {
          errFields.push('ogImg')
        }
      }

      if (data.bgImg) {
        let choiceBackgroundImageSize = await getSize(data.bgImg).then(imageSize => imageSize)
        if (!validateBackgroundImage(choiceBackgroundImageSize)) {
          errFields.push('bgImg')
        }
      }
      return errFields.length === 0 ? null : errFields
    }
  }
}
</script>

<style src="./asset/style.css" scoped></style>
