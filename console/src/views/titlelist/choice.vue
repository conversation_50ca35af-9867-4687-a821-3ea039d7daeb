<script>
import TitlelistBase from "@/views/titlelist/Titlelist";

export default {
  name: 'TitleList',
  extends: TitlelistBase,
  props: {
    canSetDefaultTitlelist: {
      type: Boolean,
      default: true
    },
    listType: {type: String, default: 'choice'},
    supportTypeList: {
      type: Array,
      default: () => ['choice']
    },
    pinnedOptions: {
      type: Array,
      default: () => [
        {value: null, text: 'Please select an option'},
        {value: 'airing', text: '新劇跟播片單'},
        {value: 'new_finale', text: '全集新上架'},
        {value: 'guaranteed_visible', text: '保證曝光'}
      ]
    },
  },
  data: () => {
    return {
      tableFields: [
        {key: 'action', label: ''},
        {key: 'enabled', label: '啟用'},
        {key: 'duration'},
        {key: 'bg_image', label: 'Background image'},
        {key: 'topic', label: 'Topic / Caption / Summary'},
        {key: 'collection', label: '分類'},
        {key: 'pinned', label: '設定釘選'},
      ],
    }
  },
  methods: {
    newEmptyItem() {
      const that = this
      const now = new Date()
      return {
        list_type: 'choice',
        visible_since: now, visible_until: now,
        enabled: true, order: 1,
        meta: {
          collections: [that.browseKey],
          title_id: ['empty_group_item']
        }
      }
    },
    async validate() {
      return null;
    }
  }
}
</script>

<style src="./asset/style.css" scoped></style>