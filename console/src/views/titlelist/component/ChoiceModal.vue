<template>
  <div>
    <b-modal
      size="xl"
      v-model="showModal"
      :title="getModalTitleTxt"
      ref="formModal"
      @ok="onOK"
      @cancel="onCancel"
      @close="reset"
      no-close-on-backdrop
      no-close-on-esc
    >
      <form ref="form" @submit.stop.prevent="onSubmit">
        <div>
          <div class="section-title">
            片單基本設定
          </div>
          <div class="section-container">
            <div class="section-container-item">
              <Category
                :validStates="validStates"
                :formRecord="formRecord"
                :required="true"
              />
              <StatusActive :formRecord="formRecord" />
              <PinnedList :formRecord="formRecord" />
              <VisibleDuration
                :validStates="validStates"
                :formRecord="formRecord"
                :required="true"
              />
            </div>
            <div class="section-container-item">
              <ShareLink
                :formRecord="formRecord"
                :client-base-url="clientBaseUrl"
                :record="record"
              />
              <div class="image-section">
                <ImageUpload
                  :validStates="validStates"
                  :formRecord="formRecord"
                  image-type="ogImg"
                  :image-info="imageInfo.ogImg"
                  ref="imageUpload_ogImg"
                />
              </div>
            </div>
          </div>
        </div>

        <hr class="style-one">
        <div>
          <div class="section-title">
            設定片單頁的顯示
          </div>
          <div class="section-container">
            <div class="section-container-item">
              <ListCaption
                :validStates="validStates"
                :formRecord="formRecord"
                :captionInfo="captionInfo"
              />
              <ListDescription
                :validStates="validStates"
                :formRecord="formRecord"
                :descriptionInfo="descriptionInfo"
              />
            </div>
            <div class="section-container-item">
              <div class="image-section">
                <ImageUpload
                  :validStates="validStates"
                  :formRecord="formRecord"
                  image-type="bgImg"
                  :image-info="imageInfo.bgImg"
                  ref="imageUpload_bgImg"
                />
              </div>
              <CopyRight :formRecord="formRecord" />
            </div>
          </div>
        </div>

        <hr class="style-one">
        <div>
          <div class="section-title">
            編輯片單
          </div>
          <EditTitle
            :validStates="validStates"
            :formRecord="formRecord"
            :required="true"
            ref="editTitle"
          />
        </div>
      </form>
    </b-modal>
  </div>
</template>

<script>
import Category from './modalEditItem/Category.vue'
import StatusActive from './modalEditItem/StatusActive.vue'
import VisibleDuration from './modalEditItem/VisibleDuration.vue'
import ShareLink from './modalEditItem/ShareLink.vue'
import ListCaption from './modalEditItem/ListCaption.vue'
import ListDescription from './modalEditItem/ListDescription.vue'
import CopyRight from './modalEditItem/CopyRight.vue'
import PinnedList from './modalEditItem/PinnedList.vue'
import ImageUpload from './modalEditItem/ImageUpload.vue'
import EditTitle from './modalEditItem/EditTitle.vue'

const _ = require('lodash')

const IMAGE_INFO = {
  bgImg: {
    label: 'Title list Image 片單頁主視覺',
    description: '比例 16:9，尺寸 1920*1080px，檔案大小<2MB，若未上傳則預設為片單第一個作品的第一張劇照'
  },
  ogImg: {
    label: 'OG Image',
    description: '分享至其他社群時的顯示圖片，尺寸 1200*630px，檔案大小<2MB，若未上傳將顯示預設圖片'
  }
}

export default {
  name: 'ChoiceModal',
  components: {
    Category,
    StatusActive,
    PinnedList,
    VisibleDuration,
    ShareLink,
    ListCaption,
    ListDescription,
    CopyRight,
    EditTitle,
    ImageUpload
  },
  props: {
    record: {
      type: Object,
      default: () => {
        return {
          id: null, list_type: []
        }
      }
    },
    show: {
      type: Boolean, default: false
    },
    clientBaseUrl: {
      type: String,
      default: null
    }
  },
  provide() {
    return {
      registerValidation: this.registerValidation
    }
  },
  data() {
    return {
      showModal: this.show,
      formRecord: this.initFormRecord(this.record),
      validationFields: new Set(),
      validStates: {
        collections: null,
        duration: null,
        caption: null,
        titleIDs: null,
        desc: null,
        bgImg: null,
        ogImg: null,
        copyright: null
      },
      captionInfo: {
        label: '片單標題 Title List Name',
        subscript: '限 15 字，也會顯示在片單頁內',
        lengthCheck: 15,
        required: true,
      },
      descriptionInfo: {
        label: '片單簡介 Title List Description',
        subscript: '限 40 字，也會顯示在片單頁內，建議寫長於片單標題視覺效果較佳',
        lengthCheck: 40,
        required: false
      },
      imageInfo: IMAGE_INFO
    }
  },
  computed: {
    getModalTitleTxt() {
      return this.record.id && this.record.id !== '' ?
        '修改 ' + this.record.title : '新增片單'
    }
  },
  methods: {
    initFormRecord(record) {
      const clonedRecord = _.cloneDeep(record)
      if (!clonedRecord.meta) {
        clonedRecord.meta = {}
      }
      return clonedRecord
    },
    registerValidation(component) {
      this.validationFields.add(component)

      return () => this.validationFields.delete(component)
    },
    validateAll() {
      let isValid = true;
      for (const component of this.validationFields) {
        if (typeof component.validate === 'function') {
          const fieldValid = component.validate()
          if (!fieldValid) {
            isValid = false
          }
        }
      }
      return isValid
    },
    onSubmit() {
      console.log("this.formRecord:", this.formRecord)
      if (!this.validateAll()) {
        return
      }
      let info = _.cloneDeep(this.formRecord);
      info.meta.title_id = info.meta.title_id.filter(value => value !== 'empty_group_item');

      const bgImg = this.$refs.imageUpload_bgImg.getFile('bgImg') || null
      const ogImg = this.$refs.imageUpload_ogImg.getFile('ogImg') || null

      this.$emit('submit', {
        info,
        record: this.record,
        bgImg,
        ogImg
      })
    },
    onOK(modalEvent) {
      modalEvent.preventDefault()
      this.onSubmit()
    },
    onCancel() {
      this.reset()
    },
    reset() {
      for (const [key] of Object.entries(this.validStates)) {
        this.validStates[key] = null
      }
      if (this.$refs.editTitle) {
        this.$refs.editTitle.reset();
      }
      const imageTypes = ['ogImg', 'bgImg'];
      imageTypes.forEach(type => {
        if (this.$refs[`imageUpload_${type}`]) {
          this.$refs[`imageUpload_${type}`].reset();
        }
      });
    }
  },
  watch: {
    show: {
      handler(val) {
        this.showModal = val
      }
    },
    showModal: {
      deep: true,
      handler(val) {
        this.$emit('show', val)
      }
    },
    record: {
      deep: true,
      handler(val) {
        this.formRecord = this.initFormRecord(val)
      }
    }
  }
}
</script>

<style scoped>
</style>
