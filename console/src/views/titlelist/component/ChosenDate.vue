<template>
  <b-form-group description="輸入時間可以過濾出該時間點看得到的片單">
    <b-input-group prepend="時間">
      <flat-pickr v-model="picked" :config="datePickerConfig" class="form-control" />
      <b-input-group-append>
        <b-button  title="Clear" data-clear @click="picked = null">
          <i class="fa fa-times fa-sm">
            <span aria-hidden="true" class="sr-only">Clear</span>
          </i>
        </b-button>
      </b-input-group-append>
    </b-input-group>
  </b-form-group>
</template>

<script>
function getDefaultDisplayDate() {
  const today = new Date();
  return new Date(today.getFullYear(), today.getMonth(), today.getDate(), 12, 5, 0)
}

export default {
  name: "ChosenDate",
  props: {
    datePickerConfig: Object,
  },
  data() {
    const defaultDisplayDate = getDefaultDisplayDate();
    return {
      picked: defaultDisplayDate,
      defaultDate: defaultDisplayDate
    }
  },
  methods: {
    resetData() {
      Object.assign(this.$data, this.$options.data());
    },
    getDefaultDisplayDate: getDefaultDisplayDate,
  },
  mounted() {
    const that = this;
    this.$on('reset', () => {
      that.resetData()
    });
  },
  watch: {
    picked: {
      deep: true,
      handler(val) {
        this.$emit('update', val)
      }
    }
  }
}
</script>

<style scoped>

</style>
