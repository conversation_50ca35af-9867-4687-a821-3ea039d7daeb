<template>
  <b-form-group :label="captionInfo.label" :description="captionInfo.subscript" :state="validStates.caption"
    :invalid-feedback="validationMessage" class="section-form-group">
    <template #label v-if="captionInfo.required">
      <span class="required-field">*</span>{{ captionInfo.label }}
    </template>
    <b-form-input v-model="formRecord.title" type="text" :state="validStates.caption" required></b-form-input>
  </b-form-group>
</template>

<script>
export default {
  name: 'ListCaption',
  props: {
    validStates: Object,
    formRecord: Object,
    captionInfo: {
      label: String,
      subscript: String,
      required: Boolean,
      lengthCheck: Number
    }
  },
  inject: ['registerValidation'],
  provide() {
    return {
      registerValidation: this.registerValidation
    }
  },
  data() {
    return {
      validationMessage: ''
    }
  },
  mounted() {
    const unregister = this.registerValidation(this)
    this.$once('hook:beforeDestroy', unregister)
  },
  methods: {
    validate() {
      const title = this.formRecord.title || '';
      const trimmedTitle = title.trim();
      const titleLength = title.length;
      const validationRules = [
        {
          condition: () =>
            trimmedTitle.length === 0 && titleLength >= 1,
            errorMessage: `Can't be space`
        },
        {
          condition: () =>
            this.captionInfo.required && !trimmedTitle,
            errorMessage: `Can't be blank`
        },
        {
          condition: () =>
            this.captionInfo.lengthCheck && titleLength > this.captionInfo.lengthCheck,
            errorMessage: `Can't be longer than ${this.captionInfo.lengthCheck} characters`
        }
      ]
      for (const rule of validationRules) {
        if (rule.condition()) {
          this.updateValidState(false, rule.errorMessage);
          return false;
        }
      }

      this.updateValidState(true);
      return true;
    },
    updateValidState(isValid, message = '') {
      this.validStates.caption = isValid;
      this.validationMessage = message;
    }
  }
}
</script>