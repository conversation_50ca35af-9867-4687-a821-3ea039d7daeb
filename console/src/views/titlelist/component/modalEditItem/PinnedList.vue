<template>
  <div>
    <label name="pinned" for="pinned">設定為釘選</label>
    <select
      v-model="formRecord.meta.pinned"
      id="pinned"
      class="form-select"
    >
      <option
        v-for="option in pinnedOptions"
        :value="option.value"
        :key="option.value"
        :id="option.value"
      >
        {{ option.text }}
      </option>
    </select>
    <small class="form-text text-muted">
      標記「新劇跟播片單」或「全集新上架」或「保證曝光」
    </small>
  </div>
</template>

<script>
export default {
  name: 'PinnedList',
  props: {
    formRecord: Object
  },
  data: () => {
    return {
      pinnedOptions: [
        {value: null, text: 'Please select an option'},
        {value: 'airing', text: '新劇跟播片單'},
        {value: 'new_finale', text: '全集新上架'},
        {value: 'guaranteed_visible', text: '保證曝光'}
      ]
    }
  }
}
</script>

<style scoped>
.form-select {
  width: 100%;
  border: 1px solid #e4e7ea;
  border-radius: 5px;
  height: 35px;
  padding: 4px 10px;
  color: #5c6873;
  appearance: none;
  letter-spacing: 1px;
  background-image:
    linear-gradient(45deg, transparent 50%, gray 50%),
    linear-gradient(135deg, gray 50%, transparent 50%),
    linear-gradient(to right, #ccc, #ccc);
  background-position:
  calc(100% - 15px) calc(1em + 2px),
    calc(100% - 10px) calc(1em + 2px),
    calc(100% - 30px) 0.5em;
    background-size:
    5px 5px,
    5px 5px,
    1px 1.5em;
  background-repeat: no-repeat;

  &:focus {
    outline: none;
  }
}
</style>
