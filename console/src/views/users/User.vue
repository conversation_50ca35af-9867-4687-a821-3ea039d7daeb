<template>
  <b-row>
    <b-col cols="12" xl="12">
      <b-card no-header>
        <template slot="header">
          <b-button class="mr-1" @click="goBack">&lt;</b-button>
          <b-button class="mr-1" @click="onOrder($route.params.id)">Order</b-button>
          <b-button v-if="familyID && familyID != ''" class="mr-1" @click="onFamily(familyID)" v-b-tooltip.hover title="有發現 family 資訊，查詢是否有 family 方案及成員">Family</b-button>
          <b-button class="mr-1" @click="onToken($route.params.id)">Token</b-button>
          <b-button class="mr-1" @click="onFavorite($route.params.id)">Favorite Title</b-button>
          <b-button class="mr-1" @click="onWatchHistory($route.params.id)">Watch History</b-button>
          <b-button class="mr-1" @click="onUserInfo($route.params.id)">UserInfo</b-button>
          <b-button class="mr-1" @click="onAmplitude($route.params.id)" variant="primary">Amplitude</b-button>
          <b-button class="mr-1" @click="onAuditLog($route.params.id)">Audit Log</b-button>
          <b-button v-if="item.payment_type =='iab' || item.payment_type =='iap'"
            v-b-tooltip.hover title="會即時查詢 Google(iab) 或是 Apple(iap) 目前收據狀態，is_in_grace_period 如果是 true 表示使用者在寬限期中 "
            @click="onUserReceipt($route.params.id)">查詢 {{ item.payment_type}} 收據</b-button>
          <br />
          User id:  {{ $route.params.id }}
          <br />
          expired at <strong>{{ new Date(item.expiredAt*1000).toLocaleString()}}</strong> <strong v-if="receiptMsg"></strong>
        </template>
        <b-table striped small fixed responsive="sm" :items="filterItems" :fields="fields">
          <template slot="value" slot-scope="data">

            <div v-if="data.item.key === 'mediaSource' && kkbox !=''">
              <b-button variant="primary"  @click="onKKBOX(kkbox)">
                KKBOX 帳號狀態
              </b-button>&nbsp;
            </div>
            <div v-else-if="data.item.key === 'mediaSource' && item.origin_provider !== 'kkbox' && kkbox !=''">
              <b-button variant="warning"
                        v-b-tooltip.hover title="刪除帳號中對應 kkbox 的資訊，用戶可以重新用 kkbox 登入重新綁定" @click="onUpdate(item.id, $event, 'unbind')">
                <i class="fa" />
                解除 KKBOX 帳號綁定
              </b-button>
            </div>
            <div v-else-if="data.item.key === 'mod_subscriber_id' && data.item.value">
              <b-button variant="primary" @click="onMOD(data.item.value)">HINET MOD 狀態</b-button>
            </div>

            <div v-if="data.item.key === 'email'">
              <strong>{{data.item.value}}</strong>
              <div v-if="item['email_verified_at'] && item['email_verified_at'] !== 0">
                <b-badge variant="success">verified</b-badge>
                <span class="text">於 {{ new Date(item['email_verified_at']*1000).toLocaleString()}}</span>
              </div>
              <div v-else><b-badge variant="secondary">unverified</b-badge></div>
            </div>
            <div v-else-if="data.item.key === 'phone'">
              <strong>{{data.item.value}}</strong>
              <div v-if="item['phone_verified_at'] && item['phone_verified_at'] !== 0">
                <b-badge variant="success">verified</b-badge>
                <span class="text">於 {{ new Date(item['phone_verified_at']*1000).toLocaleString()}}</span>
              </div>
              <div v-else><b-badge variant="secondary">unverified</b-badge></div>
            </div>
            <div v-else>
              <strong>{{data.item.value}}</strong>
            </div>
          </template>

        </b-table>
        <template slot="footer"></template>
      </b-card>
      <b-card-group>
        <b-card>
          <div class="mt-2">Username:</div>
          <b-form-input type="text" v-model="tmpUser.name"></b-form-input>
          <div class="mt-2">Email:</div>
          <b-form-input type="text" v-model="tmpUser.email"></b-form-input>
          <div class="mt-2">Phone:</div>
          <b-form-input type="text" v-model="tmpUser.phone"></b-form-input>
          <span class="m-md-1 icon-pencil"></span>請選擇要變更的 Membership: <b-form-select v-model="tmpUser.membership[0].role" :options="membershipOptions" class="m-md-2" />
          <span class="m-md-1 icon-pencil"></span>請選擇要變更的 Auto Renew: <b-form-select v-model="tmpUser.auto_renew" :options="autorenewOptions" class="m-md-2" />
          <span class="m-md-1 icon-calendar"></span>請選擇要變更的到期日 expiredAt:<flat-pickr v-model="expired_at" :config="config" @on-close="onTimeChange()"></flat-pickr>
          <div class="mt-2">異動原因:</div>
          <form @submit.stop.prevent>
            <b-form-group :state="reasonInfoState" invalid-feedback="*必填欄位">
              <b-form-input type="text" v-model="reason_info" :state="reasonInfoState" required></b-form-input>
            </b-form-group>
          </form>
          <div style="width: 270px;">
            <b-button variant="warning" v-b-tooltip.hover title="修改／儲存使用者，您必須有 客服管理 的權限"
                @click="onUpdate(item.id, $event)">
                <i class="fa" />
                更新使用者資料
            </b-button>
          </div>
        </b-card>
        <b-card>
          <div class="mt-2" v-if="this.item.payment_type">
            Recipient Adderss:
            <b-form-input type="text" v-model="tmpUser.recipient_address"></b-form-input>
            載具類別(0=手機條碼載具 1=自然人憑證條碼載具 2=智付寶載具):
            <b-form-group :state="reasonCarrierTypeState" invalid-feedback="*必須是 0, 1 或 2 ，且須填寫手機條碼載具">
              <b-form-input type="text" v-model="tmpUser.carrier_type"></b-form-input>
            </b-form-group>

            載具編號(一般為手機條碼載具):
            <b-form-input type="text" v-model="tmpUser.carrier_value"></b-form-input>
            異動原因:
            <form @submit.stop.prevent>
              <b-form-group :state="reasonPaymentState" invalid-feedback="*必填欄位">
                <b-form-input type="text" v-model="reason_payment" :state="reasonPaymentState" required></b-form-input>
              </b-form-group>
            </form>
            <b-button variant="warning" v-b-tooltip.hover title="修改／儲存使用者，您必須有 客服管理 的權限"
              @click="onUpdate(item.id, $event, 'payment')">
              <i class="fa" />
              更新使用者付款資料
            </b-button>
            <hr />

          </div>

          <div class="mt-2" v-if="this.item.email">
            <b-button v-if="item.email"
              variant="warning"
              @click="onUpdate(item.id, $event, 'edmUnsubscribe')"
              v-b-tooltip.hover title="User 拒收EDM">
              <i class="fa" />User EDM 取消
            </b-button>

            <hr />
          </div>

          <div class="mt-2" v-if="item.mod_subscriber_id">
            <b-button variant="warning"
                @click="onUpdate(item.id, $event, 'unbindMOD')"
                v-b-tooltip.hover title="清除這個使用者目前綁定的 MOD ID 資訊，讓這位使用者可以再使用別的帳號做綁定，請先將接續訂單取消，並無重複訂閱的情形"><i class="fa" />解除 MOD ID 綁定 </b-button>
            <hr />
          </div>

          <b-button @click="onUserReset($route.params.id)" variant="danger"
            v-b-tooltip.hover title="刪除帳號 refresh_tokens tokens 及所有訂單，限管理者權限">
            UserReset</b-button>

          <div style="width: 270px; padding-top: 30px;">
            <b-button @click="onDeleteUser(item.id, $event)" variant="danger"
              v-b-tooltip.hover title="刪除使用者">
              <i class="fa" />刪除使用者</b-button>
          </div>


        </b-card>
      </b-card-group>
      <b-card v-if="isAdmin == true">
        <div class="mt-2">修改CMS權限，勾選即增加，取消勾選即取消權限</div>
            <b-form-checkbox
              v-for="value in consoleRoles"
              v-model="value.checked"
              @change="setRole($event, value.value)">
              {{ value.value }}
            </b-form-checkbox>
      </b-card>
      <b-alert :show="dismissCountDown"
        dismissible
        variant="warning"
        @dismissed="dismissCountDown=0"
        @dismiss-count-down="countDownChanged">
        {{ alertMsg }}
      </b-alert>
    </b-col>
    <b-modal v-model="showModal" ref="formModal" @ok="onModalOK" title="user 資訊" size="xl" ok-only>
      <b-form-textarea
      id="textarea"
      v-model="userinfoStr"
      placeholder="the userinfo response api data"
      rows="25">
      </b-form-textarea>
    </b-modal>
  </b-row>
</template>

<script>
import api from '@/api'
export default {
  name: 'User',
  props: {
    caption: {
      type: String,
      default: 'User id'
    },
  },
  data: () => {
    return {
      items: [],
      item: {mediaSource:{}},
      dismissCountDown: 0,
      alertMsg: '',
      receiptMsg: '',
      reasonInfoState: null,
      reasonPaymentState: null,
      reasonCarrierTypeState: null,
      expired_at: null,
      reason_info: '',
      reason_payment: '',
      showModal: false,
      userinfoStr: '',
      tmpUser: {
        membership : [],
        email: null,
        phone: null,
        recipient_address: null,
        name: null,
        carrier_type: null,
        carrier_value: null
      },
      autorenewOptions: [
        { value: true, text: 'True' },
        { value: false, text: 'False' },
      ],
      membershipOptions: [
        { value: "freetrial", text: "Free Trial" },
        { value: "expired", text: "Expired" },
        { value: "premium", text: "Premium" },
        { value: "prime", text: "Prime" },
        { value: "pr", text: "PR" },
        { value: "paid:anime", text: "AnimePass" }
      ],
      config: {
          enableTime: true,
          altFormat: 'Y-m-d H:i',
          altInput: true,
          dateFormat: 'Y-m-d H:i'
      },
      kkbox: '',
      familyID: '',
      fields: [
        {key: 'key'},
        {key: 'value'},
      ],
      isAdmin: false,
      consoleRoles: [],
      userEmail: '',
    }
  },
  mounted () {
    this.onFetch()
  },
  computed: {
    filterItems() {
      let ignoreFields = ['phone_verified_at', 'email_verified_at']
      return this.items.filter(item => {
        return !ignoreFields.includes(item.key)
      })
    }
  },
  methods: {
    goBack() {
      this.$router.go(-1)
      // this.$router.replace({path: '/users'})
    },
    onKKBOX (sub) {
      if ( sub !== '') {
        window.open('/v3/primestatus?sub=' + encodeURIComponent(sub))
      }
    },
    onMOD (modID) {
      if ( modID !== '') {
        this.$router.push({ path: '/user/mod', query: {q: modID} });
      }
    },
    onOrder(user_id) {
      this.$router.push({ path: '/user/order', query: {q: user_id} });
    },
    onFamily(user_id) {
      this.$router.push({ path: '/user/family', query: {q: user_id} });
    },
    onToken(user_id) {
      this.$router.push({ path: '/user/token', query: {q: user_id} });
    },
    onFavorite(user_id) {
      this.$router.push({ path: '/user/favorite/' + user_id});
    },
    onWatchHistory(user_id) {
      this.$router.push({ path: '/user/watch_history/' + user_id});
    },
    onAmplitude(user_id) {
      window.open('https://analytics.amplitude.com/kktv/project/148242/search/' + user_id)
    },
    onAuditLog(user_id) {
      this.$router.push({ path: '/user/audit_log/' + user_id});
    },
    countDownChanged (dismissCountDown) {
      this.dismissCountDown = dismissCountDown
    },
    onUserInfo() {
      const id = this.$route.params.id
      api.request('get', '/v3/console/userinfo?q='+id)
        .then((response) => {
          if (response.data) {
            this.userinfoStr = JSON.stringify(response.data, null, 2)
          }
        })
      this.showModal = true
    },
    onUserReceipt() {
      const id = this.$route.params.id
      var url = `/v3/console/user_receipt?id=${id}&payment_type=${this.item.payment_type}`
      console.log(url)
      api.request('get', url)
        .then((response) => {
          if (response.data) {
            console.log(response.data)
            this.userinfoStr = JSON.stringify(response.data, null, 2)
          }
        })
        .catch(error => {
          this.userinfoStr = JSON.stringify(error.response.data, null, 2)
        })
      this.showModal = true
    },
    onModalOK() {
      this.userinfoStr = ''
    },
    onUserReset() {
      const id = this.$route.params.id
      api.request('put', '/v3/console/userreset?q='+id)
        .then((response) => {
          this.alertMsg = "重置成功"
          this.showAlert()
        }).catch(error => {
          this.alertMsg = "重置失敗"
          this.showAlert()
        })
    },
    showAlert () {
      this.dismissCountDown = 5
    },
    onTimeChange () {
      var timeStr = this.tmpUser.expiredAt
      this.$forceUpdate()
    },
    
    onFetch() {
      console.log('onFetch')
      const id = this.$route.params.id
      api.request('get', '/v3/console/user/'+id)
        .then((response) => {
          if (response.data && response.data.data) {
            const user = response.data.data
            this.item = user
            if (user.mediaSource) {
              var u = JSON.parse(user.mediaSource)
              if (u && u.kkbox && u.kkbox.sub) {
                this.kkbox = u.kkbox.sub
              }
            }
            else {
              this.kkbox = ''
            }

            if (u && u.family) {
              this.familyID = user.id
            } else if ( user.family_id) {
              this.familyID = user.family_id
            }
            const userDetails = user ? Object.entries(user) : [['id', 'Not found']]
            this.items = userDetails.map(
              ([key, value]) => {
                if (key === 'expiredAt') {
                  value = new Date(value * 1000).toLocaleString()
                }
                if (key === 'createdAt') {
                  value = new Date(value).toLocaleString()
                }
                if (key === 'revoked_at') {
                  if (user.revoked_at){
                    var revoked_at = Date.parse(user.revoked_at);
                    value = new Date(revoked_at).toLocaleString()
                  }
                }
                return {key: key, value: value}
              }
            )
            this.expired_at = user.expiredAt * 1000
            this.tmpUser.membership = user.membership
            this.tmpUser.auto_renew = user.auto_renew
            this.tmpUser.email = user.email
            this.tmpUser.phone = user.phone
            this.tmpUser.name = user.name
            this.tmpUser.recipient_address = user.recipient_address
            this.tmpUser.carrier_type = user.carrier_type
            this.tmpUser.carrier_value = user.carrier_value
            this.reason_info = ''
            this.reason_payment = ''
            this.userEmail = user.email
          }
          this.onConsoleAdjust()
        })
    },
    resetFormStates() {
      this.reasonInfoState = null
      this.reasonPaymentState = null
      this.reasonCarrierTypeState = null
    },
    onDeleteUser(userid, event) {
      var confirm = window.confirm(
        "確定刪除此筆使用者？ (User ID: " + userid + ")\n\n" +
        "此刪除動作會執行以下動作：\n" +
        "1. 將此帳號標記為刪除(寫入 revoked_at 時間)\n" +
        "2. 通知 KKID 將此帳號綁定的 KKID 註銷\n\n" +
        "**重要提醒**：\n" +
        "1. 註銷 KKID 後使用者若想用同一組 email/phone 登入 KKTV，必須用同一組 email/phone 重新註冊 KKID。\n" +
        "2. 註銷 KKID 後即使重新用相同 email/phone 註冊所取得的 KKID 識別碼「不會」與原本被註銷的 KKID 識別碼相同。"
      )

      if (confirm) {
        this.resetFormStates()
        this.setBtnLoadingStyle(event)

        if (!this.reason_info) {
          alert("請填寫異動原因")
          this.reasonInfoState = 'invalid'
          this.setBtnErrorStyle(event)
          return
        }

        if (this.item.revoked_at) {
          alert('該帳號原已為刪除狀態')
          this.setBtnErrorStyle(event)
          return
        }


        console.log("media_source: ", this.item.mediaSource)
        var media_source
        if (this.item.mediaSource) {
          media_source = JSON.parse(this.item.mediaSource)
        }

        if (this.item.role === 'premium') {
          alert('VIP 帳號不能刪除')
          this.setBtnErrorStyle(event)
          return
        }

        var url = `/v3/console/user/${userid}?reason=${this.reason_info}`
        api.request('delete', url)
          .then((response) => {
            this.alertMsg = "刪除成功"
            this.setBtnSuccessStyle(event)
            this.showAlert()
            this.$forceUpdate()

          }).catch(error => {
            this.setBtnErrorStyle(event)

            console.log("response: ", error.response)

            if (error.response && error.response.data && error.response.data.status && error.response.data.status.message) {
              var msg = ""
              switch ( error.response.data.status.subtype ) {
                case '403.1':
                  msg = "VIP 帳號不能刪除"
                  break
                case '400.1':
                  msg = "該帳號原已為刪除狀態"
                  break
                case '400.3':
                  msg = "請填寫異動原因"
                  break
                case '500.1':
                  msg = "發生未知錯誤，請聯絡開發人員"
                  break
              }
              this.alertMsg = "刪除失敗: " + msg
            } else {
              this.alertMsg = "您的權限不足"
            }
            this.showAlert()
          }).finally( () => {
            setTimeout(() => {
              this.onFetch()
            }, 2000);
          })
      }
    },
    onUpdate (userid, event, updateType = null) {
      this.resetFormStates()
      var btn = event.target
      var i = event.target.firstChild
      
      var putUrl

      this.setBtnLoadingStyle(event)
      updateType = updateType ? updateType : 'info'
      var payload = this.tmpUser
      switch ( updateType ) {
        case 'info':
          // check if reason is not entered
          if (!this.reason_info) {
            this.reasonInfoState = 'invalid'
            this.setBtnErrorStyle(event)
            return
          }
          this.tmpUser.reason = this.reason_info
          putUrl = `/v3/console/V2user/${userid}`
          
          this.tmpUser.membership = this.item.membership
          if (!this.tmpUser.email) {
            this.tmpUser.email = null
          }
          if (!this.tmpUser.phone) {
            this.tmpUser.phone = null
          }

          // pass utc datetime to api
          
          var date = new Date(this.expired_at);
          var s = date.toISOString().split('T')
          var expiredAt = s[0] + ' ' + s[1].substring(0, 5)
          this.tmpUser.expired_at = expiredAt

          break
        case 'payment':
          if (this.tmpUser.carrier_type || this.tmpUser.carrier_value) {
            
            var carrier_type = this.tmpUser.carrier_type
            if (! ["0", "1", "2"].includes(carrier_type)) {
              this.reasonCarrierTypeState = 'invalid'
              this.setBtnErrorStyle(event)
              return
            }

            if (!this.tmpUser.carrier_value) {
              this.reasonCarrierTypeState = 'invalid'
              this.setBtnErrorStyle(event)
              return
            }
          }

          if (!this.tmpUser.carrier_type) {
            this.tmpUser.carrier_type = null
          }
          if (!this.tmpUser.carrier_value) {
            this.tmpUser.carrier_value = null
          }

          // check if reason is not entered
          if (!this.reason_payment) {
            this.reasonPaymentState = 'invalid'
            this.setBtnErrorStyle(event)
            return
          }
          if (!this.tmpUser.recipient_address) {
            this.tmpUser.recipient_address = null
          }
          payload = {
            'recipient_address' : this.tmpUser.recipient_address,
            'carrier_type' : this.tmpUser.carrier_type,
            'carrier_value' : this.tmpUser.carrier_value,
            'reason' : this.reason_payment
          }
        
          putUrl = `/v3/console/user/${userid}/paymentinfo`
          
          break
        case 'unbind':
          putUrl = `/v3/console/user/${userid}?type=unbind`
          break
        case 'unbindMOD':
          putUrl = `/v3/console/user/${userid}?type=unbindMOD`
          break
        case 'edmUnsubscribe':
          putUrl = `/v3/console/user/${userid}?type=edmUnsubscribe`
          break
      }
      console.log(putUrl)
      api.request('put', putUrl, payload)
        .then((response) => {
          this.setBtnSuccessStyle(event)
          this.alertMsg = "更新成功"
          this.showAlert()
          this.$forceUpdate()
        }).catch(error => {
          this.setBtnErrorStyle(event)
          if (error.response && error.response.data && error.response.data.status && error.response.data.status.message) {
            this.alertMsg = "更新失敗: " + error.response.data.status.message
          } else {
            this.alertMsg = "您的權限不足"
          }
          this.showAlert()
        }).finally( () => {
          setTimeout(() => {
            this.setBtnWarningStyle(event)
            this.onFetch()
          }, 2000);
        })
    },
    resetBtnStyle(event) {
      var btn = event.target
      var i = event.target.firstChild
      btn.classList.remove('btn-warning', 'btn-danger', 'btn-success')
      i.classList.remove('fa-warning', 'fa-check')
      i.classList.remove('fa-refresh', 'fa-spin')
    },
    setBtnLoadingStyle(event) {
      this.resetBtnStyle(event)
      var btn = event.target
      var i = event.target.firstChild
      i.classList.add('fa-refresh')
      i.classList.add('fa-spin')
    },
    setBtnWarningStyle(event) {
      this.resetBtnStyle(event)
      var btn = event.target
      var i = event.target.firstChild
      btn.classList.add('btn-warning')
      i.classList.remove('fa-warning', 'fa-check')
      i.classList.remove('fa-refresh', 'fa-spin')
    },
    setBtnErrorStyle(event) {
      this.resetBtnStyle(event)
      var btn = event.target
      var i = event.target.firstChild
      btn.classList.add('btn-danger')
      i.classList.add('fa-warning')
      i.classList.remove('fa-refresh', 'fa-spin')
    },
    setBtnSuccessStyle(event) {
      this.resetBtnStyle(event)
      var btn = event.target
      var i = event.target.firstChild
      btn.classList.add('btn-success')
      i.classList.add('fa-check')
      i.classList.remove('fa-refresh', 'fa-spin')
    },
    async onConsoleAdjust() {
      const email = this.userEmail
      this.isAdmin = JSON.parse(localStorage.user).roles === 'admin'
      if (this.isAdmin === false) {
        return
      }

      const [consoleRolesResponse, userHaveRolesResponse] = await Promise.all([
        api.request('get', '/v3/console/roles'),
        api.request('get', '/v3/console/user/roles?email=' + email)
      ])

      const userHaveRoles = userHaveRolesResponse.data.data
      consoleRolesResponse.data.data.map((role) => {
        this.consoleRoles.push({
          value: role.name,
          key: role.name,
          checked: userHaveRoles && userHaveRoles.includes(role.name),
        })
      })
    },
    setRole(checked, value) {
      const email = this.userEmail
      const data = {
        roles: [value],
        email: email
      }
      const url = '/v3/console/user/roles'
      let method = checked ? 'post' : 'delete'
      api.request(method, url, data).then(() => {
        this.alertMsg = "更新成功"
        this.showAlert()
      }).catch(error => {
        if (error.response && error.response.data && error.response.data.status && error.response.data.status.message) {
          this.alertMsg = "更新失敗: " + error.response.data.status.message
        } else {
          this.alertMsg = "您的權限不足"
        }
        this.showAlert()
      })
    },
  }
}
</script>
