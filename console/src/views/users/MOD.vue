<template>
  <b-row>
    <b-col cols="12" xl="12">
      <transition name="slide">
      <b-card no-header>
        <template slot="header">
          MOD subscriber ID:  {{ q }}
        </template>

          <b-row>
            <b-col sm="12">
            <form v-on:submit.prevent="onFetch">
            <b-form-group description="">
              <b-input-group>
                <b-form-input type="text" id="name" v-model="q"></b-form-input>
                <!-- Attach Right button -->
                <b-input-group-append>
                  <b-button variant="primary" @click="onFetch" >
                    <i class="fa" :class="{'fa-refresh': loading, 'fa-spin': loading}"></i>
                    Submit
                    </b-button>
                </b-input-group-append>
            <b-alert :show="dismissCountDown"
                  dismissible
                  variant="warning"
                  @dismissed="dismissCountDown=0"
                  @dismiss-count-down="countDownChanged">
            這位大大，您的權限不足
            </b-alert>
              </b-input-group>
            </b-form-group>

            <small v-html="'請輸入 MOD ID 查詢 <span style=\'font-weight: bold; color: #e67300;\'>※ 這裡的資料是透過 API 去「中華電信的 SMS 系統」查詢，呈現的資料是用戶目前在中華電信的訂閱狀況，供同仁排除問題時對照使用。</span>'"></small>
            </form>

            </b-col>
          </b-row>

          <table aria-colcount="1" class="table b-table table-striped">
            <thead class="">
              <tr>
                <th aria-colindex="1" class="">MOD ID</th>
                <th>區域 (北/中/南)</th>
                <th>訂購方案 (KKTV)</th>
                <th>方案代碼</th>
                <th>服務代碼</th>
                <th>價格</th>
                <th>起訂日</th>
                <th>結束日</th>
                <th>綁定的 userID (KKTV)</th>
              </tr>
            </thead>
            <tbody class="">
                <tr>
                  <td> {{ subscriber_id }} </td>
                  <td> {{ area }} </td>
                  <td> {{ subscriber.ProductName }} </td>
                  <td> {{ subscriber.ItemID }} </td>
                  <td> {{ subscriber.MType }} </td>
                  <td> {{ subscriber.Price }} </td>
                  <td> {{ subscriber.StartTime }} </td>
                  <td> {{ subscriber.EndTime }} </td>
                  <td @click="click2User(subscriber.UserID)"> {{ subscriber.UserID }} </td>
                </tr>
            </tbody>
          </table>
      </b-card>
      </transition>
    </b-col>
  </b-row>
</template>

<script>
import { mapState } from 'vuex'
import api from '@/api'

export default {
  name: 'MOD',
  props: {
    caption: {
      type: String,
      default: 'MOD'
    },
    hover: {
      type: Boolean,
      default: true
    },
    striped: {
      type: Boolean,
      default: true
    },
    bordered: {
      type: Boolean,
      default: false
    },
    small: {
      type: Boolean,
      default: false
    },
    fixed: {
      type: Boolean,
      default: false
    }
  },
  data: () => {
    return {
      area: '',
      subscriber_id:'',
      subscriber: {},
      alertMsg: '',
      dismissCountDown: 0,
      q: '',
      fields: [
        {key: 'subscriber_id'},
        {key: 'subscriber_area'}
      ],
      currentPage: 1,
      // perPage: 5,
      perPage: 0,
      totalRows: 0
    }
  },
  computed: {
    ...mapState(
      ['loading']
    )
  },
  mounted () {
    if (this.$route.query.q) {
      this.q = this.$route.query.q
      this.onFetch()
    }
  },
  methods: {
    getBadge (status) {
      return status === 'Active' ? 'success'
        : status === 'Inactive' ? 'secondary'
          : status === 'Pending' ? 'warning'
            : status === 'Banned' ? 'danger' : 'primary'
    },
    countDownChanged (dismissCountDown) {
      this.dismissCountDown = dismissCountDown
    },
    showAlert () {
      this.dismissCountDown = 5
    },
    userAgentHint(ug) {
      var hint = ''
      if (ug.startsWith('com.kktv.ios.kktv') || ug.includes('iPhone')) {
        hint = 'fa-apple'
      } else if (ug.startsWith('com.kktv.kktv') || ug.includes('Adnroid')) {
        hint = 'fa-android'
      } else if (ug.startsWith('axios')) {
        hint = 'fa-desktop'
      } else if (ug.includes('Macintosh')) {
        hint = 'fa-desktop'
      } else if (ug.includes('Windows')) {
        hint = 'fa-desktop'
      } else if (ug.includes('Linux')) {
        hint = 'fa-linux'
      } else {
        hint = 'fa-question'
      }
      return hint
    },
    getRowCount (items) {
      return items.length
    },
    click2User (id) {
      this.$router.push({path: `/user/${id.toString()}`})
    },
    onFetch () {
      this.$router.push({ path: '/user/mod', query: {q: this.q} });
      api.request('get', '/v3/console/modorderstate?q='+this.q)
        .then((response) => {
          if (response.data && response.data.data) {
            this.subscriber_id = response.data.data.subscriberID
            if (response.data.data.subscriberArea === 'None') {
                this.area = '帳號不存在，非訂戶'
            } else {
              this.area = response.data.data.subscriberArea
            }
            this.subscriber = response.data.data.subscriberInfo
            if (response.data.data.subscriberInfo.ItemID === '') {
                this.subscriber.ItemID = '未訂閱方案'
            }
            if (response.data.data.subscriberInfo.UserID === '') {
                this.subscriber.UserID = '未綁定'
            }
          }
        })
    }

  }
}
</script>

<style scoped>
.card-body >>> table > tbody > tr > td {
  cursor: pointer;
}
</style>
