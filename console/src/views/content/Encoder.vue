<template>
  <b-row>
    <b-col cols="12" xl="12">
      <transition name="slide">
      <b-card :header="caption">
          <b-row>
            <b-col sm="12">
            <form v-on:submit.prevent="onFetch">
            <b-form-group description="輸入 EpisodID">
              <b-input-group>
                <b-form-input type="text" id="name" v-model="q"></b-form-input>
                <!-- Attach Right button -->
                <b-input-group-append>
                  <b-button variant="primary" @click="onFetch" >
                    <i class="fa" :class="{'fa-refresh': loading, 'fa-spin': loading}"></i>
                    Submit
                    </b-button>
                </b-input-group-append>
              </b-input-group>
            </b-form-group>
            </form>

            </b-col>
          </b-row>

        <b-table v-if="items" :hover="hover" :sort-by="'StartTimestamp'" :sort-desc="true" :striped="striped" :bordered="bordered" :small="small" :fixed="fixed" responsive="sm" :items="filterItems" :fields="fields" :current-page="currentPage" :per-page="perPage">
          <template slot="StartTimestamp" slot-scope="data">
            {{ new Date(data.item.StartTimestamp).toLocaleString() }}
          </template>
          <template slot="episodid" slot-scope="data">
            <strong>{{data.item.episodid}}</strong>
          </template>
          <template slot="dispatch" slot-scope="data">
            <b-badge :variant="getBadge(data.item.dispatch[1])">{{data.item.dispatch[1]}}</b-badge>
          </template>
          <template slot="Transcode" slot-scope="data">
            <b-badge :variant="getBadge(data.item.Transcode[1])">{{data.item.Transcode[1]}}</b-badge>
            <br />
            Transcode (<strong>{{ data.item.Transcode[2] }}</strong>)
          </template>
          <template slot="thumbnail" slot-scope="data">
            <b-badge v-if="data.item.thumbnail" :variant="getBadge(data.item.thumbnail[1])">{{data.item.thumbnail[1]}}</b-badge>
          </template>

          <template slot="Encryption" slot-scope="data">
            <b-badge v-if="data.item.Encryption" :variant="getBadge(data.item.Encryption[1])">{{data.item.Encryption[1]}}</b-badge>
          </template>

          <template slot="Adaptive" slot-scope="data">
            <b-badge v-if="data.item.Adaptive" :variant="getBadge(data.item.Adaptive[1])">{{data.item.Adaptive[1]}}</b-badge>
          </template>
          <template slot="Cleanup" slot-scope="data">
            <span v-if="data.item.Cleanup">
            <b-badge :variant="getBadge(data.item.Cleanup[1])">{{data.item.Cleanup[1]}}</b-badge>
            <br />
            <strong>{{ new Date(data.item.Cleanup[0]).toLocaleString() }} </strong>
            </span>
          </template>
        </b-table>
        <!-- <nav>
          <b-pagination size="sm" :total-rows="getRowCount(items)" :per-page="perPage" v-model="currentPage" prev-text="Prev" next-text="Next" hide-goto-end-buttons/>
        </nav> -->
      </b-card>
      </transition>
    </b-col>
  </b-row>
</template>
<script>
import { mapState } from 'vuex'
import api from '@/api'

export default {
  name: 'Encoder',
  props: {
    caption: {
      type: String,
      default: 'Encoding Status'
    },
    hover: {
      type: Boolean,
      default: true
    },
    striped: {
      type: Boolean,
      default: true
    },
    bordered: {
      type: Boolean,
      default: false
    },
    small: {
      type: Boolean,
      default: false
    },
    fixed: {
      type: Boolean,
      default: false
    }
  },
  data: () => {
    return {
      itemMap: {},
      items: [],
      q: '',
      fields: [
        {key: 'StartTimestamp', sortable: true},
        {key: 'episodid'},
        {key: 'dispatch'},
        {key: 'Transcode'},
        {key: 'thumbnail'},
        {key: 'Encryption'},
        {key: 'Adaptive'},
        {key: 'Cleanup'}
      ],
      currentPage: 1,
      // perPage: 5,
      perPage: 0,
      totalRows: 0
    }
  },
  mounted () {
      this.onFetch()
  },
  computed: {
    filterItems () {
      if (this.q === '') {
        return this.items
      } else {
        var qs = this.q
        return this.items.filter(function (item) {
          return item.episodid.includes(qs)
        })
      }
    },
    ...mapState(
      ['loading']
    )
  },
  methods: {
    getBadge (status) {
      var css
      switch (status) {
        case 'COMPLETED':
        case 'CLOSED':
          css = 'success'
          break
        case "FAILED":
          css = 'danger'
          break
        case "OPEN":
          css = 'warning'
          break
      }
      return css
    },
    getRowCount (items) {
      return items.length
    },
    userLink (id) {
      return `user/${id.toString()}`
    },
    rowClicked (item) {
      const userLink = this.userLink(item.id)
      this.$router.push({path: userLink})
    },
    checkTargetStage (stage) {
      if (stage.startsWith('dispatch') ||
          stage.startsWith('thumbnail') ||
          stage.startsWith('CENC') ||
          stage.startsWith('Adaptive') ||
          stage.startsWith('Cleanup')
      ) {
        return true
      } else {
        return false
      }
    },
    cookItem () {
      // turn object to an Array
      this.items = []
      for (var key in this.itemMap) {
        var item = {}
        item['episodid'] = key
        item['jobs'] = this.itemMap[key]
        item['']
        var transcodeParent = {}
        var transcodeStr = ''
        var transcodeJobs = []
        var transcodeClose = 0

        for(var i=0; i < item['jobs'].length; i++) {
          var job = item['jobs'][i]
          if (i === 0) {
            item['StartTimestamp'] = job['StartTimestamp']
          }
          var stage = job['Execution']['WorkflowId'].split('-')[0]
          if (this.checkTargetStage(stage)) {

              item[stage] = [job['StartTimestamp'], job['ExecutionStatus']]
              if (job['CloseStatus']) {
                item[stage] = [job['StartTimestamp'], job['CloseStatus']]
              }
          }
          if (stage == 'Transcode') {
            transcodeParent = job
          }
          if (job['Execution']['WorkflowId'].startsWith('Transcode p')) {
            if (job['ExecutionStatus'] && job['ExecutionStatus'] === 'CLOSED') {
              transcodeClose += 1
            }
            transcodeJobs.push(job)
          }
        } // for jobs

        // mirror 'CENC Encryption' to 'Encryption'
        if (item['CENC Encryption']) {
          item['Encryption'] =  item['CENC Encryption']
        }

        if (transcodeJobs.length > 0 && transcodeParent) {
          transcodeStr = transcodeClose + '/' + transcodeJobs.length
          item['Transcode'] = [transcodeParent['StartTimestamp'], transcodeParent['ExecutionStatus'], transcodeStr]
          if (transcodeParent['ExecutionStatus'] !== 'CLOSED') {
            item['_rowVariant'] = 'danger'
          }
        } else {
          item['Transcode'] = [,,]
        }

        if (!item['Encryption'] || !item['Transcode'] ||
          !item['thumbnail'] || !item['Encryption'] || !item['Adaptive'] || !item['Cleanup']) {
            item['_rowVariant'] = 'warning'
        }

        this.items.push(item)

      } // for key
    },
    onFetch () {
      api.request('get', '/v3/console/encoder')
        .then((response) => {
          if (response.data && response.data.data &&
          response.data.data.jobs) {
            this.itemMap = response.data.data.jobs
            this.cookItem()
          }
        })
    }

  }
}
</script>

<style scoped>
.card-body >>> table > tbody > tr > td {
  cursor: pointer;
}
</style>
