<template>
  <b-row>
    <b-col cols="12" xl="12">
      <transition name="slide">
        <b-card :header="listTypeStr">
          <form v-on:submit.prevent="onFetch">
            <b-row>
              <b-col sm="5">
                <b-form-group description="輸入時間可以過濾出該時間點看得到的片單">
                    <flat-pickr v-model="displayDate" :config="flatpickrConfig">
                    </flat-pickr>
                  </b-form-group>
              </b-col>
              <b-col sm="7">
                  <b-form-group description="輸入字串可以過濾出 Topic 或 Caption 符合的片單">
                    <b-input-group>
                      <b-form-input type="text" id="name" v-model="q"></b-form-input>
                      <b-input-group-append>
                        <b-button variant="primary" @click="onModalShow({'meta':{'title_id':[]}})">新增</b-button>
                      </b-input-group-append>
                    </b-input-group>
                  </b-form-group>
              </b-col>
            </b-row>
            <b-row>
              <b-col sm="12">
                <b-form-checkbox v-model="showAll" value="checked" unchecked-value="false" inline>顯示全部</b-form-checkbox>
                <b-form-select v-if="listTypeStr != 'highlight'" v-model="selectedCollection" :options="[{value:'', text: '請選擇分類'}].concat(collectionOptions)">選擇分類</b-form-select>
                <b-alert :show="dismissCountDown"
                      dismissible
                      :variant="alertCss"
                      @dismissed="dismissCountDown=0"
                      @dismiss-count-down="countDownChanged">
                      {{ alertMsg }}
                </b-alert>
              </b-col>
            </b-row>
          </form>
              <!-- default titlelist setting -->
          <b-form-group description="修改預設片單的id">
            <b-input-group>
              <b-form-input type="text" id="default-titlelist" v-model="titlelistDefault"></b-form-input>
              <b-input-group-append>
                <b-button variant="primary" @click="onStoreDefaultTitlelist()">儲存</b-button>
              </b-input-group-append>
            </b-input-group>
          </b-form-group>
              <!-- end  -->
          <small v-html="'預設只顯示啟用中的片單，要顯示所有片單，請勾 <span style=\'font-weight: bold;color: #e67300;\'>顯示全部</span>'"></small>

          <b-table v-if="items" :hover="hover" :striped="striped" :bordered="bordered" :small="small" :fixed="fixed" responsive="sm"
            :items="filterItems" :fields="fields" :current-page="currentPage" :per-page="perPage" style="overflow-x:auto;">
            <template slot="action" slot-scope="data">
              <b-button @click="onModalShow(data.item)"><i class="fa fa-edit"></i></b-button>&nbsp;
              <b-button @click="onDelete(data.item)"><i class="fa fa-trash"></i></b-button>
            </template>
            <template slot="enabled" slot-scope="data">
              <b-badge :variant="getBadge(data.item.enabled)">{{data.item.enabled}}</b-badge>
            </template>
            <template slot="duration" slot-scope="data">
              <span style="white-space: nowrap;">
                {{ getLocalTime(data.item.visible_since) }} ～<br />
                {{ getLocalTime(data.item.visible_until) }}
              </span>
            </template>
            <template slot="image" slot-scope="data">
              <b-img :src="data.item.image" width="100" alt=""></b-img>
            </template>
            <template slot="topic" slot-scope="data">
              <span style="white-space: nowrap;">
                {{data.item.topic}}<br />
                <b>{{data.item.title}}</b><br />
                {{data.item.summary}} <br />
              </span>
            </template>
            <template slot="title" slot-scope="data">
              <span v-if="data.item.list_type === 'title'">
                {{data.item.title_id}} <br />
                <b>{{data.item.title_name}}</b><br /><br />
                Trailer: <br />
                &nbsp;&nbsp;自動播放： <b-badge :variant="getBadge(data.item.trailer_autoplay_enabled)">{{data.item.trailer_autoplay_enabled}}</b-badge><br />
                &nbsp;&nbsp;指定預告: {{ data.item.trailer_episode_id ? data.item.trailer_episode_id : '(隨機播放)' }}
              </span>
              <span v-if="data.item.list_type === 'link'">
                App: <a :href="data.item.uri">{{data.item.uri}}</a><br />
                Web: <a :href="data.item.url">{{data.item.url}}</a>
              </span>
            </template>

            <template slot="collection" slot-scope="data">
              <span>
                {{ data.item.meta.collections }}
              </span>
            </template>
          </b-table>
        </b-card>
      </transition>
    </b-col>
    <b-modal size="xl" v-model="showModal" :title="detailTitle"
      ref="formModal"
      @ok="onOK" @cancel="onCancel"
      no-close-on-backdrop no-close-on-esc>
      <form ref="form" @submit.stop.prevent="handleSubmit">
        <!-- start fields for general purpose -->

        <b-form-group v-if="['link', 'title', 'ranking', 'choice'].includes(listTypeStr.split(',')[0])" label="所屬分類" description="選擇這個片單要出現的分類">
        <b-form-select v-model="detailObj.meta.collections" :options="collectionOptions" :select-size="3" multiple></b-form-select>
        </b-form-group>

        <b-form-group>
          <b-form-checkbox v-model="detailObj.enabled">啟用</b-form-checkbox>
        </b-form-group>
        <b-form-group label="Order" description="輪播的順序，數字越小排越前面"
          :state="orderState"
          invalid-feedback="Must greater than 0"
        >
          <b-form-input v-model="detailObj.order" type="number" min="1"
            :state="orderState" required
          ></b-form-input>
        </b-form-group>
        <b-form-group v-if="['link', 'title'].includes(detailObj.list_type)" label="Topic" description="主題文字" >
          <b-form-input v-model="detailObj.topic" type="text"></b-form-input>
        </b-form-group>
        <b-form-group label="Caption" description="標題 字數限制在15字內"
          :state="captionState"
          invalid-feedback="Can't be blank"
        >
          <b-form-input v-model="detailObj.title" type="text"
            :state="captionState" required
          ></b-form-input>
        </b-form-group>
        <b-form-group v-if="['link', 'title'].includes(detailObj.list_type)" label="Summary" description="說明文字">
          <b-form-input v-model="detailObj.summary" type="text"></b-form-input>
        </b-form-group>

        <b-row>
          <b-col sm="6">
            <b-form-group label="Visible Since" description="上架時間">
              <flat-pickr v-model="sinceDate" :config="flatpickrConfig">
              </flat-pickr>
            </b-form-group>
          </b-col>
          <b-col sm="6">
            <b-form-group label="Visible Until" description="下架時間">
              <flat-pickr v-model="untilDate" :config="flatpickrConfig">
              </flat-pickr>
            </b-form-group>
          </b-col>
        </b-row>

        <b-form-group v-if="['link', 'title', 'highlight'].includes(detailObj.list_type)" label="Image" description="顯示的圖片"
          :state="imageState"
          invalid-feedback="Can't be blank"
        >
          <b-form-file v-model="image" ref="image-input" accept="image/jpeg"
            :state="imageState"
          ></b-form-file>
        </b-form-group>
        <b-form-group v-if="['link', 'title', 'highlight'].includes(detailObj.list_type)" label="預覽圖片" :description="detailObj.source_image">
          <b-img v-if="detailObj.image" :src="detailObj.image" width="150" thumbnail></b-img>
        </b-form-group>
        <b-form-group v-if="['link', 'title', 'highlight'].includes(detailObj.list_type)" label="主色色碼" description="圖片上傳的時候會自動計算，如果要自行定義，請再上傳圖片後，自行更新色碼後，再儲存一次，已覆蓋原先上傳圖片自動計算的色碼"
          :state="colorState"
          invalid-feedback="color code invalid">
          <b-form-input v-model="detailObj.dominant_color" :state="colorState"></b-form-input>
        </b-form-group>
        <b-form-group label="Type" description="類型"
          :state="typeState"
          invalid-feedback="Can't be blank"
        >
          <b-form-select v-model="detailObj.list_type" :options="supportTypeOptions"
            :state="typeState" required
          ></b-form-select>
        </b-form-group>
        <!-- end -->

        <!-- start share titlelist-->
        <b-form-group v-if="['choice', 'highlight'].includes(detailObj.list_type)" label="Share Link" description="分享連結">
          <div class="input-group mb-3">
            <div class="input-group-prepend">
              <span class="input-group-text" id="baseShareLink">{{ shareLinkBaseUrl }}</span>
            </div>
            <input type="text" v-model="detailObj.meta.share_id" class="form-control">
            <b-button v-if="this.detailTitle !== '新增片單'" variant="primary" @click="onStoreTitleShareId()">儲存</b-button>
          </div>
        </b-form-group>


        <b-form-group v-if="['choice'].includes(detailObj.list_type)" label="設定為釘選" description="標記新劇跟播片單或全集新上架或保證曝光">
          <b-form-select v-model="detailObj.meta.pinned" :options="pinnedOptions"></b-form-select>
        </b-form-group>

        <!-- end -->
        <!-- titlelist background image  -->
        <b-form-group v-if="['choice', 'highlight'].includes(detailObj.list_type)" label="片單圖" description="片單獨立頁顯示的圖片，限制比例為16:9，尺寸為1920X1080px，檔案大小限制為2mb"
                      :state="titlelistDetailImageState"
                      :invalid-feedback="detailImageFeedback"
        >
          <b-form-file v-model="backgroundImage" ref="image-input" accept="image/jpeg"
                       :state="titlelistDetailImageState"
          ></b-form-file>
        </b-form-group>
        <b-form-group v-if="['choice', 'highlight'].includes(detailObj.list_type)" label="預覽圖片" :description="detailObj.meta.background_image_url">
          <b-img v-if="detailObj.meta.background_image_url" :src="detailObj.meta.background_image_url" width="150" thumbnail></b-img>
        </b-form-group>
        <!-- end -->
        <!-- titlelist og image -->
        <b-form-group v-if="['choice', 'highlight'].includes(detailObj.list_type)" label="og圖" description="og顯示的圖片，限制尺寸為1200X630px，檔案限制大小為2mb"
                      :state="ogImageState"
                      :invalid-feedback="ogFeedback"
        >
          <b-form-file v-model="ogImage" ref="image-input" accept="image/jpeg"
                       :state="ogImageState"
          ></b-form-file>
        </b-form-group>
        <b-form-group v-if="['choice', 'highlight'].includes(detailObj.list_type)" label="預覽圖片" :description="detailObj.meta.og_image">
          <b-img v-if="detailObj.meta.og_image" :src="detailObj.meta.og_image" width="150" thumbnail></b-img>
        </b-form-group>
        <!-- end -->
        <b-form-group v-if="['choice', 'highlight'].includes(detailObj.list_type)" label="Description" description="片單簡介,42字以內"
        :state="descriptionState" invalid-feedback="Description should less than 42 character">
          <b-form-input v-model="detailObj.meta.description" type="text"></b-form-input>
        </b-form-group>
        <b-form-group v-if="['choice', 'highlight'].includes(detailObj.list_type)" label="Copyright" description="版權" >
          <b-form-input v-model="detailObj.meta.copyright" type="text"></b-form-input>
        </b-form-group>

        <!-- start fields only for title type -->
        <div v-if="detailObj.list_type === 'title'">
          <b-form-group label="Title ID" description="目標戲劇 ID"
            :state="titleIdState"
            invalid-feedback="Can't be blank"
          >
            <v-select v-model="detailObj.title_id" :options="this.titleOptions" label="label" :reduce="name => name.id" @input="getTrailerOptions"
                      :state="titleIdState"
            ></v-select>
          </b-form-group>
          <b-form-group description="若不勾選則不會播放 Trailer，只會顯示 Image">
            <b-form-checkbox v-model="detailObj.trailer_autoplay_enabled">自動播放預告片</b-form-checkbox>
          </b-form-group>
          <b-form-group label="指定 Trailer" description="非必填，如不指定，則會隨機播放">
            <v-select v-model="detailObj.trailer_episode_id" ref="extra-input" :options="this.trailerOptions" label="name" :reduce="name => name.id"></v-select >
          </b-form-group>
        </div>
        <!-- end -->

        <!-- start fields only for link type -->
        <div v-if="detailObj.list_type === 'link'">
          <b-form-group label="Deep Link" description="App 點選 Headline 後的行為，關於 Deep Link 設定，請參考：https://reurl.cc/odgl6v"
            :state="uriState"
            invalid-feedback="Can't be blank"
          >
            <b-form-input v-model="detailObj.uri" type="text"
              :state="uriState"
            ></b-form-input>
          </b-form-group>
          <b-form-group label="Web URL" description="網頁 User 點選 Headline 後轉換的網址，外部連結與內部連結設定不同，請參考：https://reurl.cc/odgl6v" >
            <b-form-input v-model="detailObj.url" type="text"></b-form-input>
          </b-form-group>
        </div>
        <!-- end -->

        <!-- start fields only for title type -->
        <div v-if="!['link', 'title'].includes(detailObj.list_type)">

          <b-form-group label="Title ID" description="輸入title ID ，若要一次輸入多筆，請用空格隔開"
            :state="metaTitleIdState"
            invalid-feedback="Can't be blank">
            <b-form-input type="text" v-model="titleID" @keyup.enter="onAdd"></b-form-input> <b-button variant="primary" @click="onAdd">加入</b-button>
            <ul v-show="isOpen" class="list-group mb-3">
              <li v-for="(item, index) in suggestions" :key="index"
                @click="setTitleID(item)"
                @mouseover="indexCounter = index"
                :class="{active: index == indexCounter}"
                class="list-group-item small-list-item">
                {{ item.id }} {{ item.name}}
              </li>
            </ul>
          </b-form-group>

          <!-- adjust order -->
          <b-form-group label="調整 titleid 排序或移除" description="請用拖拉的方式調整 title 排序">
            <b-list-group v-if="detailObj.meta && detailObj.meta.title_id">
              <draggable v-model="detailObj.meta.title_id">
                <b-list-group-item v-for="(item, index ) in detailObj.meta.title_id" v-bind:key="index">
                  {{ renderSelected(item)}}
                  <b-button @click="detailObj.meta.title_id.splice(index, 1)"><i class="fa fa-trash"></i></b-button>
                </b-list-group-item>
              </draggable>
            </b-list-group>
          </b-form-group>

        </div>
        <!-- end -->

      </form>
    </b-modal>
  </b-row>
</template>

<script>
import draggable from 'vuedraggable'
import api from '@/api'
import RESERVED_SHARE_ID_LIST from '@/utils/reservedShareIdList';

export default {
  name: 'TitleList',
  components: {
    draggable,
  },
  props: {
    caption: {
      type: String,
      default: 'TitleList'
    },
    hover: {
      type: Boolean,
      default: true
    },
    striped: {
      type: Boolean,
      default: true
    },
    bordered: {
      type: Boolean,
      default: false
    },
    small: {
      type: Boolean,
      default: false
    },
    fixed: {
      type: Boolean,
      default: false
    }
  },
  data: () => {
    return {
      q: '',
      items: [],
      suggestions: [],
      isOpen: false,
      indexCounter: -1,
      showAll: false,
      showModal: false,
      detailTitle: '',
      titleID: '',
      listTypeStr: '',
      selectedCollection: '',
      collectionOptions: [],
      detailObj: {id: null, meta: {title_id:[]}},
      image: null,
      supportTypeOptions: [
      ],
      supportListType: [],
      titleOptions: [],
      titleDict: {},
      trailerOptions: [],
      pinnedOptions: [
          { value: null, text: 'Please select an option' },
          { value: 'airing', text: '新劇跟播片單' },
          { value: 'new_finale', text: '全集新上架' },
          { value: 'guaranteed_visible', text: '保證曝光' }
      ],
      sinceDate: new Date(),
      untilDate: new Date(),
      displayDate: null,
      flatpickrConfig: {
        enableTime: true,
        altFormat: 'Y-m-d H:i',
        altInput: true,
        dateFormat: 'Z',
        allowInput: true,
      },
      orderState: null,
      captionState: null,
      imageState: null,
      titlelistDetailImageState: null,
      detailImageFeedback: null,
      ogImageState: null,
      ogFeedback: null,
      colorState: null,
      typeState: null,
      titleIdState: null,
      metaTitleIdState: null,
      uriState: null,
      descriptionState: null,
      alertCss: '',
      alertMsg: '',
      dismissCountDown: 0,
      fields: [
        {key: 'action', label: ''},
        {key: 'order', label: '排序'},
        {key: 'enabled', label: '啟用'},
        {key: 'duration'},
        {key: 'image'},
        {key: 'topic', label: 'Topic / Caption / Summary'},
        {key: 'list_type'},
        {key: 'collection', label: '分類'},
        {key: 'title', label: '劇名 or URL'},
      ],
      currentPage: 1,
      perPage: 0,
      totalRows: 0,
      shareLinkBaseUrl: '',
      titlelistDefault: '',
      backgroundImage: null,
      ogImage: null,
    }
  },
  computed: {
    filterItems () {
      var items = []
      // if showAll is checked, return all items
      if (this.showAll === 'checked') {
        items = this.items
      } else {
        // only display enabled items by default
        items = this.items.filter(item => {
          return item.enabled === true
        })
        // if displayDate is given, filter items by displayDate
        if (this.displayDate) {
          var displayDate = Date.parse(this.displayDate)
          items = items.filter(item => {
            return (Date.parse(item.visible_since) <= displayDate && Date.parse(item.visible_until) >= displayDate )
          })
        }
        // if query string is given, filter items by query string
        if (this.q !== '') {
          var qs = this.q
          items = items.filter(item => {
            return (item.title && item.title.includes(qs)) || (item.topic && item.topic.includes(qs))
          })
        }

        if (this.selectedCollection !== '') {
           var collection = this.selectedCollection
           items = items.filter(item => {
             return (item.meta.collections && item.meta.collections.includes(collection))
          })
        }
      }
      return items
    }
  },
  mounted () {
    this.prepare()
  },
  watch: {
    '$route': function (val, oldVal) {
      this.prepare()
    },
    'titleID': function (val, oldVal) {
      if (val != '' && val.length > 1 ) {
        this.filterSuggestions()
        this.isOpen = true

      } else {
        this.isOpen = false

      }
    }
  },
  methods: {
    getBadge (status) {
      var css
      switch (status) {
        case true:
          css = 'success'
          break
        case false:
          css = 'warning'
          break
      }
      return css
    },
    getLocalTime(time) {
      return new Date(time).toLocaleString('default', {
        hour12: false,
        year : 'numeric',
        month : 'short',
        day : '2-digit',
        hour : 'numeric',
        minute : 'numeric',
      })
    },
    getTitleOptions() {
      if (this.titleOptions.length === 0) {
        console.log('Load title options')
        api.request('get', '/v3/console/titlehint')
          .then((response) => {
            if (response.data && response.data.data &&
            response.data.data.titles) {
              this.titleOptions = response.data.data.titles
              for (var i=0; i < this.titleOptions.length; i++) {
                var item = this.titleOptions[i]
                this.titleDict[item['id']] = item
              }
            }
          })
      }
    },
    filterSuggestions () {
      var purgeID = this.titleID.split(' ').filter(s => s.trim()).map(s => s.trim())
      if (purgeID.length > 0) {
        var keyWord = purgeID[purgeID.length-1]
        this.suggestions = this.titleOptions.filter( i => i.id.startsWith(keyWord) || i.name.includes(keyWord))
      }
    },
    setTitleID (item) {
      // on user select the suggesttion item, remove last one then
      // append the selected item.id
      var purgeID = this.titleID.split(' ').filter(s => s.trim()).map(s => s.trim())
      purgeID.pop() // remove last one the suggestion input
      purgeID.push(item.id)
      if (this.detailObj.meta.title_id) {
        this.detailObj.meta.title_id = this.detailObj.meta.title_id.concat(purgeID)
      } else {
        this.detailObj.meta.title_id = purgeID
      }
      this.titleID = ''
    },
    onAdd() {
      var purgeID = this.titleID.split(' ').filter(s => s.trim()).map(s => s.trim())
      if (this.detailObj.meta.title_id) {
        this.detailObj.meta.title_id = this.detailObj.meta.title_id.concat(purgeID)
      } else {
        this.detailObj.meta.title_id = purgeID
      }
      this.titleID = ''
    },
    prepare() {
      // init
      this.supportTypeOptions = []
      this.resetFormStates()
      this.resetDetailObj()
      this.resetImage()

      var that = this
      if (this.$route.params.list_type !== '') {
        this.listTypeStr = this.$route.params.list_type
        this.listTypeStr.split(',').forEach( e => {
          that.supportListType.push(e)
          that.supportTypeOptions.push({value: e, text: e.charAt(0).toUpperCase() + e.slice(1)})
        })
      }
      this.onFetch()
    },
    getTrailerOptions() {
      if (this.detailObj.list_type === 'title') {
        if (this.detailObj.title_id) {
          const url = '/v3/console/extrahint?series_id=' + this.detailObj.title_id + "tr"
          console.log('Load trailer options from ' + url)
          api.request('get', url)
            .then((response) => {
              if (response.data && response.data.data &&
              response.data.data.extra) {
                this.trailerOptions = response.data.data.extra
              } else {
                console.log('Extra not found')
                this.resetSelectedExtra()
              }
            })
        } else {
          console.log('No title selected')
          this.resetSelectedExtra()
        }
      }
    },
    resetFormStates() {
      this.orderState = null
      this.captionState = null
      this.imageState = null
      this.colorState = null
      this.typeState = null
      this.titleIdState = null
      this.metaTitleIdState = null
      this.uriState = null
      this.titlelistDetailImageState = null
      this.ogImageState = null
    },
    resetDetailObj () {
      this.detailObj = {id: null, meta: {title_id:[]}}
    },
    resetImage () {
      if (this.image && this.$refs['image-input']) {
        this.$refs['image-input'].reset()
      }
      this.image = null
    },
    resetSelectedExtra() {
      if (this.$refs['extra-input']) {
        this.$refs['extra-input'].clearSelection()
      }
      this.trailerOptions = []
    },
    countDownChanged (dismissCountDown) {
      this.dismissCountDown = dismissCountDown
    },
    showAlert () {
      this.dismissCountDown = 5
    },
    renderSelected(titleid) {
      if (this.titleDict[titleid]) {
          return this.titleDict[titleid].id + ' ' + this.titleDict[titleid].name
      } else {
        return  titleid + ' Title Not Found'
      }
    },
    onFetch () {
      api.request('get', '/v3/console/metatitlelist?list_type=' +this.listTypeStr)
        .then((response) => {
          if (response.data && response.data.data &&
          response.data.data.titlelist) {
            this.items = response.data.data.titlelist
          } else {
            this.items = []
          }
        })

      api.request('get', '/v3/console/browse')
        .then((response) => {
          if (response.data && response.data.data) {
            var browse = response.data.data
            var collections = []
            for (var i=0; i < browse.length;i ++){
              var item = browse[i]
              var collection = item['collection_type'] + ':' + item['collection_name']
              collections.push({value: collection, text: collection})
            }

            this.collectionOptions = collections
            // this.items = response.data.data.titlelist
          } else {
            this.collectionOptions = []
          }
        })
      // preload select options for title
      this.getTitleOptions()
      this.getDefaultTitleListId()
    },
    getDefaultTitleListId() {
      api.request('get', '/v3/console/default_titlelist')
        .then((response) => {
          this.titlelistDefault = response.data.data && response.data.data.default_titlelist_id ? response.data.data.default_titlelist_id : null
          this.shareLinkBaseUrl = response.data.data && response.data.data.base_url ? response.data.data.base_url : null
        })
    },
    onStoreDefaultTitlelist() {
      api.request('post', '/v3/console/default_titlelist?defaultId=' +  this.titlelistDefault)
        .then(() => {
            this.alertCss = 'success'
            this.alertMsg = '儲存成功'
            this.showAlert()
            this.onFetch()
        })
        .catch(() => {
          this.alertCss = 'warning'
          this.alertMsg = '儲存失敗'
          this.showAlert()
          this.onFetch()
        })
    },
    onStoreTitleShareId() {
      // 因 DB 內部分 share id 有含大寫字母，因此 share id 轉小寫後不傳到 API，僅用來做 reserved share id 驗證，避免更新到舊有資料
      let share_id = this.detailObj.meta.share_id ? this.detailObj.meta.share_id.toLowerCase() : ''
      if (RESERVED_SHARE_ID_LIST.find(reservedShareId => reservedShareId === share_id)) {
        alert(`儲存失敗，${share_id} 為系統保留的片單 share id`)
        this.onFetch()
        return
      }
      api.request('put', '/v3/console/titlelist_shareId?id=' + this.detailObj.id + '&shareId=' + this.detailObj.meta.share_id)
        .then(() => {
          this.alertCss = 'success'
          this.alertMsg = '儲存成功'
          this.showAlert()
          this.onFetch()
        })
        .catch(() => {
          this.alertCss = 'warning'
          this.alertMsg = '儲存失敗'
          this.showAlert()
          this.onFetch()
        })
    },
    onModalShow(item) {
      this.detailObj = Object.assign({}, item)
      // preload select options for extra
      this.getTrailerOptions()

      if (item.id) {
        // update
        this.detailTitle =  '修改 ' + item.title
        this.sinceDate   = this.detailObj.visible_since
        this.untilDate   = this.detailObj.visible_until
      } else {
        // insert
        this.detailTitle =  '新增片單'
        this.sinceDate   = new Date()
        this.untilDate   = new Date()
        this.detailObj.enabled = true
      }
      this.showModal = true
    },
    validateOGImage(ogImage) {
        if ((40/21) != (ogImage.width/ogImage.height)) {
          return false
        }
        if ((ogImage.width > 1200) || (ogImage.height > 630)) {
          return false
        }
      return true
    },
    getSize(file) {
      return new Promise((resolve, reject) => {
        let _URL = window.URL || window.webkitURL;
        let img = new Image();

        img.onload = () => resolve({ height: img.height, width: img.width });
        img.onerror = reject;
        console.log("file:", file, "type:", typeof file)
        img.src = _URL.createObjectURL(file);
      });
    },
    validateBackGroundImage(backgroundImg) {
        console.log("ratio:", backgroundImg.width/backgroundImg.height)
        if ((16/9) != (backgroundImg.width/backgroundImg.height)) {
          return false
        }
      return true
    },
    async isValid() {
      var isOK = true
      this.resetFormStates()

      console.log('start validate')
      // handle general validation
      // require title, order, list_type
      if (!(this.detailObj.order && this.detailObj.order > 0)) {
        this.orderState = 'invalid'
        isOK = false
      }

      if (!this.detailObj.title) {
        this.captionState = 'invalid'
        isOK = false
      }

      if (!this.detailObj.list_type) {
        this.typeState = 'invalid'
        isOK = false
      }

      // handle different list_type validation
      switch (this.detailObj.list_type) {
        case 'link':
          // require image, uri
          if (!this.detailObj.source_image && !this.image) {
            this.imageState = 'invalid'
            isOK = false
          }
          if (this.detailObj.dominant_color && !/^[0-9A-F]{6}$/i.test(this.detailObj.dominant_color)) {
            this.colorState = 'invalid'
            isOK = false
          }
          if (!this.detailObj.uri) {
            this.uriState = 'invalid'
            isOK = false
          }
          break
        case 'title':
          // require image, title_id
          if (!this.detailObj.source_image && !this.image) {
            this.imageState = 'invalid'
            isOK = false
          }
          if (this.detailObj.dominant_color && !/^[0-9A-F]{6}$/i.test(this.detailObj.dominant_color)) {
            this.colorState = 'invalid'
            isOK = false
          }

          if (!this.detailObj.title_id) {
            this.titleIdState = 'invalid'
            isOK = false
          }
          break
        case 'highlight':
          // require image, tmeta.title_id Array
          if (!this.detailObj.source_image && !this.image) {
            this.imageState = 'invalid'
            isOK = false
          }
          if (this.detailObj.dominant_color && !/^[0-9A-F]{6}$/i.test(this.detailObj.dominant_color)) {
            this.colorState = 'invalid'
            isOK = false
          }
          if (!this.detailObj.meta || !this.detailObj.meta.title_id || this.detailObj.meta.title_id.length < 0) {
            isOK = false
          }

          if (this.detailObj.meta.description) {
            if (this.detailObj.meta.description.length > 42) {
              this.descriptionState = 'invalid'
              isOK = false
            }
          }

          if (this.ogImage) {
            let ogImageSize = await this.getSize(this.ogImage).then(imageSize => imageSize)
            if (!this.validateOGImage(ogImageSize)) {
              this.ogImageState = 'invalid'
              this.ogFeedback = 'image size wrong, should fit 1.91:1'
              isOK = false
            }
          }

          if (this.backgroundImage) {
            let backgroundImageSize = await this.getSize(this.backgroundImage).then(imageSize => imageSize)

            if (!this.validateBackGroundImage(backgroundImageSize)) {
              this.titlelistDetailImageState = 'invalid'
              this.detailImageFeedback = 'image size wrong, should fit 16:9'
              isOK = false
            }
          }

          break
        case 'choice':
          // require meta.title_id Array
          if (!this.detailObj.meta || !this.detailObj.meta.title_id || this.detailObj.meta.title_id.length < 0) {
            isOK = false
          }

          if (this.detailObj.meta.description) {
            if (this.detailObj.meta.description.length > 42) {
              this.descriptionState = 'invalid'
              isOK = false
            }
          }

          if (this.ogImage) {
            let choiceOgImageSize = await this.getSize(this.ogImage).then(imageSize => imageSize)
            if (!this.validateOGImage(choiceOgImageSize)) {
              this.ogImageState = 'invalid'
              this.ogFeedback = 'image size wrong, should fit 1.91:1'
              isOK = false
            }
          }

          if (this.backgroundImage) {
            let choiceBackgroundImageSize = await this.getSize(this.backgroundImage).then(imageSize => imageSize)
            if (!this.validateBackGroundImage(choiceBackgroundImageSize)) {
              this.titlelistDetailImageState = 'invalid'
              this.detailImageFeedback = 'image size wrong, should fit 16:9'
              isOK = false
            }
          }

          break
        default:
      }

      return isOK
    },
    async handleSubmit() {
      var that = this

      // handle attributes
      var since = Date.parse(this.sinceDate)
      var until = Date.parse(this.untilDate)
      if (since > until) {
        until = [since, since = until][0]
      }
      this.detailObj.visible_since = new Date(since).toISOString()
      this.detailObj.visible_until = new Date(until).toISOString()
      this.detailObj.order         = parseInt(this.detailObj.order)
      if (this.detailObj.dominant_color) {
        this.detailObj.dominant_color = this.detailObj.dominant_color.replace('#', '')
      } else {
        this.detailObj.dominant_color = null
      }
      let isValid = await this.isValid()
      if (!isValid) {
        return
      }

      // highlight always have collections:["genre:featured"]
      if (this.detailObj.list_type === 'highlight') {
        this.detailObj.meta.collections = ["genre:featured"]
      }

      const url = '/v3/console/metatitlelist'
      let method = 'post'
      if (this.detailObj.id) {
        method = 'put'
      }
      api.request(method, url, this.detailObj)
        .then((response) => {
          if (!this.detailObj.id) {
            this.detailObj.id = response.data.data.id
          }
          if (this.image) {
            const imgUploadUrl = '/v3/console/metatitlelistimage?id=' + this.detailObj.id
            let formData = new FormData()
            formData.append('file', this.image)
            api.request('post', imgUploadUrl, formData, { headers: { 'Content-Type': 'multipart/form-data' }})
              .then(function(){
                that.alertCss = 'success'
                that.alertMsg = '儲存成功'
                that.showAlert()
                that.onFetch()
              })
              .catch(function(){
                that.alertCss = 'warning'
                that.alertMsg = '圖片儲存失敗'
                that.showAlert()
                that.onFetch()
              })
          } else {
            that.alertCss = 'success'
            that.alertMsg = '儲存成功'
            that.showAlert()
            that.onFetch()
          }

          if (this.backgroundImage) {
            const imgUploadUrl = '/v3/console/titlelist_meta_image?id=' + this.detailObj.id + '&imageType=backgroundImage'
            let formData = new FormData()
            formData.append('file', this.backgroundImage)
            api.request('post', imgUploadUrl, formData, {headers: {'Content-Type': 'multipart/form-data'}})
              .then(function () {
                that.alertCss = 'success'
                that.alertMsg = '儲存成功'
                that.showAlert()
                that.onFetch()
              })
              .catch(function () {
                that.alertCss = 'warning'
                that.alertMsg = '圖片儲存失敗'
                that.showAlert()
                that.onFetch()
              })
          } else {
            that.alertCss = 'success'
            that.alertMsg = '儲存成功'
            that.showAlert()
            that.onFetch()
          }

          if (this.ogImage) {
            const imgUploadUrl = '/v3/console/titlelist_meta_image?id=' + this.detailObj.id + '&imageType=ogImage'
            let formData = new FormData()
            formData.append('file', this.ogImage)
            api.request('post', imgUploadUrl, formData, {headers: {'Content-Type': 'multipart/form-data'}})
              .then(function () {
                that.alertCss = 'success'
                that.alertMsg = '儲存成功'
                that.showAlert()
                that.onFetch()
              })
              .catch(function () {
                that.alertCss = 'warning'
                that.alertMsg = '圖片儲存失敗'
                that.showAlert()
                that.onFetch()
              })
          } else {
            that.alertCss = 'success'
            that.alertMsg = '儲存成功'
            that.showAlert()
            that.onFetch()
          }

          that.onCancel()
          that.$refs.formModal.hide()
        }).catch(error => {
          that.alertCss = 'warning'
          that.alertMsg = '這位大大，您的權限不足'
          that.onCancel()
          that.$refs.formModal.hide()
          that.showAlert()
        })
    },
    onOK(modalEvent) {
      modalEvent.preventDefault()
      this.handleSubmit()
    },
    onCancel () {
      // re-initilize variables
      this.resetFormStates()
      this.resetDetailObj()
      this.resetImage()
    },
    onDelete (item) {
      var confirm = window.confirm("Are you sure you want to delete this title list ?")
      if (confirm) {
        var that = this
        const url = '/v3/console/metatitlelist?id=' + item.id
        console.log(url)
        api.request('delete', url)
        .then(function(){
          that.alertCss = 'success'
          that.alertMsg = '刪除成功'
          that.showAlert()
          that.onFetch()
        })
        .catch(function(){
          that.alertCss = 'warning'
          that.alertMsg = '刪除失敗'
          that.showAlert()
          that.onFetch()
        })
      }
    }
  }
}
</script>

<style scoped>
.card-body >>> table > tbody > tr > td {
  cursor: pointer;
}
.small-list-item {
  padding: 2px;
}
</style>
