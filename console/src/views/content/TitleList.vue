<template>
  <b-row>
    <b-col cols="12" xl="12">
      <transition name="slide">

      <b-card no-header>
        <template slot="header">
          Title List
        </template>
          <b-row>
            <b-col sm="12">
            <form v-on:submit.prevent="onFetch">
            <b-form-group>
              <b-input-group>
                <!-- <b-form-input type="text" id="name" placeholder="Enter user_id" v-model="q"></b-form-input> -->
                <!-- Attach Right button -->
                <!-- <b-input-group-append>
                  <b-button variant="primary" @click="onFetch" >
                    <i class="fa" :class="{'fa-refresh': loading, 'fa-spin': loading}"></i>
                    Submit
                    </b-button>
                </b-input-group-append> -->
      <b-alert :show="dismissCountDown"
             dismissible
             variant="warning"
             @dismissed="dismissCountDown=0"
             @dismiss-count-down="countDownChanged">
       這位大大，您的權限不足
      </b-alert>

              </b-input-group>
            </b-form-group>
            </form>

            </b-col>
          </b-row>

        <b-table v-if="items" :hover="hover" :striped="striped" :bordered="bordered"
        :small="small" :fixed="fixed" responsive="sm" :items="Object.keys(titleMap)" :fields="fields"
        :current-page="currentPage" :per-page="perPage">
          <template slot="title_key" slot-scope="data">
            <strong>{{titleMap[data.item]}}</strong>
            &nbsp;<b-button @click="onLink(data.item)"><i class="fa fa-edit"></i> Edit</b-button>
          </template>
        </b-table>
      </b-card>
      </transition>
    </b-col>
  </b-row>
</template>

<script>
import { mapState } from 'vuex'
import api from '@/api'

export default {
  name: 'TitleList',
  props: {
    caption: {
      type: String,
      default: 'Title List'
    },
    hover: {
      type: Boolean,
      default: true
    },
    striped: {
      type: Boolean,
      default: true
    },
    bordered: {
      type: Boolean,
      default: false
    },
    small: {
      type: Boolean,
      default: false
    },
    fixed: {
      type: Boolean,
      default: false
    }
  },
  data: () => {
    return {
      items: [],
      titleMap: {},
      dismissCountDown: 0,
      q: '',
      fields: [
        {key: 'title_key', label: '片單'},
      ],
      currentPage: 1,
      // perPage: 5,
      perPage: 0,
      totalRows: 0
    }
  },
  computed: {
    ...mapState(
      ['loading']
    )
  },
  mounted () {
    this.onFetch()
  },
  methods: {
    goBack() {
      this.$router.go(-1)
    },
    getBadge (status) {
      return status === 'Active' ? 'success'
        : status === 'Inactive' ? 'secondary'
          : status === 'Pending' ? 'warning'
            : status === 'Banned' ? 'danger' : 'primary'
    },
    getRowCount (items) {
      return items.length
    },
    countDownChanged (dismissCountDown) {
      this.dismissCountDown = dismissCountDown
    },
    showAlert () {
      this.dismissCountDown = 5
    },
    onLink (title_key) {
      var path = "/content/titlelist/" + title_key
      console.log(path)
      this.$router.push({path: path})
    },
    onFetch () {
      // const id = this.$route.params.id
      api.request('get', '/v3/console/titlelist')
        .then((response) => {
          if (response.data && response.data.data) {
            // this.titeMap = response.data.data
            for (var key in response.data.data){
                this.$set(this.titleMap, key, response.data.data[key])
            }
          }
        })
    }

  }
}
</script>

<style scoped>
.card-body >>> table > tbody > tr > td {
  cursor: pointer;
}
</style>
