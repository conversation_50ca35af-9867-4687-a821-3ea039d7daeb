import api from "@/api";

function sortedByCollectionType(a, b) {
  if (a.collection_name === 'featured') {
    return -1
  } else if (b.collection_name === 'featured') {
    return 1
  }
  return (a.collection_type > b.collection_type) ? -1 : 1
}

function getFeaturedPageBrowses() {
  const allowedBrowseTypes = ['genre', 'content_agent', 'content_provider', 'plan'];

  return api.request('get', '/v3/console/browse')
    .then((response) => {
      if (response.data && response.data.data) {
        const browseItems = response.data.data;
        let hasHomeBrowse = false;
        let filtered = [];
        browseItems.forEach((item) => {
          if (item.collection_type === 'genre' && item.collection_name === 'featured') {
            hasHomeBrowse = true;
          }
          if (allowedBrowseTypes.includes(item.collection_type)) {
            filtered.push(item);
          }
        });
        // if items not contains `精選` then add it to the first
        if (!hasHomeBrowse) {
          filtered.unshift({collection_type: 'genre', collection_name: 'featured', title: '精選'});
        }
        filtered.sort(sortedByCollectionType)
        return filtered;
      } else {
        return [];
      }
    })
}

export default getFeaturedPageBrowses;
