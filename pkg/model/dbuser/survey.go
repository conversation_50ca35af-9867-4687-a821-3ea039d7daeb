package dbuser

import (
	"time"
)

type Survey struct {
	ID                            int64            `db:"id"`
	Identifier                    SurveyIdentifier `db:"identifier"`
	Status                        SurveyStatus     `db:"status"`
	AwardBillingProductIdentifier string           `db:"award_billing_product_identifier"` // 完成問卷獎勵
	CreatedAt                     time.Time        `db:"created_at"`
	UpdatedAt                     time.Time        `db:"updated_at"`
}

type SurveyStatus string

const (
	SurveyStatusActive   SurveyStatus = "active"
	SurveyStatusInactive SurveyStatus = "inactive"
)

type SurveyIdentifier string

const (
	SurveyIdentifierSignup SurveyIdentifier = "signup"
)

var AllowedSurveyIdentifiers = []SurveyIdentifier{
	SurveyIdentifierSignup,
}

func (s SurveyStatus) String() string {
	return string(s)
}

func (s SurveyIdentifier) String() string {
	return string(s)
}
