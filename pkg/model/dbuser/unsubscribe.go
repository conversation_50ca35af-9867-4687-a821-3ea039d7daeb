package dbuser

import (
	"gopkg.in/guregu/null.v3"
)

type Unsubscribe struct {
	UserID              string      `db:"user_id" json:"user_id"`
	Name                null.String `db:"name" json:"name"`
	Email               null.String `db:"email" json:"email"`
	Reason              string      `db:"reason" json:"reason"`
	PaymentType         null.String `db:"payment_type" json:"payment_type"`
	PaymentInfoSnapshot null.String `db:"payment_info_snapshot" json:"payment_info_snapshot"`
}
