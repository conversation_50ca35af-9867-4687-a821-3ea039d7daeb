package dbuser

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"sort"
	"strings"
	"time"

	"github.com/KKTV/kktv-api-v3/pkg/datatype"
	_ "github.com/lib/pq"

	"gopkg.in/guregu/null.v3"
)

type ProductPackageField string

func (f ProductPackageField) String() string {
	return string(f)
}

const (
	ProductPackageFieldPlatform          ProductPackageField = "platform"
	ProductPackageFieldPayDuration       ProductPackageField = "pay_duration"
	ProductPackageFieldProductIds        ProductPackageField = "product_ids"
	ProductPackageFieldActive            ProductPackageField = "active"
	ProductPackageFieldAutoRenew         ProductPackageField = "auto_renew"
	ProductPackageFieldSort              ProductPackageField = "sort"
	ProductPackageFieldInfo              ProductPackageField = "info"
	ProductPackageFieldCategory          ProductPackageField = "category"
	ProductPackageFieldBillingProductIds ProductPackageField = "billing_product_ids"
	ProductPackageFieldTargets           ProductPackageField = "targets"
)

type PackageTargetIdentity string

const (
	PackageTargetIdentityGuest                 PackageTargetIdentity = "guest"
	PackageTargetIdentityPrime                 PackageTargetIdentity = "prime"
	PackageTargetIdentityVip                   PackageTargetIdentity = "vip"
	PackageTargetIdentityFreeTrial             PackageTargetIdentity = "freetrial"
	PackageTargetIdentityExpiredNeverPurchased PackageTargetIdentity = "expired.never_purchased"
	PackageTargetIdentityExpiredPurchased      PackageTargetIdentity = "expired.purchased"
	PackageTargetIdentityAnimePass             PackageTargetIdentity = "anime_pass"
)

func (ti PackageTargetIdentity) String() string {
	return string(ti)
}

type IncExcOperator string

const (
	IncExcOperatorInclude IncExcOperator = "include"
	IncExcOperatorExclude IncExcOperator = "exclude"
)

func (o IncExcOperator) String() string {
	return string(o)
}

type PackageTargetLatestPackages struct {
	IDs      []int64        `json:"ids"`
	Operator IncExcOperator `json:"operator"`
}

type PackageTargetCondition struct {
	Identities     []string                     `json:"identities"`
	LatestPackages *PackageTargetLatestPackages `json:"latest_packages"`
}

type PackageTargetDisplay struct {
	Title           string      `json:"title"`
	Duration        string      `json:"duration"`
	Highlight       string      `json:"highlight"`
	ButtonText      string      `json:"button_text"`
	Description     string      `json:"description"`
	OriginPriceDesc string      `json:"origin_price_desc"`
	Label           null.String `json:"label"`
	Price           float64     `json:"price"`
}

type PackageTarget struct {
	Condition PackageTargetCondition `json:"condition"`
	Display   PackageTargetDisplay   `json:"display"`
}

type PackageTargets []PackageTarget

type ProductPackage struct {
	ID                int                       `db:"id"`
	Platform          string                    `db:"platform"`
	Price             string                    `db:"price"`
	Duration          string                    `db:"duration"`
	Title             string                    `db:"title"`
	Description       string                    `db:"description"`
	ButtonText        string                    `db:"button_text"`
	Label             null.String               `db:"label"`
	CreatedAt         time.Time                 `db:"created_at"`
	UpdatedAt         null.Time                 `db:"updated_at"`
	Active            bool                      `db:"active"`
	Highlight         string                    `db:"highlight"`
	AutoRenew         bool                      `db:"auto_renew"`
	Sort              int                       `db:"sort"`
	Promotion         null.String               `db:"promotion"`
	Info              *ProductPackageInfo       `db:"info"`     // jsonb
	Targets           *PackageTargets           `db:"targets"`  // jsonb
	Category          ProductPackageCategory    `db:"category"` // jsonb
	PayDuration       null.String               `db:"pay_duration"`
	Product           Product                   `db:"product"`
	BillingProductIds *ProductPackageProductIDs `db:"billing_product_ids"` // jsonb
	ProductIDs        datatype.JSONIntArray     `db:"product_ids"`         // jsonb
	Layout            *PackageLayout            `db:"package_layout"`      // left join PackageLayout
}

func (p *ProductPackage) PackageName() string {
	name := p.Title
	if p.Targets != nil && len(*p.Targets) > 0 {
		name = (*p.Targets)[0].Display.Title
	} else if p.Info != nil {
		for _, v := range *p.Info {
			name = v.Title
			break
		}
	}
	return name
}

func (p *ProductPackage) PackageDescription() string {
	description := p.Description
	if p.Targets != nil && len(*p.Targets) > 0 {
		description = (*p.Targets)[0].Display.Description
	} else if p.Info != nil {
		for _, v := range *p.Info {
			description = v.Description
			break
		}
	}
	return description
}

func (p *ProductPackage) IsBillingPackage() bool {
	return p.BillingProductIds != nil && len(*p.BillingProductIds) > 0
}

type ProductPackageInfo map[string]ProductPackageInfoItem

type ProductPackageInfoItem struct {
	Label       null.String `json:"label"`
	Price       float64     `json:"price"`
	Title       string      `json:"title"`
	Duration    string      `json:"duration"`
	DisplayTo   []string    `json:"displayTo"`
	Highlight   string      `json:"highlight"`
	ButtonText  string      `json:"button_text"`
	Description string      `json:"description"`
	OriginPrice string      `json:"originPriceDesc"`
}

func (info *ProductPackageInfo) Scan(src any) error {
	if src == nil {
		return nil
	}

	b, ok := src.([]byte)
	if !ok {
		return nil
	}

	return json.Unmarshal(b, info)
}

func (info *ProductPackageInfo) Value() (driver.Value, error) {
	if info == nil || len(*info) == 0 {
		return nil, nil
	}

	b, err := json.Marshal(info)
	if err != nil {
		return nil, err
	}

	return driver.Value(b), nil
}

type ProductPackageCategory []string

func (ppc *ProductPackageCategory) Scan(src interface{}) error {
	if src == nil {
		*ppc = nil
		return nil
	}

	srcBytes, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("invalid type for ProductPackageCategory")
	}

	var arrayData []string
	err := json.Unmarshal(srcBytes, &arrayData)
	if err != nil {
		return err
	}

	*ppc = arrayData

	return nil
}

func (ppc ProductPackageCategory) Value() (driver.Value, error) {
	if ppc == nil {
		ppc = []string{}
	}
	return json.Marshal(ppc)
}

type ProductPackageProductIDs []string

func (ids *ProductPackageProductIDs) Scan(src any) error {
	if src == nil {
		return nil
	}

	b, ok := src.([]byte)
	if !ok {
		return nil
	}

	return json.Unmarshal(b, ids)
}

func (ids ProductPackageProductIDs) Value() (driver.Value, error) {
	v := ids
	if v == nil {
		v = []string{}
	}
	return json.Marshal(v)
}

func (targets *PackageTargets) Scan(src any) error {
	if src == nil {
		return nil
	}

	b, ok := src.([]byte)
	if !ok {
		return nil
	}

	return json.Unmarshal(b, targets)
}

func (targets PackageTargets) Value() (driver.Value, error) {
	if targets == nil {
		targets = []PackageTarget{}
	}
	return json.Marshal(targets)
}

type ProductPackages []*ProductPackage

func (pkgs *ProductPackages) Append(productPackages *ProductPackages) *ProductPackages {
	if productPackages == nil {
		return pkgs
	}

	*pkgs = append(*pkgs, *productPackages...)

	return pkgs
}

func (pkgs *ProductPackages) Sort() *ProductPackages {
	sort.SliceStable(*pkgs, func(i, j int) bool {
		return (*pkgs)[i].Sort < (*pkgs)[j].Sort
	})

	return pkgs
}

func (pkgs *ProductPackages) FilterByRole(identities []string) *ProductPackages {
	return filter(*pkgs, identities, filterByIdentity)
}

func (pkgs *ProductPackages) FilterByProductIdSuffixs(identities []string) *ProductPackages {
	return filter(*pkgs, identities, filterByBillingProductIdSuffixs)
}

type filterFunc func(ProductPackage, []string) bool

func filter(pkgs ProductPackages, identities []string, f filterFunc) *ProductPackages {
	var filtered ProductPackages
	for _, pkg := range pkgs {
		if f(*pkg, identities) {
			filtered = append(filtered, pkg)
		}
	}

	return &filtered
}

func filterByIdentity(pkg ProductPackage, identities []string) bool {
	if pkg.Info == nil {
		return true
	}

	for displayObj := range *pkg.Info {
		for _, identity := range identities {
			if strings.Contains(displayObj, identity) {
				return true
			}
		}
	}

	return false
}

func filterByBillingProductIdSuffixs(pkg ProductPackage, identities []string) bool {
	if *pkg.BillingProductIds != nil {
		for _, productID := range *pkg.BillingProductIds {
			for _, identity := range identities {
				if strings.HasSuffix(productID, identity) {
					return false
				}
			}
		}
	}

	return true
}
