package cachemeta

import "github.com/KKTV/kktv-api-v3/pkg/model/dbmeta"

type UnitedSeriesDetail struct {
	ID       string `json:"id"`        // the original series id
	UnitedID string `json:"united_id"` // the united series id

	Available          string           `json:"available"`
	Name               string           `json:"name"` // already saved with united series name
	TitleType          dbmeta.TitleType `json:"title_type"`
	AudioTrackLanguage string           `json:"audio_track_language,omitempty"`
	AVODEpisodeHint    string           `json:"avod_episode_hint,omitempty"`
	Episodes           []Episode        `json:"episodes,omitempty"`

	IsContainingAVOD bool `json:"is_containing_avod,omitempty"`
	IsFreeTrial      bool `json:"is_free_trial,omitempty"`
	CountAVOD        int  `json:"count_avod,omitempty"`

	Ost    *dbmeta.Ost `json:"ost,omitempty"`
	Cover  string      `json:"cover,omitempty"`
	Stills []string    `json:"stills,omitempty"`

	ContentProvider string   `json:"content_provider,omitempty"`
	Casts           []string `json:"casts,omitempty"`
	Directors       []string `json:"directors,omitempty"`
	Writers         []string `json:"writers,omitempty"`
	Producers       []string `json:"producers,omitempty"`

	Copyright   string `json:"copyright,omitempty"`
	Country     string `json:"country,omitempty"`
	Description string `json:"description,omitempty"`

	Summary      string `json:"summary,omitempty"`
	StartYear    string `json:"start_year,omitempty"`
	EndYear      string `json:"end_year,omitempty"`
	LicenseStart string `json:"license_start,omitempty"`
	LicenseEnd   string `json:"license_end,omitempty"`

	IsEnding  string `json:"is_ending,omitempty"`
	UpdatedAt string `json:"updated_at,omitempty"`
}
