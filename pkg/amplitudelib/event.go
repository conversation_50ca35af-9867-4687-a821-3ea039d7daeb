package amplitudelib

import (
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/KKTV/kktv-api-v3/pkg/datetimer"
	"gopkg.in/guregu/null.v3"
)

type Event interface {
	GetIdentify() Identify
	GetAPIEvents() []ApiEvent
}

type mergedEvent struct {
	Identify
	apiEvents []ApiEvent
}

func (m *mergedEvent) GetIdentify() Identify {
	return m.Identify
}

func (m *mergedEvent) GetAPIEvents() []ApiEvent {
	return m.apiEvents
}

func MergedEvent(events ...Event) Event {
	if len(events) == 0 {
		return nil
	}
	idty := events[0].GetIdentify()
	apiEvents := make([]ApiEvent, 0)
	for _, evt := range events {
		apiEvents = append(apiEvents, evt.GetAPIEvents()...)
	}
	return &mergedEvent{
		Identify:  idty,
		apiEvents: apiEvents,
	}
}

type GeoBlockEvent struct {
	UserID   string
	DeviceID string

	Country           string
	RegisteredCountry string
	IP                string
}

func (g *GeoBlockEvent) GetIdentify() Identify {
	return Identify{
		UserID:   g.UserID,
		DeviceID: g.DeviceID,
	}
}

func (g *GeoBlockEvent) GetAPIEvents() []ApiEvent {
	return []ApiEvent{
		{
			UserID:    g.UserID,
			DeviceID:  g.DeviceID,
			EventType: "Geo Location Blocked",
			Time:      time.Now(),
			IP:        g.IP,
			EventProperties: map[string]interface{}{
				"country": g.Country, "registered country": g.RegisteredCountry,
			},
		},
	}
}

type hasReason struct {
	Reason string
}

type hasTriggerCondition struct {
	TriggerCondition string
}

type AccountRelatedEvent struct {
	Identify
	DeviceMode string
}

func (e *AccountRelatedEvent) ScanFromRequest(req *http.Request) {
	e.Identify, e.DeviceMode = NewIdentifyFromRequest(req)
}

type LoginEvent struct {
	AccountRelatedEvent
	SignUpType  string
	PhoneNumber string
	FbName      string
	FbID        string
}

func (e *LoginEvent) GetIdentify() Identify {
	idtf := e.Identify
	idtf.UserProperties.Set = map[string]interface{}{
		"account in guest mode":  "false",
		"account signed up type": e.SignUpType,
		"account phone number":   e.PhoneNumber,
		"account fb id":          e.FbID,
		"account fb name":        e.FbName,
		"account state":          "active",
	}
	idtf.UserProperties.Add = map[string]interface{}{
		"total times logged in":                  1,
		"total times logged in on " + e.Platform: 1,
	}
	return idtf
}

func (e *LoginEvent) GetAPIEvents() []ApiEvent {
	property := make(map[string]interface{})
	property["log version"] = "0.7.0"
	property["account signed up type"] = "kkid" // "kkid" "phone number" "facebook" "kktv"
	if e.SignUpType != "" {
		property["account signed up type"] = e.SignUpType
	}
	property["account user id"] = e.UserID
	property["signing process duration"] = time.Now().Unix()
	property["device mode"] = e.DeviceMode

	return []ApiEvent{
		{
			UserID:          e.UserID,
			DeviceID:        e.DeviceID,
			EventType:       "Account Logged In",
			Time:            time.Now(),
			EventProperties: property,
		},
	}
}

type KKTVAccount struct {
	ExpiredDate       null.Time
	VerifiedPhone     string
	VerifiedEmail     string
	PhoneVerifiedDate null.Time
	EmailVerifiedDate null.Time
}

func (k *KKTVAccount) parseExpiredDate() (expiredDateStr string) {
	return k.ExpiredDate.Time.In(datetimer.LocationTaipei).Format("2006-01-02")
}

func (k *KKTVAccount) parseVerifiedDate() (phoneVerifiedDateStr, emailVerifiedDateStr string) {
	if k.VerifiedPhone != "" {
		phoneVerifiedDateStr = k.PhoneVerifiedDate.Time.Format(time.DateOnly)
	} else {
		emailVerifiedDateStr = k.EmailVerifiedDate.Time.Format(time.DateOnly)
	}
	return
}

type SignUpEvent struct {
	AccountRelatedEvent
	KKTVAccount
	SignUpDate      time.Time
	SignUpType      string
	PhoneNumber     string
	FbID            string
	FbName          string
	KKBOXSub        string
	KKBOXIdentifier string
	Membership      string
	Gender          string
	Birthday        string
	RequestBy       string
}

func (e *SignUpEvent) GetIdentify() Identify {
	phoneVerifiedDateStr, emailVerifiedDateStr := e.parseVerifiedDate()
	idtf := e.Identify
	idtf.UserProperties.SetOnce = map[string]interface{}{
		"account signed up date": e.SignUpDate.Format(time.DateOnly),
	}
	idtf.UserProperties.Set = map[string]interface{}{
		"account in guest mode":            "false",
		"account signed up type":           e.SignUpType,
		"account kktv phone":               e.VerifiedPhone,
		"account kktv email":               e.VerifiedEmail,
		"phone verified date":              phoneVerifiedDateStr,
		"email verified date":              emailVerifiedDateStr,
		"account phone number":             e.PhoneNumber,
		"account fb id":                    e.FbID,
		"account fb name":                  e.FbName,
		"account kkbox sub":                e.KKBOXSub,
		"account kkbox user identifier":    e.KKBOXIdentifier,
		"account current membership":       e.Membership,
		"account type":                     "users",
		"account gender":                   e.Gender,
		"account birthday":                 e.Birthday,
		"account state":                    "active",
		"account ever cancelled":           "false",
		"account ever transacted":          "false",
		"total times logged in":            0,
		"total times logged in on iOS":     0,
		"total times logged in on Android": 0,
		"total times logged in on Web":     0,
		"total transactions count":         0,
		"total transactions amount":        0.0,
	}
	return idtf
}

func (e *SignUpEvent) GetAPIEvents() []ApiEvent {
	signUpType := "kktv" // kktv, kkid, facebook, phone number
	if e.SignUpType != "" {
		signUpType = e.SignUpType
	}
	requestBy := "kktv clients" // kktv clients, tstar
	if e.RequestBy != "" {
		requestBy = e.RequestBy
	}

	property := map[string]interface{}{
		"log version":              "0.7.0",
		"account signed up type":   signUpType,
		"request by":               requestBy,
		"account user id":          e.UserID,
		"signing process duration": time.Now().Unix(),
		"device mode":              e.DeviceMode,
	}

	return []ApiEvent{
		{
			UserID:          e.UserID,
			DeviceID:        e.DeviceID,
			EventType:       "Account Signed Up",
			Time:            time.Now(),
			EventProperties: property,
		},
	}
}

type AccountUpdatedEvent struct {
	AccountRelatedEvent
	KKTVAccount
}

func (e *AccountUpdatedEvent) GetIdentify() Identify {
	phoneVerifiedDateStr, emailVerifiedDateStr := e.parseVerifiedDate()
	idtf := e.Identify
	idtf.UserProperties.Set = map[string]interface{}{
		"account kktv phone":  e.VerifiedPhone,
		"account kktv email":  e.VerifiedEmail,
		"phone verified date": phoneVerifiedDateStr,
		"email verified date": emailVerifiedDateStr,
	}
	return idtf

}

func (e *AccountUpdatedEvent) GetAPIEvents() []ApiEvent {
	property := map[string]interface{}{
		"log version":     "0.7.0",
		"account user id": e.UserID,
		"device mode":     e.DeviceMode,
	}

	return []ApiEvent{
		{
			UserID:          e.UserID,
			DeviceID:        e.DeviceID,
			EventType:       "Account Updated",
			Time:            time.Now(),
			EventProperties: property,
		},
	}
}

type ExperimentEngagedEvent struct {
	Identify
	ProjectCode string
	ProjectName string
	TestGroup   string
}

func (e *ExperimentEngagedEvent) GetIdentify() Identify {
	return e.Identify
}

func (e *ExperimentEngagedEvent) GetAPIEvents() []ApiEvent {
	property := map[string]interface{}{
		"experiment project code": e.ProjectCode,
		"experiment project name": e.ProjectName,
		"test group":              e.TestGroup,
	}

	return []ApiEvent{
		{
			UserID:          e.UserID,
			DeviceID:        e.DeviceID,
			EventType:       "Experiment Engaged",
			Time:            time.Now(),
			EventProperties: property,
		},
	}
}

type AccountCouponRedeemedEvent struct {
	Identify
	PreviousPaymentType              string
	PreviousPlan                     string
	TotalTransactionsAmount          int64
	TotalTransactionsAmountViaCoupon int64
	Price                            int64
	Quantity                         int
	CouponType                       string
	CouponExp                        string
	Code                             string
	GroupID                          int64
	ObtainedVipMonths                int
	ObtainedVipDays                  int
	GivenFreeVipMonths               int
	GivenFreeVipDays                 int
}

func (e *AccountCouponRedeemedEvent) GetIdentify() Identify {
	idtf := e.Identify
	idtf.UserProperties.Set = map[string]interface{}{
		"account current membership": "premium",
		"previous payment type":      e.PreviousPaymentType,
		"previous plan":              e.PreviousPlan,
	}
	idtf.UserProperties.SetOnce = map[string]interface{}{
		"account ever transacted": "false",
	}
	idtf.UserProperties.Add = map[string]interface{}{
		"total transactions count":             1,
		"total transactions amount":            e.TotalTransactionsAmount,
		"total transactions count via Coupon":  1,
		"total transactions amount via Coupon": e.TotalTransactionsAmountViaCoupon,
	}
	return idtf
}

func (e *AccountCouponRedeemedEvent) GetAPIEvents() []ApiEvent {
	property := make(map[string]interface{})
	property["log version"] = "0.7.0"
	property["coupon code"] = e.Code
	property["coupon group id"] = e.GroupID
	property["coupon type"] = e.CouponType
	property["revenue"] = e.Price
	property["coupon code expiration date"] = e.CouponExp
	property["obtained vip months"] = e.ObtainedVipMonths
	property["obtained vip days"] = e.ObtainedVipDays
	property["given free vip months"] = e.GivenFreeVipMonths
	property["given free vip days"] = e.GivenFreeVipDays

	return []ApiEvent{
		{
			UserID:    e.UserID,
			DeviceID:  e.DeviceID,
			EventType: "Account Coupon Redeemed",
			Time:      time.Now(),
			Price:     int(e.Price),
			Quantity:  e.Quantity,
			Revenue:   int(e.Price),

			EventProperties: property,
		},
	}
}

func (e *AccountCouponRedeemedEvent) SetDurationFromCouponGroupDuration(groupDuration, groupFreeDuration string) {
	duration := strings.Split(groupDuration, "-")
	freeDuration := strings.Split(groupFreeDuration, "-")

	dYear, _ := strconv.Atoi(duration[0])
	fYear, _ := strconv.Atoi(freeDuration[0])
	dMonth, _ := strconv.Atoi(duration[1])
	fMonth, _ := strconv.Atoi(freeDuration[1])
	dDay, _ := strconv.Atoi(duration[2])
	fDay, _ := strconv.Atoi(freeDuration[2])

	if dMonth > 0 {
		e.ObtainedVipMonths = dMonth + (dYear * 12)
	}
	if dDay > 0 {
		e.ObtainedVipDays = dDay
	}
	if fMonth > 0 {
		e.GivenFreeVipMonths = fMonth + (fYear * 12)
	}
	if fDay > 0 {
		e.GivenFreeVipDays = fDay
	}

	totalYears := dYear + fYear
	totalMonths := dMonth + fMonth
	totalDays := dDay + fDay
	var couponDuration string
	if totalDays > 0 {
		couponDuration = fmt.Sprintf("%d days", totalDays)
	}
	if totalMonths > 0 {
		couponDuration = fmt.Sprintf("%d months %d days", totalMonths, totalDays)
	}
	if totalYears > 0 {
		couponDuration = fmt.Sprintf("%d years %d months %d days", totalYears, totalMonths, totalDays)
	}
	e.PreviousPlan = fmt.Sprintf("coupon %s", couponDuration)
}

type AccountTransactionCompletedEvent struct {
	AccountRelatedEvent
	KKTVAccount
	UserPlan              string // Deprecated
	PaymentType           string
	Plan                  string // Deprecated
	BillingPlan           string
	ProductID             string
	ProductPeriod         string
	TriggerCondition      string
	Price                 int
	Quantity              int
	Revenue               int
	OrderNumber           string
	ProductDurationDays   int
	ProductDurationMonths int
	InSubscription        null.Bool
	PackageName           string
	PackageDescription    string
	PackageType           string
}

func (e *AccountTransactionCompletedEvent) GetIdentify() Identify {
	idtf := e.Identify
	idtf.UserProperties.Set = map[string]interface{}{
		"account current membership": "premium",
	}
	idtf.UserProperties.Add = map[string]interface{}{
		"total transactions count":  1,
		"total transactions amount": e.Revenue,
	}
	if e.PaymentType != "" {
		idtf.UserProperties.Set["payment type"] = e.PaymentType
		if e.Revenue > 0 {
			idtf.UserProperties.Add["total transactions amount via "+e.PaymentType] = e.Revenue
		}
	}
	if e.UserPlan != "" {
		idtf.UserProperties.Set["plan"] = e.UserPlan // Deprecated
	}
	return idtf
}

func (e *AccountTransactionCompletedEvent) GetAPIEvents() []ApiEvent {
	property := map[string]interface{}{
		"log version":         "0.7.0",
		"revenue":             e.Revenue,
		"payment type":        e.PaymentType,
		"plan":                e.Plan, // Deprecated
		"billing plan":        e.BillingPlan,
		"product id":          e.ProductID,
		"product period":      e.ProductPeriod,
		"package name":        e.PackageName,
		"package description": e.PackageDescription,
		"package type":        e.PackageType,
		"trigger condition":   e.TriggerCondition,
		"order number":        e.OrderNumber,
	}
	if e.Price == 0 {
		property["given free vip days"] = e.ProductDurationDays
	} else {
		if e.ProductDurationDays > 0 {
			property["obtained vip days"] = e.ProductDurationDays
		} else if e.ProductDurationMonths > 0 {
			property["obtained vip months"] = e.ProductDurationMonths
		}
	}
	return []ApiEvent{
		{
			UserID:          e.UserID,
			DeviceID:        e.DeviceID,
			EventType:       "Account Transaction Completed",
			Time:            time.Now(),
			Price:           e.Price,
			Quantity:        1,
			Revenue:         e.Revenue,
			EventProperties: property,
		},
	}
}

type MODAccountTransactionCompletedEvent struct {
	AccountTransactionCompletedEvent
	ModSubscriberID string
}

func (e *MODAccountTransactionCompletedEvent) GetAPIEvents() []ApiEvent {
	events := e.AccountTransactionCompletedEvent.GetAPIEvents()
	if len(events) == 1 && e.ModSubscriberID != "" {
		events[0].EventProperties["account mod subscriber id"] = e.ModSubscriberID
	}
	return events
}

type TelecomAccountTransactionCompletedEvent struct {
	AccountTransactionCompletedEvent
	TelecomBill string
}

func (e *TelecomAccountTransactionCompletedEvent) GetIdentify() Identify {
	e.PaymentType = "telecom"
	return e.AccountTransactionCompletedEvent.GetIdentify()
}

func (e *TelecomAccountTransactionCompletedEvent) GetAPIEvents() []ApiEvent {
	e.PaymentType = "telecom"
	events := e.AccountTransactionCompletedEvent.GetAPIEvents()
	if len(events) == 1 {
		if e.TelecomBill != "" {
			events[0].EventProperties["account telecom bill"] = e.TelecomBill
		}
	}
	return events
}

type AccountTransactionCancelledEvent struct {
	AccountRelatedEvent
	KKTVAccount
	hasReason
	hasTriggerCondition
	PaymentType  string
	PackageTitle string
	ProductId    string
	ProductName  string
	Membership   []string
}

func (e *AccountTransactionCancelledEvent) GetIdentify() Identify {
	idtf := e.Identify
	idtf.UserProperties.Set = map[string]interface{}{
		"account expired date": e.parseExpiredDate(),
		"in subscription":      false,
	}
	return idtf
}

func (e *AccountTransactionCancelledEvent) GetAPIEvents() []ApiEvent {
	property := make(map[string]interface{})
	property["log version"] = "0.7.0"
	property["payment type"] = e.PaymentType
	property["cancellation reason"] = e.Reason
	property["trigger condition"] = e.TriggerCondition
	property["account current membership"] = e.Membership
	if e.PackageTitle != "" {
		property["package title"] = e.PackageTitle
	}
	if e.ProductId != "" {
		property["product id"] = e.ProductId
	}
	if e.ProductName != "" {
		property["product name"] = e.ProductName
	}
	return []ApiEvent{
		{
			UserID:    e.UserID,
			DeviceID:  e.DeviceID,
			EventType: "Account Transaction Cancelled",
			Time:      time.Now(),

			EventProperties: property,
		},
	}
}

type AccountSurveyProfileUpdatedEvent struct {
	AccountRelatedEvent

	// 問卷個資
	Gender   string
	Birthday string
	Nickname string
	Phone    string
	Email    string
	Job      string

	Preferences map[string]interface{}
}

func (e *AccountSurveyProfileUpdatedEvent) GetIdentify() Identify {
	idtf := e.Identify
	userProps := map[string]interface{}{
		"account gender":      e.Gender,
		"account birthday":    e.Birthday,
		"nickname":            e.Nickname,
		"survey email":        e.Email,
		"survey phone number": e.Phone,
		"survey job":          e.Job,
	}

	for question, answer := range e.Preferences {
		question = fmt.Sprintf("survey %s", strings.ReplaceAll(question, "_", " "))
		userProps[question] = answer
	}

	idtf.UserProperties.Set = userProps
	return idtf
}

func (e *AccountSurveyProfileUpdatedEvent) GetAPIEvents() []ApiEvent {
	eventProps := map[string]interface{}{
		"log version": "0.7.0",
	}

	for question, answer := range e.Preferences {
		question = strings.ReplaceAll(question, "_", " ")
		eventProps[question] = answer
	}

	return []ApiEvent{
		{
			UserID:          e.UserID,
			DeviceID:        e.DeviceID,
			EventType:       "Account Survey Profile Updated",
			Time:            time.Now(),
			EventProperties: eventProps,
		},
	}
}

type AccountPackageChangedEvent struct {
	AccountRelatedEvent
	KKTVAccount
	PaymentType    string
	Plan           string // Deprecated
	BillingPlan    string
	ProductID      string
	ProductPeriod  string
	InSubscription null.Bool
	// package
	PackageName        string
	PackageDescription string
	PackageType        string
}

func (e *AccountPackageChangedEvent) GetIdentify() Identify {
	idtf := e.Identify
	idtf.UserProperties.Set = map[string]interface{}{
		"account current membership": "premium",
		"payment type":               e.PaymentType,
		"plan":                       e.Plan,
		"billing plan":               e.BillingPlan,
		"product id":                 e.ProductID,
		"product period":             e.ProductPeriod,
		"package name":               e.PackageName,
		"package description":        e.PackageDescription,
		"package type":               e.PackageType,
		"in subscription":            e.InSubscription,
	}

	return idtf
}

func (e *AccountPackageChangedEvent) GetAPIEvents() []ApiEvent {
	property := map[string]interface{}{
		"payment type":        e.PaymentType,
		"plan":                e.Plan, // Deprecated
		"billing plan":        e.BillingPlan,
		"product id":          e.ProductID,
		"product period":      e.ProductPeriod,
		"package name":        e.PackageName,
		"package description": e.PackageDescription,
		"package type":        e.PackageType,
	}
	return []ApiEvent{
		{
			UserID:          e.UserID,
			DeviceID:        e.DeviceID,
			EventType:       "Account Package Changed",
			Time:            time.Now(),
			EventProperties: property,
		},
	}
}

type AccountDowngradedEvent struct {
	AccountRelatedEvent
	TriggerCondition     string
	CancellationReason   string
	CancelledPaymentType string
}

func (e *AccountDowngradedEvent) SetCancelReasonByPaymentStatus(paymentStatus string) {
	switch paymentStatus {
	case "ok", "error":
		e.CancellationReason = "payment error"
	case "fail", "in_progress":
		e.CancellationReason = "payment fail"
	case "", "cancel", "refund":
		fallthrough
	default:
		e.CancellationReason = "user intention"
	}
}

func (e *AccountDowngradedEvent) GetIdentify() Identify {
	idtf := e.Identify
	idtf.UserProperties.Set = map[string]interface{}{
		"account current membership": "expired",
	}
	return idtf
}

func (e *AccountDowngradedEvent) GetAPIEvents() []ApiEvent {
	property := map[string]interface{}{
		"log version":            "0.7.0",
		"trigger condition":      e.TriggerCondition,
		"cancellation reason":    e.CancellationReason,
		"cancelled payment type": e.CancelledPaymentType,
	}

	return []ApiEvent{
		{
			UserID:          e.UserID,
			DeviceID:        e.DeviceID,
			EventType:       "Account Downgraded",
			Time:            time.Now(),
			EventProperties: property,
		},
	}
}
