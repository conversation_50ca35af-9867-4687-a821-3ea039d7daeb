package amplitudelib

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestIdentify_ScanFromRequest(t *testing.T) {
	r, _ := http.NewRequest("GET", "http://localhost:8080/", nil)
	r.Header.Set("X-DEVICE-ID", "aaabbb")
	r.Header.Set("X-KKTV-PLATFORM", "ios")
	r.Header.Set("X-KKTV-APP-VERSION", "1.0.0")

	copyCat := ExperimentEngagedEvent{
		TestGroup: "test A",
	}
	copyCat.ScanFromRequest(r)

	assert.Equal(t, "aaabbb", copyCat.DeviceID)
	assert.Equal(t, "ios", copyCat.Platform)
	assert.Equal(t, "1.0.0", copyCat.AppVersion)
	assert.Equal(t, "test A", copyCat.TestGroup)
}
