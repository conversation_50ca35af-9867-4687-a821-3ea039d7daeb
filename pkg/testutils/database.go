package testutils

import (
	"context"
	"errors"
	"fmt"
	"log"
	"strings"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp/datastore"
	"github.com/KKTV/kktv-api-v3/migrations"
	"github.com/KKTV/kktv-api-v3/pkg/rand"
	"github.com/docker/go-connections/nat"
	"github.com/golang-migrate/migrate/v4"
	_ "github.com/golang-migrate/migrate/v4/database/postgres"
	_ "github.com/golang-migrate/migrate/v4/source/file"
	"github.com/golang-migrate/migrate/v4/source/iofs"
	"github.com/testcontainers/testcontainers-go"
	"github.com/testcontainers/testcontainers-go/network"
	"github.com/testcontainers/testcontainers-go/wait"
)

type DBConf struct {
	DBName       string
	MigrationDir string
	HasReplica   bool
}

var (
	DBUser = DBConf{
		DBName:       "kktv_users",
		MigrationDir: "user",
		HasReplica:   true,
	}
	DBMeta = DBConf{
		DBName:       "meta",
		MigrationDir: "meta",
	}
	DBRedeem = DBConf{
		DBName:       "kktv_redeem",
		MigrationDir: "redeem",
	}
)

var changelogs = migrations.Changelogs

func MigrateDB(uri, migrationDir string) error {
	// check if there is the db_changelog directory first
	fileDriver, err := iofs.New(changelogs, "db_changelog/"+migrationDir)
	if err != nil {
		log.Fatalf("Could not create file driver: %v, please make sure the migration files are ready. run `make get-db-migrate`. ", err)
	}
	m, err := migrate.NewWithSourceInstance("iofs", fileDriver, uri)
	if err != nil {
		return err
	}
	defer m.Close()

	err = m.Up()
	if err != nil && !errors.Is(err, migrate.ErrNoChange) {
		return err
	}
	return nil
}

type Container struct {
	Containers []testcontainers.Container
}

func (c *Container) Close() error {
	ctx := context.Background()
	var errs []error
	for _, container := range c.Containers {
		if err := container.Terminate(ctx); err != nil {
			errs = append(errs, err)
		}
	}
	if len(errs) > 0 {
		return fmt.Errorf("failed to terminate containers: %v", errs)
	}
	return nil
}

func SetupTestDatabase(requiredDB DBConf) (*Container, *datastore.DBPool) {
	ctx := context.Background()
	newNetwork, err := network.New(ctx)
	if err != nil {
		panic(err)
	}

	networkName := newNetwork.Name

	randomID := rand.New().RandomString(6)
	// Create PostgreSQL primary container request
	masterReq := testcontainers.ContainerRequest{
		Image:        "bitnami/postgresql:13.3.0",
		ExposedPorts: []string{"5432/tcp"},
		Networks:     []string{networkName},
		WaitingFor:   wait.ForListeningPort("5432/tcp"),
		Env: map[string]string{
			"POSTGRESQL_USERNAME":             "postgres",
			"POSTGRESQL_PASSWORD":             "123456",
			"POSTGRESQL_DATABASE":             requiredDB.DBName,
			"POSTGRESQL_REPLICATION_MODE":     "master",
			"POSTGRESQL_REPLICATION_USER":     "replicator",
			"POSTGRESQL_REPLICATION_PASSWORD": "replicator",
			"POSTGRESQL_INITDB_ARGS":          "--data-checksums",
		},
		Name: fmt.Sprintf("%s%s-db-primary", requiredDB.DBName, randomID),
	}

	// Start PostgreSQL container
	masterContainer, err := testcontainers.GenericContainer(
		ctx,
		testcontainers.GenericContainerRequest{
			ContainerRequest: masterReq,
			Started:          true,
		})
	if err != nil {
		panic(err)
	}
	// Get host and port of PostgreSQL container
	masterHost, err := masterContainer.Host(ctx)
	if err != nil {
		panic(err)
	}
	masterPort, err := masterContainer.MappedPort(ctx, "5432")
	if err != nil {
		panic(err)
	}

	masterConnectURI := fmt.Sprintf("postgres://postgres:123456@%v:%v/%v?sslmode=disable", masterHost, masterPort.Port(), requiredDB.DBName)

	dsns := []string{masterConnectURI}

	containers := []testcontainers.Container{masterContainer}
	if requiredDB.HasReplica {
		replicaContainer, replicaHost, replicaPort := createReplica(ctx, masterContainer, requiredDB, networkName)
		replicaConnectURI := fmt.Sprintf("postgres://postgres:123456@%v:%v/%v?sslmode=disable", replicaHost, replicaPort.Port(), requiredDB.DBName)
		dsns = append(dsns, replicaConnectURI)

		containers = append(containers, replicaContainer)
	}

	// migrate the database. maximum 10 times retry
	for i := 0; i < 10; i++ {
		err = MigrateDB(masterConnectURI, requiredDB.MigrationDir)
		if err != nil {
			//sleep 3 seconds before retry
			time.Sleep(3 * time.Second)
		}
	}
	if err != nil {
		panic(err)
	}
	connPool := datastore.NewDBPool(dsns)
	return &Container{
		Containers: containers,
	}, connPool
}

func createReplica(ctx context.Context,
	masterContainer testcontainers.Container, requiredDB DBConf, networkName string) (testcontainers.Container, string, nat.Port) {
	info, err := masterContainer.Inspect(ctx)
	if err != nil {
		panic(err)
	}
	masterName := strings.TrimPrefix(info.Name, "/")
	// Create PostgreSQL replica container request
	replicaReq := testcontainers.ContainerRequest{
		Image:        "bitnami/postgresql:13.3.0",
		ExposedPorts: []string{"5432/tcp"},
		Networks:     []string{networkName},
		WaitingFor:   wait.ForListeningPort("5432/tcp"),
		Env: map[string]string{
			"POSTGRESQL_USERNAME":             "postgres",
			"POSTGRESQL_PASSWORD":             "123456",
			"POSTGRESQL_DATABASE":             requiredDB.DBName,
			"POSTGRESQL_REPLICATION_MODE":     "slave",
			"POSTGRESQL_REPLICATION_USER":     "replicator",
			"POSTGRESQL_REPLICATION_PASSWORD": "replicator",
			"POSTGRESQL_MASTER_HOST":          masterName,
			"POSTGRESQL_MASTER_PORT_NUMBER":   "5432",
		},
	}
	// Start PostgreSQL replica container
	replicaContainer, err := testcontainers.GenericContainer(
		ctx,
		testcontainers.GenericContainerRequest{
			ContainerRequest: replicaReq,
			Started:          true,
		})
	if err != nil {
		panic(err)
	}

	replicaHost, err := replicaContainer.Host(context.Background())
	if err != nil {
		panic(err)
	}
	replicaPort, err := replicaContainer.MappedPort(context.Background(), "5432")
	if err != nil {
		panic(err)
	}
	return replicaContainer, replicaHost, replicaPort
}
