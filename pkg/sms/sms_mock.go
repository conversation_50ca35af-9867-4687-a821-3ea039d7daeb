// Code generated by MockGen. DO NOT EDIT.
// Source: sms.go

// Package sms is a generated GoMock package.
package sms

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockServiceProvider is a mock of ServiceProvider interface.
type MockServiceProvider struct {
	ctrl     *gomock.Controller
	recorder *MockServiceProviderMockRecorder
}

// MockServiceProviderMockRecorder is the mock recorder for MockServiceProvider.
type MockServiceProviderMockRecorder struct {
	mock *MockServiceProvider
}

// NewMockServiceProvider creates a new mock instance.
func NewMockServiceProvider(ctrl *gomock.Controller) *MockServiceProvider {
	mock := &MockServiceProvider{ctrl: ctrl}
	mock.recorder = &MockServiceProviderMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceProvider) EXPECT() *MockServiceProviderMockRecorder {
	return m.recorder
}

// SendMessage mocks base method.
func (m *MockServiceProvider) SendMessage(sender, recipient, content string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendMessage", sender, recipient, content)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendMessage indicates an expected call of SendMessage.
func (mr *MockServiceProviderMockRecorder) SendMessage(sender, recipient, content interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendMessage", reflect.TypeOf((*MockServiceProvider)(nil).SendMessage), sender, recipient, content)
}
