package billing

import (
	"encoding/json"
	"regexp"
	"sort"
	"strconv"
	"strings"

	zlog "github.com/KKTV/kktv-api-v3/pkg/log"
)

type Products struct {
	Products []Product `json:"products"`
	Map      map[string]Product
}

type Product struct {
	Identifier            string `json:"identifier"`
	Name                  string `json:"name"`
	PaymentType           string `json:"payment_type"`
	IntervalAmount        string `json:"interval_amount"`
	Recurring             bool   `json:"recurring"`
	Price                 int32  `json:"price"`
	ListPrice             int32  `json:"list_price"`
	Trial                 bool   `json:"trial"`
	CtaButton             string `json:"cta_button"`
	Description           string `json:"description"`
	Referral              string `json:"referral"`
	Label                 string `json:"label"`
	IntroductoryOffer     bool   `json:"introductory_offer"`
	IntroductoryOfferable bool   `json:"introductory_offerable"`
	PreOrder              bool   `json:"pre_order"`
	Plan                  string `json:"plan"`
}

func (p *Product) PackageType() string {
	packageType := "channel"
	if p.PaymentType == string(PaymentTypeCreditCard) {
		packageType = "organic"
	}
	return packageType
}

func (p *Product) ParsedIntervalAmount() (int64, string, error) {
	var amountStr string
	var amount int64
	var unit string
	var err error

	interval := p.IntervalAmount
	reg := regexp.MustCompile(`(\d+)*\s*個*([天月年])`)
	units := map[string]string{"天": "day", "月": "month", "年": "year"}

	if reg.MatchString(interval) {
		matches := reg.FindStringSubmatch(interval)
		amountStr, unit = matches[1], units[matches[2]]
		if amountStr == "" {
			amountStr = "1"
		}
		amount, err = strconv.ParseInt(amountStr, 10, 64)
	}

	return amount, unit, err
}

// IsPrimeOnly 判斷產品方案是否為 Prime 專屬優惠方案
func (p *Product) IsPrimeOnly() bool {
	return strings.HasSuffix(p.Identifier, ".prime-only")
}

func (p *Product) IsPreOrder() bool {
	return p.PreOrder
}

type GetProductsRespData struct {
	Products []Product `json:"products"`
}

func (r *WebhookReqBody) ToGetProductsRespData() (data *GetProductsRespData, err error) {
	err = json.Unmarshal([]byte(r.Data), &data)
	if err != nil {
		zlog.Error("billing webhook: decrypted products failed").Err(err).Send()
	}
	return
}

func (p *Products) Sort() {
	sort.Slice(p.Products, func(i, j int) bool {
		products := p.Products
		if products[i].PaymentType == products[j].PaymentType {
			return products[i].Identifier < products[j].Identifier
		}
		return products[i].PaymentType < products[j].PaymentType
	})
}
