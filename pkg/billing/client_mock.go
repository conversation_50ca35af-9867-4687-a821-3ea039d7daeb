// Code generated by MockGen. DO NOT EDIT.
// Source: client.go

// Package billing is a generated GoMock package.
package billing

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockClient is a mock of Client interface.
type MockClient struct {
	ctrl     *gomock.Controller
	recorder *MockClientMockRecorder
}

// MockClientMockRecorder is the mock recorder for MockClient.
type MockClientMockRecorder struct {
	mock *MockClient
}

// NewMockClient creates a new mock instance.
func NewMockClient(ctrl *gomock.Controller) *MockClient {
	mock := &MockClient{ctrl: ctrl}
	mock.recorder = &MockClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockClient) EXPECT() *MockClientMockRecorder {
	return m.recorder
}

// CancelSubscription mocks base method.
func (m *MockClient) CancelSubscription(userID string) (Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CancelSubscription", userID)
	ret0, _ := ret[0].(Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CancelSubscription indicates an expected call of CancelSubscription.
func (mr *MockClientMockRecorder) CancelSubscription(userID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelSubscription", reflect.TypeOf((*MockClient)(nil).CancelSubscription), userID)
}

// CreateGracePeriodOrder mocks base method.
func (m *MockClient) CreateGracePeriodOrder(userID string, req CreateGracePeriodOrderReq) (*CreateOrderRespData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateGracePeriodOrder", userID, req)
	ret0, _ := ret[0].(*CreateOrderRespData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateGracePeriodOrder indicates an expected call of CreateGracePeriodOrder.
func (mr *MockClientMockRecorder) CreateGracePeriodOrder(userID, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateGracePeriodOrder", reflect.TypeOf((*MockClient)(nil).CreateGracePeriodOrder), userID, req)
}

// CreateOrder mocks base method.
func (m *MockClient) CreateOrder(params map[string]string, userID string) (Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateOrder", params, userID)
	ret0, _ := ret[0].(Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateOrder indicates an expected call of CreateOrder.
func (mr *MockClientMockRecorder) CreateOrder(params, userID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateOrder", reflect.TypeOf((*MockClient)(nil).CreateOrder), params, userID)
}

// FulFillOrder mocks base method.
func (m *MockClient) FulFillOrder(userID string) (Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FulFillOrder", userID)
	ret0, _ := ret[0].(Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FulFillOrder indicates an expected call of FulFillOrder.
func (mr *MockClientMockRecorder) FulFillOrder(userID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FulFillOrder", reflect.TypeOf((*MockClient)(nil).FulFillOrder), userID)
}

// GetCustomerContract mocks base method.
func (m *MockClient) GetCustomerContract(userID string) (Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCustomerContract", userID)
	ret0, _ := ret[0].(Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCustomerContract indicates an expected call of GetCustomerContract.
func (mr *MockClientMockRecorder) GetCustomerContract(userID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCustomerContract", reflect.TypeOf((*MockClient)(nil).GetCustomerContract), userID)
}

// GetCustomerStatus mocks base method.
func (m *MockClient) GetCustomerStatus(userID string) (*CustomerStatus, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCustomerStatus", userID)
	ret0, _ := ret[0].(*CustomerStatus)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCustomerStatus indicates an expected call of GetCustomerStatus.
func (mr *MockClientMockRecorder) GetCustomerStatus(userID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCustomerStatus", reflect.TypeOf((*MockClient)(nil).GetCustomerStatus), userID)
}

// GetOrder mocks base method.
func (m *MockClient) GetOrder(userID, orderNum string) (Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrder", userID, orderNum)
	ret0, _ := ret[0].(Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrder indicates an expected call of GetOrder.
func (mr *MockClientMockRecorder) GetOrder(userID, orderNum interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrder", reflect.TypeOf((*MockClient)(nil).GetOrder), userID, orderNum)
}

// GetOrders mocks base method.
func (m *MockClient) GetOrders(userID string) (Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrders", userID)
	ret0, _ := ret[0].(Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrders indicates an expected call of GetOrders.
func (mr *MockClientMockRecorder) GetOrders(userID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrders", reflect.TypeOf((*MockClient)(nil).GetOrders), userID)
}

// GetPaymentDecision mocks base method.
func (m *MockClient) GetPaymentDecision(params map[string]string) (Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPaymentDecision", params)
	ret0, _ := ret[0].(Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPaymentDecision indicates an expected call of GetPaymentDecision.
func (mr *MockClientMockRecorder) GetPaymentDecision(params interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPaymentDecision", reflect.TypeOf((*MockClient)(nil).GetPaymentDecision), params)
}

// IsHavingSubscription mocks base method.
func (m *MockClient) IsHavingSubscription(userID string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsHavingSubscription", userID)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsHavingSubscription indicates an expected call of IsHavingSubscription.
func (mr *MockClientMockRecorder) IsHavingSubscription(userID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsHavingSubscription", reflect.TypeOf((*MockClient)(nil).IsHavingSubscription), userID)
}

// IsHavingUnfulfilledOrder mocks base method.
func (m *MockClient) IsHavingUnfulfilledOrder(userID string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsHavingUnfulfilledOrder", userID)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsHavingUnfulfilledOrder indicates an expected call of IsHavingUnfulfilledOrder.
func (mr *MockClientMockRecorder) IsHavingUnfulfilledOrder(userID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsHavingUnfulfilledOrder", reflect.TypeOf((*MockClient)(nil).IsHavingUnfulfilledOrder), userID)
}

// ListProducts mocks base method.
func (m *MockClient) ListProducts() (Products, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListProducts")
	ret0, _ := ret[0].(Products)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListProducts indicates an expected call of ListProducts.
func (mr *MockClientMockRecorder) ListProducts() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListProducts", reflect.TypeOf((*MockClient)(nil).ListProducts))
}

// NotifyTelecomCustomer mocks base method.
func (m *MockClient) NotifyTelecomCustomer(params map[string]string) (Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NotifyTelecomCustomer", params)
	ret0, _ := ret[0].(Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NotifyTelecomCustomer indicates an expected call of NotifyTelecomCustomer.
func (mr *MockClientMockRecorder) NotifyTelecomCustomer(params interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NotifyTelecomCustomer", reflect.TypeOf((*MockClient)(nil).NotifyTelecomCustomer), params)
}

// RedeemCode mocks base method.
func (m *MockClient) RedeemCode(params map[string]string, userID string) (Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RedeemCode", params, userID)
	ret0, _ := ret[0].(Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RedeemCode indicates an expected call of RedeemCode.
func (mr *MockClientMockRecorder) RedeemCode(params, userID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RedeemCode", reflect.TypeOf((*MockClient)(nil).RedeemCode), params, userID)
}

// RedeemEdenredCode mocks base method.
func (m *MockClient) RedeemEdenredCode(userID, code string) (Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RedeemEdenredCode", userID, code)
	ret0, _ := ret[0].(Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RedeemEdenredCode indicates an expected call of RedeemEdenredCode.
func (mr *MockClientMockRecorder) RedeemEdenredCode(userID, code interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RedeemEdenredCode", reflect.TypeOf((*MockClient)(nil).RedeemEdenredCode), userID, code)
}

// UpdateCreditCard mocks base method.
func (m *MockClient) UpdateCreditCard(params map[string]string, UserID string) (Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateCreditCard", params, UserID)
	ret0, _ := ret[0].(Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateCreditCard indicates an expected call of UpdateCreditCard.
func (mr *MockClientMockRecorder) UpdateCreditCard(params, UserID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateCreditCard", reflect.TypeOf((*MockClient)(nil).UpdateCreditCard), params, UserID)
}

// UpdateCustomer mocks base method.
func (m *MockClient) UpdateCustomer(params map[string]string, userID string) (Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateCustomer", params, userID)
	ret0, _ := ret[0].(Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateCustomer indicates an expected call of UpdateCustomer.
func (mr *MockClientMockRecorder) UpdateCustomer(params, userID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateCustomer", reflect.TypeOf((*MockClient)(nil).UpdateCustomer), params, userID)
}
