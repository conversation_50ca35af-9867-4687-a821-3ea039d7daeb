// Code generated by MockGen. DO NOT EDIT.
// Source: jwt.go

// Package auth is a generated GoMock package.
package auth

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	jwt_go_v3 "gopkg.in/dgrijalva/jwt-go.v3"
)

// MockJWTAuth is a mock of JWTAuth interface.
type MockJWTAuth struct {
	ctrl     *gomock.Controller
	recorder *MockJWTAuthMockRecorder
}

// MockJWTAuthMockRecorder is the mock recorder for MockJWTAuth.
type MockJWTAuthMockRecorder struct {
	mock *MockJWTAuth
}

// NewMockJWTAuth creates a new mock instance.
func NewMockJWTAuth(ctrl *gomock.Controller) *MockJWTAuth {
	mock := &MockJWTAuth{ctrl: ctrl}
	mock.recorder = &MockJWTAuthMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockJWTAuth) EXPECT() *MockJWTAuthMockRecorder {
	return m.recorder
}

// GenerateToken mocks base method.
func (m *MockJWTAuth) GenerateToken(claims jwt_go_v3.Claims) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenerateToken", claims)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateToken indicates an expected call of GenerateToken.
func (mr *MockJWTAuthMockRecorder) GenerateToken(claims interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateToken", reflect.TypeOf((*MockJWTAuth)(nil).GenerateToken), claims)
}

// ParseToken mocks base method.
func (m *MockJWTAuth) ParseToken(tokenString string, claims jwt_go_v3.Claims) (*jwt_go_v3.Token, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ParseToken", tokenString, claims)
	ret0, _ := ret[0].(*jwt_go_v3.Token)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ParseToken indicates an expected call of ParseToken.
func (mr *MockJWTAuthMockRecorder) ParseToken(tokenString, claims interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ParseToken", reflect.TypeOf((*MockJWTAuth)(nil).ParseToken), tokenString, claims)
}

// ParseUnverifiedToken mocks base method.
func (m *MockJWTAuth) ParseUnverifiedToken(tokenString string, claims jwt_go_v3.Claims) (*UnverifiedToken, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ParseUnverifiedToken", tokenString, claims)
	ret0, _ := ret[0].(*UnverifiedToken)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ParseUnverifiedToken indicates an expected call of ParseUnverifiedToken.
func (mr *MockJWTAuthMockRecorder) ParseUnverifiedToken(tokenString, claims interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ParseUnverifiedToken", reflect.TypeOf((*MockJWTAuth)(nil).ParseUnverifiedToken), tokenString, claims)
}
