package httpreq

import (
	"net/http"
	"net/url"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestParseQuery(t *testing.T) {

	r := &http.Request{
		URL: &url.URL{
			RawQuery: "a=1&b=2&theme=%E6%88%90%E4%BA%BA",
		},
	}
	query, err := ParseQuery(r)
	assert.NoError(t, err)
	assert.Equal(t, "1", query.Get("a"))
	assert.Equal(t, "2", query.Get("b"))
	assert.Equal(t, "成人", query.Get("theme"))
}
