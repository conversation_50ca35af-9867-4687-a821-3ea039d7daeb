<!DOCTYPE html>
<html>
<head>
    <title>API Documentation Login</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background-color: #f5f5f5;
        }
        .login-container {
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        .login-button {
            background-color: #4285f4;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            text-decoration: none;
            display: inline-block;
            margin-top: 20px;
        }
        .login-button:hover {
            background-color: #357abd;
        }
        h1 {
            color: #333;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <h1>API Documentation</h1>
        <p>Please sign in with your company email to access the documentation.</p>
        <a id="login-button" href="/auth/doc/login?provider=google" class="login-button">Sign in with Google</a>
    </div>
    <script>
        const urlParams = new URLSearchParams(window.location.search);
        const token = urlParams.get('token');
        const exp = urlParams.get('exp');
        const redirect = urlParams.get('redirect');
        if (token && exp) {
            document.cookie = `doc_token=${token}; expires=${new Date(exp * 1000).toUTCString()}; path=/; secure; samesite=lax`;
            window.history.replaceState({}, document.title, window.location.pathname);

            window.location.href = redirect || '/doc/';
        }
    </script>
</body>
</html>
