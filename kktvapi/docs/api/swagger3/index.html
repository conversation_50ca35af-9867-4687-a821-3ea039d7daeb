<!doctype html> <!-- Important: must specify -->
<html>
<head>
    <meta charset="utf-8"> <!-- Important: rapi-doc uses utf8 charecters -->
    <link rel="icon" type="image/png" href="./favicon-32x32.png" sizes="32x32" />
    <style>
        .btn {
            width: 90px;
            height: 32px;
            font-size: 13px;
            background-color: #063ce6;
            color: #ffffff;
            border: none;
            margin: 0 2px;
            border-radius: 2px;
            cursor: pointer;
            outline: none;
        }
    </style>
    <script type="module" src="./rapidoc-min.js"></script>
    <script>
        function getRapiDoc() {
            return document.getElementById("thedoc");
        }

        function switchView() {
            let currRender = getRapiDoc().getAttribute('render-style');
            let newRender = currRender === "read" ? "view" : "read";
            getRapiDoc().setAttribute('render-style', newRender);
        }
    </script>

</head>
<body>
<rapi-doc id="thedoc"
          spec-url="./swagger.yaml"
          show-header="false"
          render-style="read"
          schema-description-expanded="true"
          info-description-headings-in-navbar="true"
          nav-bg-color="#551127"
          bg-color = "#eeeeee"
          text-color = "#000000"
          nav-text-color="#f4f4f4"
          theme="light"
>
    <img slot="nav-logo"
         style="width: 65%"
         src="https://www.kktv.me/static/images/img_kktv_logo.svg"/>
    <div style='display:flex; margin:0 16px; float: right;'>
        <button class='btn' onclick='switchView()'> Switch View</button>
    </div>
</rapi-doc>
</body>
</html>
