allOf:
  - $ref: ./sectionlist-item.yaml
  - type: object
    properties:
      title_list_id:
        type: string
        example: '12345'
      style:
        type: string
        example: airing_title_list
      display_name:
        type: string
        example: 動漫週期表
      items:
        type: array
        items:
          allOf:
            - $ref: ./title-basic-meta.yaml
            - $ref: ./listed-title-meta.yaml
              type: object
              properties:
                airing:
                  $ref: ./airing-title-meta.yaml
    required:
      - title_list_id
      - style
      - display_name
      - items
title: SectionlistItemAiringList
