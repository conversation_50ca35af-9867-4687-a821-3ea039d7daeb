title: WebSectionlistItemRanking
allOf:
  - $ref: ../sectionlist-item.yaml
  - type: object
    properties:
      style:
        type: string
        example: top_rank
      display_name:
        type: string
        example: 本週排行榜
      items:
        type: array
        items:
          allOf:
            - $ref: ../title-basic-meta.yaml
            - $ref: ../listed-title-meta.yaml
            - $ref: ./listed-title-meta.yaml
    required:
      - style
      - display_name
      - items
