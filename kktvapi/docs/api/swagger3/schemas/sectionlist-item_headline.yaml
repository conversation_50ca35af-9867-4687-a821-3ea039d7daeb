title: SectionlistItemHeadline
allOf:
  - $ref: ./sectionlist-item.yaml
  - type: object
    properties:
      style:
        type: string
        example: headline
      expired_at:
        type: integer
        description: timestamp to expire
        example: 1645350887
      items:
        type: array
        items:
          allOf:
            - properties:
                image:
                  type: string
                summary:
                  type: string
                topic:
                  type: string
                  example: 最新集數上架
                display_name:
                  type: string
                  example: 《我家浴缸的二三事》
                dominant_color:
                  type: string
                  example: '#49748e'
                  description: the color code of headline
                deeplink:
                  type: string
                  description: the deeplink
                title_id:
                  type: string
                  example: '01000518'
              required:
                - image
                - display_name
          type: object
    required:
      - style
      - items
