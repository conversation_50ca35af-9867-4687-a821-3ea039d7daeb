package user

type VerifyOTPReq struct {
	Account string `json:"account"`
	OTP     string `json:"otp"`
}

type GenerateOTPReq struct {
	Account string `json:"account" validate:"required,email|e164"`
}

type SignUpReq struct {
	ActionToken    string `json:"action_token" validate:"required"`
	Account        string `json:"account" validate:"required,email|e164"`
	Password       string `json:"password" validate:"required,min=8,max=50,password"`
	RepeatPassword string `json:"repeat_password" validate:"required,eqfield=Password"`
	Source         string `json:"source"`
}

type ResetPasswordReq struct {
	ActionToken    string `json:"action_token" validate:"required"`
	Account        string `json:"account" validate:"required,email|e164"`
	Password       string `json:"password" validate:"required,min=8,max=50,password"`
	RepeatPassword string `json:"repeat_password" validate:"required,eqfield=Password"`
}

type CancelSubscriptionReq struct {
	Reason string `json:"reason" validate:"required"`
}

type setAccountReq struct {
	Account     string `json:"account" validate:"required,email|e164"`
	ActionToken string `json:"action_token" validate:"required"`
}

type updateAccountReq struct {
	Account     string `json:"account" validate:"required,email|e164"`
	ActionToken string `json:"action_token" validate:"required"`
}

type setPasswdReq struct {
	Password       string `json:"password" validate:"required,min=8,max=50,password"`
	RepeatPassword string `json:"repeat_password" validate:"required,min=8,max=50"`
	ActionToken    string `json:"action_token" validate:"required"`
}

type updatePasswdReq struct {
	Password       string `json:"password" validate:"required,min=8,max=50,password"`
	RepeatPassword string `json:"repeat_password" validate:"required,min=8,max=50"`
	ActionToken    string `json:"action_token" validate:"required"`
}

type verifyAccountReq struct {
	Account string `json:"account" validate:"required,email|e164"`
}

type loginReq struct {
	Account  string `json:"account"  validate:"required,email|e164"`
	Password string `json:"password" validate:"required,min=8,max=50,password"`
}

type verifyPasswordResetAccountReq struct {
	Account string `json:"account" validate:"required,email|e164"`
}

type verifyPasswordReq struct {
	Password string `json:"password" validate:"required,min=8,max=50,password"`
}

type submitSignupSurveyReq struct {
	Preferences struct {
		KKTVSource               []string `json:"kktv_source" validate:"required,gt=0"`
		RegistrationReason       []string `json:"registration_reason" validate:"required,gt=0"`
		FrequentVideoPlatform    []string `json:"frequent_video_platform" validate:"required,gt=0"`
		PaidVideoPlatform        []string `json:"paid_video_platform" validate:"required,gt=0"`
		PreferredPaymentMethod   []string `json:"preferred_payment_method" validate:"required,gt=0"`
		PreferredPaymentCycle    []string `json:"preferred_payment_cycle" validate:"required,gt=0"`
		InfluencingPaymentFactor []string `json:"influencing_payment_factor" validate:"required,gt=0"`
		DramaSource              []string `json:"drama_source" validate:"required,gt=0"`
		AnimationSource          []string `json:"animation_source" validate:"required,gt=0"`
		ViewingFrequency         string   `json:"viewing_frequency" validate:"required"`
		InfluencingRatingsFactor []string `json:"influencing_ratings_factor" validate:"required,gt=0,lt=4"`
	} `json:"preferences" validate:"required"`
	UserInfo struct {
		Gender   string `json:"gender" validate:"required,oneof=male female undisclosed"`
		Birthday string `json:"birthday" validate:"required,datetime=2006-01-02"`
		Job      string `json:"job" validate:"required"`
		Nickname string `json:"nickname" validate:"required"`
		Phone    string `json:"phone" validate:"omitempty,e164"`
		Email    string `json:"email" validate:"omitempty,email"`
	} `json:"user_info" validate:"required"`
}
