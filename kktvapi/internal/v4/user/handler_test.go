package user

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"reflect"
	"strings"
	"sync"
	"testing"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/auth"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/deeplink"
	imw "github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/middleware"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/onetimepassword"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/rest"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/user"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/v4/presenter"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/kktverror"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/order"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/permission"
	"github.com/KKTV/kktv-api-v3/pkg/clock"
	"github.com/KKTV/kktv-api-v3/pkg/datetimer"
	"github.com/KKTV/kktv-api-v3/pkg/encrypt"
	"github.com/KKTV/kktv-api-v3/pkg/httpreq"
	"github.com/KKTV/kktv-api-v3/pkg/model/authority"
	"github.com/KKTV/kktv-api-v3/pkg/model/cacheuser"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	modelmw "github.com/KKTV/kktv-api-v3/pkg/model/middleware"
	"github.com/go-zoo/bone"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	"gopkg.in/guregu/null.v3"
)

type HandlerTestSuite struct {
	suite.Suite
	r               *require.Assertions
	ctrl            *gomock.Controller
	app             *bone.Mux
	repo            *user.MockUserRepository
	otpRepo         *onetimepassword.MockOTPRepository
	mockPaymentRepo *user.MockPaymentInfoRepository

	mockService           *MockUserService
	mockOtpService        *MockOTPService
	mockAmplitudeService  *MockAmplitudeService
	mockPermissionService *permission.MockService
	mockClock             *clock.MockClock
	mockLegacyHelper      *MocklegacyHelper
	mockOrderService      *order.MockService
}

var (
	expiredToken = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdWQiOiJra3R2LmNvbSIsImV4cCI6MTYwOTQ1OTIwMCwiaWF0IjoxNjcyMTM3MjQzLCJpc3MiOiJLS1RWIiwianRpIjoiY2VsY2s2cGUwMDgyZ25xY2dtaTAiLCJyb2xlIjoiZ3Vlc3QiLCJzdWIiOiJndWVzdDpLS1RWLWNsaWVudHM6ODQ2NjY0YTktYzNkZC00MGFmLWFiOWYtZDQ4YjRmNDA0MDM1IiwidHlwZSI6ImdlbmVyYWwifQ.c7NbGjqPFvC8NSfuxCZGdZf_iPRS0o1-y9Vqpc7AUJ0"
	validToken   = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdWQiOiJra3R2LmNvbSIsImV4cCI6NDA3MDkwODgwMCwiaWF0IjoxNjcyMTM5NzY0LCJpc3MiOiJLS1RWIiwianRpIjoiY2VsZDd0MWUwMDg0dm02ZGppN2ciLCJyb2xlIjoiZ3Vlc3QiLCJzdWIiOiJndWVzdDpLS1RWLWNsaWVudHM6OWI5MWE5MWEtNGFkZC00ODRmLTg0N2ItNzFiZjI1ZGYxOTFhIiwidHlwZSI6ImdlbmVyYWwifQ.mOaG67eUL9_rHT0IbsriUqGpBP3JsOheulAk5NCBef8"
)

func TestHandlerTestSuite(t *testing.T) {
	suite.Run(t, new(HandlerTestSuite))
}

func (suite *HandlerTestSuite) SetupTest() {
	suite.r = suite.Require()
	suite.app = bone.New()
	suite.ctrl = gomock.NewController(suite.T())
	suite.repo = user.NewMockUserRepository(suite.ctrl)
	suite.otpRepo = onetimepassword.NewMockOTPRepository(suite.ctrl)
	suite.mockPaymentRepo = user.NewMockPaymentInfoRepository(suite.ctrl)
	suite.mockService = NewMockUserService(suite.ctrl)
	suite.mockOtpService = NewMockOTPService(suite.ctrl)
	suite.mockAmplitudeService = NewMockAmplitudeService(suite.ctrl)
	suite.mockPermissionService = permission.NewMockService(suite.ctrl)
	suite.mockOrderService = order.NewMockService(suite.ctrl)
	suite.mockClock = clock.NewMockClock(suite.ctrl)
	suite.mockLegacyHelper = NewMocklegacyHelper(suite.ctrl)

	handler := &Handler{
		repo:              suite.repo,
		otpRepo:           suite.otpRepo,
		userService:       suite.mockService,
		otpService:        suite.mockOtpService,
		amplitudeService:  suite.mockAmplitudeService,
		permissionService: suite.mockPermissionService,
		orderService:      suite.mockOrderService,
		actionToken:       auth.NewActionToken(""),
		clock:             suite.mockClock,
		legacyHelper:      suite.mockLegacyHelper,
		paymentInfoRepo:   suite.mockPaymentRepo,
	}

	suite.app.PostFunc("/v4/users/otp", handler.GenerateOTP)
	suite.app.PostFunc("/v4/users/otp/verify", handler.VerifyOTP)
	suite.app.PutFunc(`/v4/users/me/password/set`, handler.SetPassword)
	suite.app.PutFunc(`/v4/users/me/password`, handler.UpdatePassword)
	suite.app.PostFunc("/v4/users/account/verify", handler.VerifyAccount)
	suite.app.PutFunc("/v4/users/me/account/set", handler.SetAccount)
	suite.app.PutFunc("/v4/users/me/account", handler.UpdateAccount)
	suite.app.PostFunc("/v4/users/sign-up", handler.SignUp)
	suite.app.PostFunc("/v4/users/login", handler.Login)
	suite.app.PutFunc("/v4/users/password", handler.PasswordReset)
	suite.app.PostFunc("/v4/users/password/reset", handler.VerifyPasswordResetAccount)
	suite.app.PostFunc("/v4/users/me/password/verify", handler.VerifyPassword)
	suite.app.GetFunc("/v4/users/me/memberships", handler.GetMyMemberships)
	suite.app.GetFunc("/v4/users/me/service", handler.GetMyProductService)
	suite.app.GetFunc("/v4/users/me", handler.GetMe)
}

func (suite *HandlerTestSuite) TearDownTest() {
	defer suite.ctrl.Finish()
}

func (suite *HandlerTestSuite) TestGenerateOTP() {
	testcases := []struct {
		name   string
		given  func()
		userID string
		body   GenerateOTPReq
		then   func(code int, body []byte)
		token  string
	}{
		{
			name: "given email and return OK WHEN got correct process",
			given: func() {
				suite.mockOtpService.EXPECT().IsExist("guest:KKTV-clients:9b91a91a-4add-484f-847b-71bf25df191a", "<EMAIL>").Return(false)
				suite.mockOtpService.EXPECT().GenerateCode().Return("123456")
				suite.mockOtpService.EXPECT().StoreCodeWithTTL("guest:KKTV-clients:9b91a91a-4add-484f-847b-71bf25df191a", "<EMAIL>", "123456").Return(nil)
				suite.mockOtpService.EXPECT().SendCode("<EMAIL>", "123456").Return(nil)
			},
			body: GenerateOTPReq{
				Account: "<EMAIL>",
			},
			userID: "guest:KKTV-clients:9b91a91a-4add-484f-847b-71bf25df191a",
			then: func(code int, bBytes []byte) {
				var expected float64 = 120
				suite.Equal(http.StatusOK, code)

				var body map[string]interface{}
				_ = json.Unmarshal(bBytes, &body)
				dataObj := body["data"].(map[string]interface{})
				suite.Equal(expected, dataObj["retry_interval"])
			},
			token: validToken,
		},
		{
			name: "given phone and return OK WHEN got correct process",
			given: func() {
				suite.mockOtpService.EXPECT().IsExist("guest:KKTV-clients:9b91a91a-4add-484f-847b-71bf25df191a", "+************").Return(false)
				suite.mockOtpService.EXPECT().GenerateCode().Return("123456")
				suite.mockOtpService.EXPECT().StoreCodeWithTTL("guest:KKTV-clients:9b91a91a-4add-484f-847b-71bf25df191a", "+************", "123456").Return(nil)
				suite.mockOtpService.EXPECT().SendCode("+************", "123456").Return(nil)
			},
			body: GenerateOTPReq{
				Account: "+*************",
			},
			userID: "guest:KKTV-clients:9b91a91a-4add-484f-847b-71bf25df191a",
			then: func(code int, bBytes []byte) {
				var expected float64 = 120
				suite.Equal(http.StatusOK, code)

				var body map[string]interface{}
				_ = json.Unmarshal(bBytes, &body)
				dataObj := body["data"].(map[string]interface{})
				suite.Equal(expected, dataObj["retry_interval"])
			},
			token: validToken,
		},
		{
			name: "return 400 WHEN user already got otp in 120 seconds",
			given: func() {
				suite.mockOtpService.EXPECT().IsExist("guest:KKTV-clients:9b91a91a-4add-484f-847b-71bf25df191a", "<EMAIL>").Return(true)
			},
			body: GenerateOTPReq{
				Account: "<EMAIL>",
			},
			userID: "guest:KKTV-clients:9b91a91a-4add-484f-847b-71bf25df191a",
			then: func(code int, bBytes []byte) {
				suite.Equal(http.StatusBadRequest, code)
			},
			token: validToken,
		},
		{
			name:  "return 400 WHEN given account is phone number but not Taiwan phone number",
			given: func() {},
			body: GenerateOTPReq{
				Account: "+***********",
			},
			userID: "guest:KKTV-clients:9b91a91a-4add-484f-847b-71bf25df191a",
			then: func(code int, bBytes []byte) {
				suite.Equal(http.StatusBadRequest, code)
			},
			token: validToken,
		},
	}
	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			// GIVEN
			tc.given()
			body, _ := json.Marshal(tc.body)
			// WHEN
			req := httptest.NewRequest(http.MethodPost, "/v4/users/otp", bytes.NewReader(body))
			rr := httptest.NewRecorder()
			req.Header.Set("Authorization", tc.token)

			ctx := context.WithValue(req.Context(), "user", model.JwtUser{Sub: tc.userID})
			req = req.WithContext(ctx)
			suite.app.ServeHTTP(rr, req)

			// THEN
			tc.then(rr.Code, rr.Body.Bytes())
		})
	}
}

func (suite *HandlerTestSuite) TestVerifyOTP() {
	var (
		now    = time.Date(2020, 4, 29, 0, 0, 0, 0, datetimer.LocationTaipei)
		userID = "guest:KKTV-clients:9b91a91a-4add-484f-847b-71bf25df191a"
	)

	suite.mockClock.EXPECT().Now().Return(now).AnyTimes()

	testcases := []struct {
		name  string
		given func()
		body  VerifyOTPReq
		then  func(code int, body []byte)
		token string
	}{
		{
			name: "given email THEN return OK and got action token after verify correct otp code",
			given: func() {
				suite.mockOtpService.EXPECT().Verify(userID, "<EMAIL>", "123456").Return(nil)
			},
			body: VerifyOTPReq{
				Account: "<EMAIL>",
				OTP:     "123456",
			},
			then: func(code int, body []byte) {
				suite.Equal(http.StatusOK, code)
				bodyObj := map[string]interface{}{}
				_ = json.Unmarshal(body, &bodyObj)
				dataObj := bodyObj["data"].(map[string]interface{})

				suite.r.NotEmpty(dataObj["action_token"])
				suite.r.EqualValues(now.Add(5*time.Minute).Unix(), dataObj["expired_at"])
			},
			token: validToken,
		},
		{
			name: "given phone THEN return OK and got action token after verify correct otp code",
			given: func() {
				suite.mockOtpService.EXPECT().Verify(userID, "+************", "123456").Return(nil)
			},
			body: VerifyOTPReq{
				Account: "+*************",
				OTP:     "123456",
			},
			then: func(code int, body []byte) {
				suite.Equal(http.StatusOK, code)
				bodyObj := map[string]interface{}{}
				_ = json.Unmarshal(body, &bodyObj)
				dataObj := bodyObj["data"].(map[string]interface{})

				suite.r.NotEmpty(dataObj["action_token"])
				suite.r.EqualValues(now.Add(5*time.Minute).Unix(), dataObj["expired_at"])
			},
			token: validToken,
		},
		{
			name: "return 400 err verify fail WHEN service return ErrVerifyFail",
			given: func() {
				suite.mockOtpService.EXPECT().Verify(userID, "<EMAIL>", "123456").Return(ErrVerifyFail)
			},
			body: VerifyOTPReq{
				Account: "<EMAIL>",
				OTP:     "123456",
			},
			then:  suite.assertBadRequest("400.5"),
			token: validToken,
		},
	}
	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			// GIVEN
			tc.given()
			body, _ := json.Marshal(tc.body)
			// WHEN
			req := httptest.NewRequest(http.MethodPost, "/v4/users/otp/verify", bytes.NewReader(body))
			req.Header.Set("Authorization", tc.token)
			ctx := context.WithValue(req.Context(), "user", model.JwtUser{Sub: userID})
			req = req.WithContext(ctx)
			rr := httptest.NewRecorder()
			suite.app.ServeHTTP(rr, req)
			// THEN
			tc.then(rr.Code, rr.Body.Bytes())
		})
	}
}

func (suite *HandlerTestSuite) TestSetPassword() {
	var (
		passwordReq = func(passwd, repeat, actionToken string) map[string]string {
			return map[string]string{
				"action_token":    actionToken,
				"password":        passwd,
				"repeat_password": repeat,
			}
		}
		userID = "josie"
		ctx    = reflect.TypeOf((*context.Context)(nil)).Elem()
	)

	const (
		actionToken        = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************.DM3bQ9CAgViVOmfzk8uSQ2lYblMDkg7qBl5rNF1XXYs"
		expiredActionToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************.DlvBZr1l8nJSC08Q36GdvKB2urEWMzLYf67OjCX8Bfk"
		invalidActionToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************.NqeyaEakrK1ZXN1KFc4gtH_OtOu1A9tLjWtVfrpdDMs"
	)

	testcases := []struct {
		name    string
		reqBody interface{}
		given   func()
		then    func(code int, body []byte)
	}{
		{
			name:    "given other user's action token THEN return bad request",
			reqBody: passwordReq("new_secret", "new_secret", invalidActionToken),
			given:   func() {},
			then:    suite.assertBadRequest(ErrRespInvalidParameter.Code),
		},
		{
			name:    "given expired action token THEN return bad request",
			reqBody: passwordReq("new_secret", "new_secret", expiredActionToken),
			given:   func() {},
			then:    suite.assertBadRequest(ErrRespInvalidParameter.Code),
		},
		{
			name:    "given valid password THEN update ok",
			reqBody: passwordReq("new_secret", "new_secret", actionToken),
			given: func() {
				suite.mockService.EXPECT().UpdatePassword(gomock.AssignableToTypeOf(ctx), userID, "new_secret", "").Return(nil)
			},
			then: func(code int, bBytes []byte) {
				suite.r.Equal(http.StatusOK, code)
			},
		},
		{
			name:    "given login user does not exist THEN return 404",
			reqBody: passwordReq("new_secret", "new_secret", actionToken),
			given: func() {
				suite.mockService.EXPECT().UpdatePassword(gomock.AssignableToTypeOf(ctx), userID, "new_secret", "").Return(fmt.Errorf("%w: user not found", kktverror.ErrResourceNotFound))
			},
			then: func(code int, bBytes []byte) {
				suite.r.Equal(http.StatusNotFound, code)
			},
		},
		{
			name:    "given mismatched password repeat THEN return bad request",
			reqBody: passwordReq("new_secret", "new_sacret", actionToken),
			given:   func() {},
			then:    suite.assertBadRequest(ErrRespPasswordMismatch.Code),
		},
		{
			name:    "given password less than 8 chars THEN return bad request",
			reqBody: passwordReq("new_se", "new_se", actionToken),
			given:   func() {},
			then:    suite.assertBadRequest(ErrRespPasswordFormat.Code),
		},
	}
	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			tc.given()

			reqBody, _ := json.Marshal(tc.reqBody)
			rr := httptest.NewRecorder()
			req := httptest.NewRequest(http.MethodPut, "/v4/users/me/password/set", bytes.NewReader(reqBody))
			// set user id into context
			reqCtx := context.WithValue(req.Context(), "user", model.JwtUser{Sub: userID})
			req = req.WithContext(reqCtx)
			suite.app.ServeHTTP(rr, req)

			tc.then(rr.Code, rr.Body.Bytes())
		})
	}
}

func (suite *HandlerTestSuite) TestUpdatePassword() {
	var (
		passwordReq = func(passwd, repeat, actionToken string) map[string]string {
			return map[string]string{
				"action_token":    actionToken,
				"password":        passwd,
				"repeat_password": repeat,
			}
		}
		userID = "josie"
		ctx    = reflect.TypeOf((*context.Context)(nil)).Elem()
	)

	const (
		actionToken        = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************._EU_CwNSo4NLBqfD0YHhp-rRhyMPLBKe6w8Nd1GppF0"
		expiredActionToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************._au752AefWHcYJVisZ-jdupG1081j8jWmT2mXTrOdck"
		invalidActionToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************.SP3RZrJCK9RIhnr5dAMvV3BuKjkPS5rBrV3EtV1XHUM"
	)

	testcases := []struct {
		name    string
		reqBody interface{}
		given   func()
		then    func(code int, body []byte)
	}{
		{
			name:    "given other user's action token THEN return bad request",
			reqBody: passwordReq("new_secret", "new_secret", invalidActionToken),
			given:   func() {},
			then:    suite.assertBadRequest(ErrRespInvalidParameter.Code),
		},
		{
			name:    "given expired action token THEN return bad request",
			reqBody: passwordReq("new_secret", "new_secret", expiredActionToken),
			given:   func() {},
			then:    suite.assertBadRequest(ErrRespInvalidParameter.Code),
		},
		{
			name:    "given valid password THEN update ok",
			reqBody: passwordReq("new_secret", "new_secret", actionToken),
			given: func() {
				suite.mockService.EXPECT().UpdatePassword(gomock.AssignableToTypeOf(ctx), userID, "new_secret", "").Return(nil)
			},
			then: func(code int, bBytes []byte) {
				suite.r.Equal(http.StatusOK, code)
			},
		},
		{
			name:    "given login user does not exist THEN return 404",
			reqBody: passwordReq("new_secret", "new_secret", actionToken),
			given: func() {
				suite.mockService.EXPECT().UpdatePassword(gomock.AssignableToTypeOf(ctx), userID, "new_secret", "").Return(fmt.Errorf("%w: user not found", kktverror.ErrResourceNotFound))
			},
			then: func(code int, bBytes []byte) {
				suite.r.Equal(http.StatusNotFound, code)
			},
		},
		{
			name:    "given mismatched password repeat THEN return bad request",
			reqBody: passwordReq("new_secret", "new_sacret", actionToken),
			given:   func() {},
			then:    suite.assertBadRequest(ErrRespPasswordMismatch.Code),
		},
		{
			name:    "given password less than 8 chars THEN return bad request",
			reqBody: passwordReq("new_se", "new_se", actionToken),
			given:   func() {},
			then:    suite.assertBadRequest(ErrRespPasswordFormat.Code),
		},
	}
	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			tc.given()

			reqBody, _ := json.Marshal(tc.reqBody)
			rr := httptest.NewRecorder()
			req := httptest.NewRequest(http.MethodPut, "/v4/users/me/password", bytes.NewReader(reqBody))
			// set user id into context
			reqCtx := context.WithValue(req.Context(), "user", model.JwtUser{Sub: userID})
			req = req.WithContext(reqCtx)
			suite.app.ServeHTTP(rr, req)

			tc.then(rr.Code, rr.Body.Bytes())
		})
	}
}

func (suite *HandlerTestSuite) TestVerifyAccount() {
	type ThenFunc func(statusCode int, respBodyBytes []byte)
	type TestCase struct {
		name    string
		userID  string
		body    interface{}
		given   func()
		then    ThenFunc
		isGuest bool
	}

	reqBody := func(account string) map[string]string {
		return map[string]string{
			"account": account,
		}
	}
	assertOK := func() ThenFunc {
		return func(statusCode int, respBodyBytes []byte) {
			suite.Equal(http.StatusOK, statusCode)
		}
	}

	const (
		userID      = "test-user-id"
		guestUserID = "guest:test-user-id"
		email       = "<EMAIL>"
		phone       = "+************"
	)

	var (
		lowerCaseEmail = strings.ToLower(email)
	)

	testCases := []TestCase{
		{
			name:   "given invalid phone THEN return account format error",
			userID: userID,
			body:   reqBody("**********"),
			given:  func() {},
			then:   suite.assertBadRequest(ErrRespAccountFormat.Code),
		},
		{
			name:   "given taiwan phone with wrong format THEN return account format error",
			userID: userID,
			body:   reqBody("+************"),
			given:  func() {},
			then:   suite.assertBadRequest(ErrRespAccountFormat.Code),
		},
		{
			name:   "given taiwan phone with wrong length THEN return account format error",
			userID: userID,
			body:   reqBody("+*************"),
			given:  func() {},
			then:   suite.assertBadRequest(ErrRespAccountFormat.Code),
		},
		{
			name:   "given invalid email THEN return account format error",
			userID: guestUserID,
			body:   reqBody("gmail.com"),
			given:  func() {},
			then:   suite.assertBadRequest(ErrRespAccountFormat.Code),
		},
		{
			name:   "given used phone THEN return account exists error",
			userID: userID,
			body:   reqBody(phone),
			given: func() {
				suite.mockService.EXPECT().GetUsersByAccount(phone).Return([]*dbuser.User{
					{
						ID:    "other-user-id",
						Phone: null.StringFrom(phone),
					},
				}, nil)
			},
			then: suite.assertBadRequest(ErrRespAccountExists.Code),
		},
		{
			name:   "given used email but belongs to the login user THEN return OK",
			userID: userID,
			body:   reqBody(email),
			given: func() {
				users := []*dbuser.User{
					{
						ID:    userID,
						Email: null.StringFrom(lowerCaseEmail),
					},
				}
				suite.mockService.EXPECT().GetUsersByAccount(lowerCaseEmail).Return(users, nil)
				suite.repo.EXPECT().GetActiveByID(userID).Return(users[0], nil)
				suite.mockService.EXPECT().ValidateAccountChangeable(users[0], lowerCaseEmail).Return(true, nil)
			},
			then: assertOK(),
		},
		{
			name:   "given non-used phone THEN return OK",
			userID: userID,
			body:   reqBody(phone),
			given: func() {
				suite.mockService.EXPECT().GetUsersByAccount(phone).Return([]*dbuser.User{}, nil)
				suite.repo.EXPECT().GetActiveByID(userID).Return(&dbuser.User{}, nil)
				suite.mockService.EXPECT().ValidateAccountChangeable(&dbuser.User{}, phone).Return(true, nil)
			},
			then: assertOK(),
		},
		{
			name:   "given unchangeable account THEN return account unchangeable error",
			userID: userID,
			body:   reqBody(phone),
			given: func() {
				suite.mockService.EXPECT().GetUsersByAccount(phone).Return([]*dbuser.User{}, nil)
				suite.repo.EXPECT().GetActiveByID(userID).Return(&dbuser.User{}, nil)
				suite.mockService.EXPECT().ValidateAccountChangeable(&dbuser.User{}, phone).Return(false, nil)
			},
			then: suite.assertBadRequest(ErrRespAccountUnchangeable.Code),
		},
		{
			name:    "[guest] given used email THEN return account exists error",
			userID:  guestUserID,
			body:    reqBody(email),
			isGuest: true,
			given: func() {
				suite.mockService.EXPECT().GetUsersByAccount(lowerCaseEmail).Return([]*dbuser.User{
					{
						ID:    "other-user-id",
						Email: null.StringFrom(lowerCaseEmail),
					},
				}, nil)
			},
			then: suite.assertBadRequest(ErrRespAccountExists.Code),
		},
		{
			name:    "[guest] given non-used phone THEN return OK",
			userID:  guestUserID,
			body:    reqBody(phone),
			isGuest: true,
			given: func() {
				suite.mockService.EXPECT().GetUsersByAccount(phone).Return([]*dbuser.User{}, nil)
			},
			then: assertOK(),
		},
		{
			name:   "[duplicate accounts] given phone already verified by other user THEN return account exists error",
			userID: userID,
			body:   reqBody(phone),
			given: func() {
				suite.mockService.EXPECT().GetUsersByAccount(phone).Return([]*dbuser.User{
					{
						ID:              "other-user-id",
						Phone:           null.StringFrom(phone),
						PhoneVerifiedAt: null.TimeFrom(time.Now().Add(-time.Hour)),
					},
					{
						ID:    userID,
						Phone: null.StringFrom(phone),
					},
				}, nil)
			},
			then: suite.assertBadRequest(ErrRespAccountExists.Code),
		},
		{
			name:   "[duplicate accounts] given email already verified by login user THEN return OK",
			userID: userID,
			body:   reqBody(email),
			given: func() {
				users := []*dbuser.User{
					{
						ID:              userID,
						Email:           null.StringFrom(lowerCaseEmail),
						EmailVerifiedAt: null.TimeFrom(time.Now().Add(-time.Hour)),
					},
					{
						ID:    "other-user-id",
						Email: null.StringFrom(lowerCaseEmail),
					},
				}
				suite.mockService.EXPECT().GetUsersByAccount(lowerCaseEmail).Return(users, nil)
				suite.repo.EXPECT().GetActiveByID(userID).Return(users[0], nil)
				suite.mockService.EXPECT().ValidateAccountChangeable(users[0], lowerCaseEmail).Return(true, nil)
			},
			then: assertOK(),
		},
		{
			name:   "[duplicate accounts] given unverified phone THEN return OK",
			userID: userID,
			body:   reqBody(phone),
			given: func() {
				users := []*dbuser.User{
					{
						ID:    userID,
						Phone: null.StringFrom(phone),
					},
					{
						ID:    "duplicate-phone-user-id",
						Phone: null.StringFrom(phone),
					},
				}
				suite.mockService.EXPECT().GetUsersByAccount(phone).Return(users, nil)
				suite.repo.EXPECT().GetActiveByID(userID).Return(users[0], nil)
				suite.mockService.EXPECT().ValidateAccountChangeable(users[0], phone).Return(true, nil)
			},
			then: assertOK(),
		},
	}

	for _, tc := range testCases {
		suite.Run(tc.name, func() {
			tc.given()

			body, _ := json.Marshal(tc.body)
			req := httptest.NewRequest(http.MethodPost, "/v4/users/account/verify", bytes.NewReader(body))
			isGuest := tc.isGuest
			accessUser := modelmw.AccessUser{UserID: tc.userID}
			if !isGuest {
				accessUser.Memberships = dbuser.MembershipFreeTrial
			}
			ctx := context.WithValue(req.Context(), "accessuser", accessUser)
			req = req.WithContext(ctx)
			rr := httptest.NewRecorder()
			suite.app.ServeHTTP(rr, req)

			tc.then(rr.Code, rr.Body.Bytes())
		})
	}
}

func (suite *HandlerTestSuite) TestSetAccount() {
	type ThenFunc func(statusCode int, respBodyBytes []byte)
	type TestCase struct {
		name       string
		userID     string
		body       interface{}
		given      func()
		then       ThenFunc
		membership dbuser.Membership
	}

	reqBody := func(account, actionToken string) map[string]string {
		return map[string]string{
			"account":      account,
			"action_token": actionToken,
		}
	}

	assertOK := func() ThenFunc {
		return func(statusCode int, respBodyBytes []byte) {
			suite.Equal(http.StatusOK, statusCode)
		}
	}

	assertNotFound := func() ThenFunc {
		return func(statusCode int, respBodyBytes []byte) {
			suite.r.Equal(http.StatusNotFound, statusCode)
		}
	}

	const (
		userID             = "test-user-id"
		email              = "<EMAIL>"
		phone              = "+*************"
		phoneActionToken   = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************.QOD7RTbctFQMPOadNlof03DSz3v56Avw9L3WTOMxfKY"
		emailActionToken   = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************.8PCDj-as9W3na3fk5oWO5ABiGUxcnxxSZNjjac2ZFMo"
		expiredActionToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************.n3GNBjLoA14xReCUfb3sGhOUsOQiAfw1mv9dCATuAAg"
		invalidActionToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************._5GUZYTxqdpZPXBEEd1WuKVoGp68K9SoWk1P5C8eEiU"
	)

	var (
		lowerCaseEmail = strings.ToLower(email)
		formattedPhone = formatTaiwanPhoneNumber(phone)
		verifiedDate   = time.Now().Add(-time.Hour)
	)

	testCases := []TestCase{
		{
			name:       "given other user's action token THEN return invalid parameter error",
			userID:     userID,
			body:       reqBody(phone, invalidActionToken),
			given:      func() {},
			then:       suite.assertBadRequest(ErrRespAccountUnverified.Code),
			membership: dbuser.MembershipPremiumOnly,
		},
		{
			name:       "given expired action token THEN return invalid parameter error",
			userID:     userID,
			body:       reqBody(email, expiredActionToken),
			given:      func() {},
			then:       suite.assertBadRequest(ErrRespOTPExpired.Code),
			membership: dbuser.MembershipExpired,
		},
		{
			name:   "given non-existent user THEN return not found error",
			userID: userID,
			body:   reqBody(phone, phoneActionToken),
			given: func() {
				suite.mockService.EXPECT().GetUsersByAccount(formattedPhone).Return([]*dbuser.User{}, nil)
				suite.repo.EXPECT().GetActiveByID(userID).Return(nil, nil)
			},
			then:       assertNotFound(),
			membership: dbuser.MembershipExpired,
		},
		{
			name:   "given used email THEN return account exists error",
			userID: userID,
			body:   reqBody(email, emailActionToken),
			given: func() {
				suite.mockService.EXPECT().GetUsersByAccount(lowerCaseEmail).Return([]*dbuser.User{
					{
						ID:    "other-user-id",
						Email: null.StringFrom(email),
					},
				}, nil)
			},
			then:       suite.assertBadRequest(ErrRespAccountExists.Code),
			membership: dbuser.MembershipExpired,
		},
		{
			name:   "given used phone but belongs to the login user THEN return OK",
			userID: userID,
			body:   reqBody(phone, phoneActionToken),
			given: func() {
				users := []*dbuser.User{
					{
						ID:    userID,
						Phone: null.StringFrom(formattedPhone),
					},
				}

				suite.mockService.EXPECT().GetUsersByAccount(formattedPhone).Return(users, nil)
				suite.repo.EXPECT().GetActiveByID(userID).Return(users[0], nil)
				suite.mockService.EXPECT().ValidateAccountChangeable(users[0], formattedPhone).Return(true, nil)
				suite.mockService.EXPECT().UpdateAccount(users[0], formattedPhone).Return(&dbuser.User{}, nil)
				suite.mockAmplitudeService.EXPECT().SendAccountUpdatedEvent("set account", formattedPhone, gomock.AssignableToTypeOf(&dbuser.User{}), gomock.AssignableToTypeOf(&http.Request{}))
			},
			then:       assertOK(),
			membership: dbuser.MembershipExpired,
		},
		{
			name:   "given used and verified email but belongs to the login user THEN return OK",
			userID: userID,
			body:   reqBody(email, emailActionToken),
			given: func() {
				users := []*dbuser.User{
					{
						ID:              userID,
						Email:           null.StringFrom(email),
						EmailVerifiedAt: null.TimeFrom(verifiedDate),
					},
				}

				suite.mockService.EXPECT().GetUsersByAccount(lowerCaseEmail).Return(users, nil)
				suite.repo.EXPECT().GetActiveByID(userID).Return(users[0], nil)
				suite.mockService.EXPECT().ValidateAccountChangeable(users[0], lowerCaseEmail).Return(true, nil)
				suite.mockService.EXPECT().UpdateAccount(users[0], lowerCaseEmail).Return(&dbuser.User{}, nil)
				suite.mockAmplitudeService.EXPECT().SendAccountUpdatedEvent("set account", lowerCaseEmail, gomock.AssignableToTypeOf(&dbuser.User{}), gomock.AssignableToTypeOf(&http.Request{}))
			},
			then:       assertOK(),
			membership: dbuser.MembershipExpired,
		},
		{
			name:   "given unchangeable account THEN return unchangeable account error",
			userID: userID,
			body:   reqBody(email, emailActionToken),
			given: func() {
				users := []*dbuser.User{
					{
						ID:              userID,
						Email:           null.StringFrom(email),
						EmailVerifiedAt: null.TimeFrom(verifiedDate),
					},
				}

				suite.mockService.EXPECT().GetUsersByAccount(lowerCaseEmail).Return(users, nil)
				suite.repo.EXPECT().GetActiveByID(userID).Return(users[0], nil)
				suite.mockService.EXPECT().ValidateAccountChangeable(users[0], lowerCaseEmail).Return(false, nil)
			},
			then:       suite.assertBadRequest(ErrRespAccountUnchangeable.Code),
			membership: dbuser.MembershipExpired,
		},
		{
			name:   "[duplicate accounts] given phone already verified by other user THEN return account exists error",
			userID: userID,
			body:   reqBody(phone, phoneActionToken),
			given: func() {
				suite.mockService.EXPECT().GetUsersByAccount(formattedPhone).Return([]*dbuser.User{
					{
						ID:    userID,
						Phone: null.StringFrom(phone),
					},
					{
						ID:              "other-user-id",
						Phone:           null.StringFrom("**********"),
						PhoneVerifiedAt: null.TimeFrom(verifiedDate),
					},
				}, nil)
			},
			then:       suite.assertBadRequest(ErrRespAccountExists.Code),
			membership: dbuser.MembershipPremiumOnly,
		},
		{
			name:   "[duplicate accounts] given email already verified by login user THEN return OK",
			userID: userID,
			body:   reqBody(email, emailActionToken),
			given: func() {
				users := []*dbuser.User{
					{
						ID:              userID,
						Email:           null.StringFrom(email),
						EmailVerifiedAt: null.TimeFrom(verifiedDate),
					},
					{
						ID:    "other-user-id",
						Email: null.StringFrom(lowerCaseEmail),
					},
				}

				suite.mockService.EXPECT().GetUsersByAccount(lowerCaseEmail).Return(users, nil)
				suite.repo.EXPECT().GetActiveByID(userID).Return(users[0], nil)
				suite.mockService.EXPECT().ValidateAccountChangeable(users[0], lowerCaseEmail).Return(true, nil)
				suite.mockService.EXPECT().UpdateAccount(users[0], lowerCaseEmail).Return(&dbuser.User{}, nil)
				suite.mockAmplitudeService.EXPECT().SendAccountUpdatedEvent("set account", lowerCaseEmail, gomock.AssignableToTypeOf(&dbuser.User{}), gomock.AssignableToTypeOf(&http.Request{}))
			},
			then:       assertOK(),
			membership: dbuser.MembershipPremiumOnly,
		},
		{
			name:   "[duplicate accounts] given unverified phone THEN return OK",
			userID: userID,
			body:   reqBody(phone, phoneActionToken),
			given: func() {
				users := []*dbuser.User{
					{
						ID:    "other-user-id",
						Phone: null.StringFrom(phone),
					},
					{
						ID:    userID,
						Phone: null.StringFrom("**********"),
					},
				}

				suite.mockService.EXPECT().GetUsersByAccount(formattedPhone).Return(users, nil)
				suite.repo.EXPECT().GetActiveByID(userID).Return(users[1], nil)
				suite.mockService.EXPECT().ValidateAccountChangeable(users[1], formattedPhone).Return(true, nil)
				suite.mockService.EXPECT().UpdateAccount(users[1], formattedPhone).Return(&dbuser.User{}, nil)
				suite.mockAmplitudeService.EXPECT().SendAccountUpdatedEvent("set account", formattedPhone, gomock.AssignableToTypeOf(&dbuser.User{}), gomock.AssignableToTypeOf(&http.Request{}))
			},
			then:       assertOK(),
			membership: dbuser.MembershipPremiumOnly,
		},
	}

	for _, tc := range testCases {
		suite.Run(tc.name, func() {
			tc.given()

			body, _ := json.Marshal(tc.body)
			req := httptest.NewRequest(http.MethodPut, "/v4/users/me/account/set", bytes.NewReader(body))
			reqCtx := context.WithValue(req.Context(), "accessuser", modelmw.AccessUser{UserID: tc.userID, Memberships: tc.membership})
			req = req.WithContext(reqCtx)
			rr := httptest.NewRecorder()
			suite.app.ServeHTTP(rr, req)

			tc.then(rr.Code, rr.Body.Bytes())
		})
	}
}

func (suite *HandlerTestSuite) TestUpdateAccount() {
	type ThenFunc func(statusCode int, respBodyBytes []byte)
	type TestCase struct {
		name   string
		userID string
		body   interface{}
		given  func()
		then   ThenFunc
	}

	reqBody := func(account, actionToken string) map[string]string {
		return map[string]string{
			"account":      account,
			"action_token": actionToken,
		}
	}

	assertOK := func() ThenFunc {
		return func(statusCode int, respBodyBytes []byte) {
			suite.Equal(http.StatusOK, statusCode)
		}
	}

	assertNotFound := func() ThenFunc {
		return func(statusCode int, respBodyBytes []byte) {
			suite.r.Equal(http.StatusNotFound, statusCode)
		}
	}

	const (
		userID             = "test-user-id"
		email              = "<EMAIL>"
		phone              = "+*************"
		actionToken        = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************.uZ_vmjzOKu5NT8Ae7Ivt_FnJElzNNanqa5EZM41s0Mg"
		expiredActionToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************.X2BSZrkpui39dt7Gy3gU7mnsAB2v18e93kdmu4-JlWg"
		invalidActionToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************.ubTjYtsasedniL6HMgSrNvlZsztzcNtpPqiDVyO1FN4"
	)

	var (
		lowerCaseEmail  = strings.ToLower(email)
		formattedPhone  = formatTaiwanPhoneNumber(phone)
		verifiedDate    = time.Now().Add(-time.Hour)
		phoneAccountOTP = &cacheuser.AccountOTP{
			Account:    formattedPhone,
			IsVerified: true,
		}
		emailAccountOTP = &cacheuser.AccountOTP{
			Account:    lowerCaseEmail,
			IsVerified: true,
		}
	)

	testCases := []TestCase{
		{
			name:   "given other user's action token THEN return invalid parameter error",
			userID: userID,
			body:   reqBody(phone, invalidActionToken),
			given:  func() {},
			then:   suite.assertBadRequest(ErrRespInvalidParameter.Code),
		},
		{
			name:   "given expired action token THEN return invalid parameter error",
			userID: userID,
			body:   reqBody(email, expiredActionToken),
			given:  func() {},
			then:   suite.assertBadRequest(ErrRespInvalidParameter.Code),
		},
		{
			name:   "given account otp does not exists in cache THEN return otp expired error",
			userID: userID,
			body:   reqBody(phone, actionToken),
			given: func() {
				suite.otpRepo.EXPECT().Get(userID).Return(nil, nil)
			},
			then: suite.assertBadRequest(ErrRespOTPExpired.Code),
		},
		{
			name:   "given unverified account otp THEN return unverified account error",
			userID: userID,
			body:   reqBody(email, actionToken),
			given: func() {
				suite.otpRepo.EXPECT().Get(userID).Return(&cacheuser.AccountOTP{
					Account:    lowerCaseEmail,
					IsVerified: false,
				}, nil)
			},
			then: suite.assertBadRequest(ErrRespAccountUnverified.Code),
		},
		{
			name:   "given non-existent user THEN return not found error",
			userID: userID,
			body:   reqBody(phone, actionToken),
			given: func() {
				suite.otpRepo.EXPECT().Get(userID).Return(phoneAccountOTP, nil)
				suite.mockService.EXPECT().GetUsersByAccount(formattedPhone).Return([]*dbuser.User{}, nil)
				suite.repo.EXPECT().GetActiveByID(userID).Return(nil, nil)
			},
			then: assertNotFound(),
		},
		{
			name:   "given used email THEN return account exists error",
			userID: userID,
			body:   reqBody(email, actionToken),
			given: func() {
				users := []*dbuser.User{
					{
						ID:    "other-user-id",
						Email: null.StringFrom(email),
					},
				}

				suite.otpRepo.EXPECT().Get(userID).Return(emailAccountOTP, nil)
				suite.mockService.EXPECT().GetUsersByAccount(lowerCaseEmail).Return(users, nil)
			},
			then: suite.assertBadRequest(ErrRespAccountExists.Code),
		},
		{
			name:   "given used phone but belongs to the login user THEN return OK",
			userID: userID,
			body:   reqBody(phone, actionToken),
			given: func() {
				users := []*dbuser.User{
					{
						ID:    userID,
						Phone: null.StringFrom(formattedPhone),
					},
				}

				suite.otpRepo.EXPECT().Get(userID).Return(phoneAccountOTP, nil)
				suite.mockService.EXPECT().GetUsersByAccount(formattedPhone).Return(users, nil)
				suite.repo.EXPECT().GetActiveByID(userID).Return(users[0], nil)
				suite.mockService.EXPECT().ValidateAccountChangeable(users[0], formattedPhone).Return(true, nil)
				suite.mockService.EXPECT().UpdateAccount(users[0], formattedPhone).Return(&dbuser.User{}, nil)
				suite.mockAmplitudeService.EXPECT().SendAccountUpdatedEvent("update account", formattedPhone, gomock.AssignableToTypeOf(&dbuser.User{}), gomock.AssignableToTypeOf(&http.Request{}))
			},
			then: assertOK(),
		},
		{
			name:   "given used and verified email but belongs to the login user THEN return OK",
			userID: userID,
			body:   reqBody(email, actionToken),
			given: func() {
				users := []*dbuser.User{
					{
						ID:              userID,
						Email:           null.StringFrom(email),
						EmailVerifiedAt: null.TimeFrom(verifiedDate),
					},
				}

				suite.otpRepo.EXPECT().Get(userID).Return(emailAccountOTP, nil)
				suite.mockService.EXPECT().GetUsersByAccount(lowerCaseEmail).Return(users, nil)
				suite.repo.EXPECT().GetActiveByID(userID).Return(users[0], nil)
				suite.mockService.EXPECT().ValidateAccountChangeable(users[0], lowerCaseEmail).Return(true, nil)
				suite.mockService.EXPECT().UpdateAccount(users[0], lowerCaseEmail).Return(&dbuser.User{}, nil)
				suite.mockAmplitudeService.EXPECT().SendAccountUpdatedEvent("update account", lowerCaseEmail, gomock.AssignableToTypeOf(&dbuser.User{}), gomock.AssignableToTypeOf(&http.Request{}))
			},
			then: assertOK(),
		},
		{
			name:   "given unchangeable account THEN return unchangeable account error",
			userID: userID,
			body:   reqBody(email, actionToken),
			given: func() {
				users := []*dbuser.User{
					{
						ID:              userID,
						Email:           null.StringFrom(email),
						EmailVerifiedAt: null.TimeFrom(verifiedDate),
					},
				}

				suite.otpRepo.EXPECT().Get(userID).Return(emailAccountOTP, nil)
				suite.mockService.EXPECT().GetUsersByAccount(lowerCaseEmail).Return(users, nil)
				suite.repo.EXPECT().GetActiveByID(userID).Return(users[0], nil)
				suite.mockService.EXPECT().ValidateAccountChangeable(users[0], lowerCaseEmail).Return(false, nil)
			},
			then: suite.assertBadRequest(ErrRespAccountUnchangeable.Code),
		},
		{
			name:   "[duplicate accounts] given phone already verified by other user THEN return account exists error",
			userID: userID,
			body:   reqBody(phone, actionToken),
			given: func() {
				users := []*dbuser.User{
					{
						ID:    userID,
						Phone: null.StringFrom(phone),
					},
					{
						ID:              "other-user-id",
						Phone:           null.StringFrom("**********"),
						PhoneVerifiedAt: null.TimeFrom(verifiedDate),
					},
				}

				suite.otpRepo.EXPECT().Get(userID).Return(phoneAccountOTP, nil)
				suite.mockService.EXPECT().GetUsersByAccount(formattedPhone).Return(users, nil)
			},
			then: suite.assertBadRequest(ErrRespAccountExists.Code),
		},
		{
			name:   "[duplicate accounts] given email already verified by login user THEN return OK",
			userID: userID,
			body:   reqBody(email, actionToken),
			given: func() {
				users := []*dbuser.User{
					{
						ID:              userID,
						Email:           null.StringFrom(email),
						EmailVerifiedAt: null.TimeFrom(verifiedDate),
					},
					{
						ID:    "other-user-id",
						Email: null.StringFrom(lowerCaseEmail),
					},
				}

				suite.otpRepo.EXPECT().Get(userID).Return(emailAccountOTP, nil)
				suite.mockService.EXPECT().GetUsersByAccount(lowerCaseEmail).Return(users, nil)
				suite.repo.EXPECT().GetActiveByID(userID).Return(users[0], nil)
				suite.mockService.EXPECT().ValidateAccountChangeable(users[0], lowerCaseEmail).Return(true, nil)
				suite.mockService.EXPECT().UpdateAccount(users[0], lowerCaseEmail).Return(&dbuser.User{}, nil)
				suite.mockAmplitudeService.EXPECT().SendAccountUpdatedEvent("update account", lowerCaseEmail, gomock.AssignableToTypeOf(&dbuser.User{}), gomock.AssignableToTypeOf(&http.Request{}))
			},
			then: assertOK(),
		},
		{
			name:   "[duplicate accounts] given unverified phone THEN return OK",
			userID: userID,
			body:   reqBody(phone, actionToken),
			given: func() {
				users := []*dbuser.User{
					{
						ID:    "other-user-id",
						Phone: null.StringFrom(phone),
					},
					{
						ID:    userID,
						Phone: null.StringFrom("**********"),
					},
				}

				suite.otpRepo.EXPECT().Get(userID).Return(phoneAccountOTP, nil)
				suite.mockService.EXPECT().GetUsersByAccount(formattedPhone).Return(users, nil)
				suite.repo.EXPECT().GetActiveByID(userID).Return(users[1], nil)
				suite.mockService.EXPECT().ValidateAccountChangeable(users[1], formattedPhone).Return(true, nil)
				suite.mockService.EXPECT().UpdateAccount(users[1], formattedPhone).Return(&dbuser.User{}, nil)
				suite.mockAmplitudeService.EXPECT().SendAccountUpdatedEvent("update account", formattedPhone, gomock.AssignableToTypeOf(&dbuser.User{}), gomock.AssignableToTypeOf(&http.Request{}))
			},
			then: assertOK(),
		},
	}

	for _, tc := range testCases {
		suite.Run(tc.name, func() {
			tc.given()

			body, _ := json.Marshal(tc.body)
			req := httptest.NewRequest(http.MethodPut, "/v4/users/me/account", bytes.NewReader(body))
			reqCtx := context.WithValue(req.Context(), "user", model.JwtUser{Sub: tc.userID})
			req = req.WithContext(reqCtx)
			rr := httptest.NewRecorder()
			suite.app.ServeHTTP(rr, req)

			tc.then(rr.Code, rr.Body.Bytes())
		})
	}
}

func (suite *HandlerTestSuite) TestSignUp() {
	const (
		password         = "1234qwer"
		repeatPassword   = "1234qwer"
		email            = "<EMAIL>"
		actionToken      = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************.OPKyLEV0ySsOS-_Xlaou3eMu_ZWuMiMZYx_P_y8Ck-M"
		phone            = "+*************"
		formattedPhone   = "+************"
		phoneActionToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.wQBdXf9xkkln5g6WHXSjY7B2VfrBPqBL_qvNWoc-G5k"
	)
	testcases := []struct {
		name   string
		given  func()
		then   func(code int)
		userID string
		body   SignUpReq
	}{
		{
			name: "Given email and sign up successfully when api receive correct parameter",
			given: func() {
				suite.mockService.EXPECT().Create(email, password, "iOS").Return(nil, nil)
			},
			then: func(code int) {
				suite.Equal(http.StatusOK, code)
			},
			userID: "test-user",
			body: SignUpReq{
				ActionToken:    actionToken,
				Account:        email,
				Password:       password,
				RepeatPassword: repeatPassword,
			},
		},
		{
			name: "Given phone and sign up successfully when api receive correct parameter",
			given: func() {
				suite.mockService.EXPECT().Create(formattedPhone, password, "iOS").Return(nil, nil)
			},
			then: func(code int) {
				suite.Equal(http.StatusOK, code)
			},
			userID: "test-user",
			body: SignUpReq{
				ActionToken:    phoneActionToken,
				Account:        phone,
				Password:       password,
				RepeatPassword: repeatPassword,
			},
		},
		{
			name: "Sign up get 400 when api receive invalid parameter",
			given: func() {
			},
			then: func(code int) {
				suite.Equal(http.StatusBadRequest, code)
			},
			userID: "test-user",
			body: SignUpReq{
				ActionToken:    actionToken,
				Account:        "",
				Password:       "",
				RepeatPassword: "",
			},
		},
		{
			name: "Sign up get 400 when password not equal to repeat_password",
			given: func() {
			},
			then: func(code int) {
				suite.Equal(http.StatusBadRequest, code)
			},
			userID: "test-user",
			body: SignUpReq{
				ActionToken:    actionToken,
				Account:        email,
				Password:       password,
				RepeatPassword: "123455",
			},
		},
	}

	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			tc.given()

			body, _ := json.Marshal(tc.body)
			req := httptest.NewRequest(http.MethodPost, "/v4/users/sign-up", bytes.NewReader(body))
			ctx := context.WithValue(req.Context(), "user", model.JwtUser{Sub: tc.userID})
			req = req.WithContext(ctx)
			req.Header.Set(httpreq.HeaderPlatform, "iOS")
			rr := httptest.NewRecorder()
			suite.app.ServeHTTP(rr, req)

			tc.then(rr.Code)
		})
	}
}

func (suite *HandlerTestSuite) TestLogin() {
	type testCase struct {
		name  string
		body  loginReq
		given func(req *http.Request) *sync.WaitGroup
		then  func(statusCode int, respBody string)
	}

	const (
		email    = "<EMAIL>"
		phone    = "+*************"
		password = "12345qwert"
	)

	var (
		lowerCaseEmail = strings.ToLower(email)
		formattedPhone = formatTaiwanPhoneNumber(phone)
	)

	testCases := []testCase{
		{
			name: "GIVEN non-existent phone THEN return unauthorized",
			body: loginReq{
				Account:  phone,
				Password: password,
			},
			given: func(req *http.Request) *sync.WaitGroup {
				suite.mockService.EXPECT().GetUsersByAccount(formattedPhone).Return([]*dbuser.User{}, nil)
				return nil
			},
			then: func(statusCode int, respBody string) {
				suite.Equal(http.StatusUnauthorized, statusCode)
			},
		},
		{
			name: "GIVEN non-existent email THEN return unauthorized",
			body: loginReq{
				Account:  email,
				Password: password,
			},
			given: func(req *http.Request) *sync.WaitGroup {
				suite.mockService.EXPECT().GetUsersByAccount(lowerCaseEmail).Return([]*dbuser.User{}, nil)
				return nil
			},
			then: func(statusCode int, respBody string) {
				suite.Equal(http.StatusUnauthorized, statusCode)
			},
		},
		{
			name: "GIVEN existed email but incorrect password THEN return unauthorized",
			body: loginReq{
				Account:  email,
				Password: "1234qwer",
			},
			given: func(req *http.Request) *sync.WaitGroup {
				users := []*dbuser.User{
					{
						ID:       "test-uid",
						Email:    null.StringFrom(lowerCaseEmail),
						Password: null.StringFrom(encrypt.GeneratePassword(password)),
					},
				}
				suite.mockService.EXPECT().GetUsersByAccount(lowerCaseEmail).Return(users, nil)
				return nil
			},
			then: func(statusCode int, respBody string) {
				suite.Equal(http.StatusUnauthorized, statusCode)
			},
		},
		{
			name: "GIVEN existed phone and correct password THEN return ok",
			body: loginReq{
				Account:  phone,
				Password: password,
			},
			given: func(req *http.Request) *sync.WaitGroup {
				users := []*dbuser.User{
					{
						ID:       "test-uid",
						Phone:    null.StringFrom(formattedPhone),
						Password: null.StringFrom(encrypt.GeneratePassword(password)),
					},
				}
				now := time.Now()
				dbUserType := gomock.AssignableToTypeOf(&dbuser.User{})
				accessToken := &presenter.AccessToken{
					Token:                 "test-token",
					ExpiredAt:             123,
					RefreshToken:          "test-refresh-token",
					RefreshTokenExpiredAt: 456,
				}

				suite.mockService.EXPECT().GetUsersByAccount(formattedPhone).Return(users, nil)
				suite.mockService.EXPECT().GenerateAccessToken(dbUserType, req).Return(accessToken, nil)
				suite.mockClock.EXPECT().Now().Return(now)
				suite.repo.EXPECT().UpdateByFields(users[0].ID, map[dbuser.UsersField]interface{}{dbuser.UserFieldLastLoginAt: now}).Return(true, nil)

				var wg sync.WaitGroup
				wg.Add(2)
				suite.mockAmplitudeService.EXPECT().SendAccountLoggedInEvent(dbUserType, gomock.AssignableToTypeOf(&http.Request{})).DoAndReturn(func(user *dbuser.User, r *http.Request) {
					wg.Done()
				})
				suite.mockLegacyHelper.EXPECT().bindBillingCustomer(users[0], formattedPhone).DoAndReturn(func(u *dbuser.User, account string) error {
					wg.Done()
					return nil
				})

				return &wg
			},
			then: func(statusCode int, respBody string) {
				suite.Equal(http.StatusOK, statusCode)
				suite.r.JSONEq(`
				{
  					"error": null,
  					"data": {
    					"token": "test-token",
    					"expired_at": 123,
    					"refresh_token": "test-refresh-token",
    					"refresh_token_expired_at": 456
  					}
				}`, respBody)
			},
		},
	}

	for _, tc := range testCases {
		suite.Run(tc.name, func() {
			body, _ := json.Marshal(tc.body)
			req := httptest.NewRequest(http.MethodPost, "/v4/users/login", bytes.NewReader(body))

			wg := tc.given(req)
			rr := httptest.NewRecorder()
			suite.app.ServeHTTP(rr, req)
			tc.then(rr.Code, rr.Body.String())
			if wg != nil {
				wg.Wait()
			}
		})
	}
}

func (suite *HandlerTestSuite) TestPasswordReset() {
	type testCase struct {
		name  string
		body  ResetPasswordReq
		given func()
		then  func(statusCode int)
	}

	const (
		email            = "<EMAIL>"
		password         = "1234qwert"
		actionToken      = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************.OPKyLEV0ySsOS-_Xlaou3eMu_ZWuMiMZYx_P_y8Ck-M"
		userID           = "test-user-id"
		phone            = "+*************"
		formattedPhone   = "+************"
		phoneActionToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.wQBdXf9xkkln5g6WHXSjY7B2VfrBPqBL_qvNWoc-G5k"
	)

	testCases := []testCase{
		{
			name: "given valid body with email and only one verified account return 200",
			body: ResetPasswordReq{
				ActionToken:    actionToken,
				Account:        email,
				Password:       password,
				RepeatPassword: password,
			},
			given: func() {
				users := []*dbuser.User{
					{
						ID:              userID,
						Email:           null.StringFrom(email),
						EmailVerifiedAt: null.TimeFrom(time.Now()),
					},
				}
				suite.mockService.EXPECT().GetUsersByAccount(email).Return(users, nil)
				suite.mockService.EXPECT().ResetPassword(users[0], email, password).Return(&dbuser.User{}, nil)
				suite.mockAmplitudeService.EXPECT().SendAccountUpdatedEvent("reset password", email, gomock.AssignableToTypeOf(&dbuser.User{}), gomock.AssignableToTypeOf(&http.Request{}))
			},
			then: func(statusCode int) {
				suite.Equal(http.StatusOK, statusCode)
			},
		},
		{
			name: "given valid body with phone and only one verified account return 200",
			body: ResetPasswordReq{
				ActionToken:    phoneActionToken,
				Account:        phone,
				Password:       password,
				RepeatPassword: password,
			},
			given: func() {
				users := []*dbuser.User{
					{
						ID:              userID,
						Phone:           null.StringFrom(formattedPhone),
						PhoneVerifiedAt: null.TimeFrom(time.Now()),
					},
				}
				suite.mockService.EXPECT().GetUsersByAccount(formattedPhone).Return(users, nil)
				suite.mockService.EXPECT().ResetPassword(users[0], formattedPhone, password).Return(&dbuser.User{}, nil)
				suite.mockAmplitudeService.EXPECT().SendAccountUpdatedEvent("reset password", formattedPhone, gomock.AssignableToTypeOf(&dbuser.User{}), gomock.AssignableToTypeOf(&http.Request{}))
			},
			then: func(statusCode int) {
				suite.Equal(http.StatusOK, statusCode)
			},
		},
		{
			name: "given different password and repeat password should return 400",
			body: ResetPasswordReq{
				ActionToken:    actionToken,
				Account:        email,
				Password:       password,
				RepeatPassword: "654321",
			},
			given: func() {
			},
			then: func(statusCode int) {
				suite.Equal(http.StatusBadRequest, statusCode)
			},
		},
		{
			name: "given duplicate users and return 400",
			body: ResetPasswordReq{
				ActionToken:    actionToken,
				Account:        email,
				Password:       password,
				RepeatPassword: password,
			},
			given: func() {
				suite.mockService.EXPECT().GetUsersByAccount(email).Return([]*dbuser.User{
					{
						ID: userID,
					},
					{
						ID: "test-user-2",
					},
				}, nil)
			},
			then: func(statusCode int) {
				suite.Equal(http.StatusBadRequest, statusCode)
			},
		},
		{
			name: "given empty users and return 404",
			body: ResetPasswordReq{
				ActionToken:    actionToken,
				Account:        email,
				Password:       password,
				RepeatPassword: password,
			},
			given: func() {
				suite.mockService.EXPECT().GetUsersByAccount(email).Return([]*dbuser.User{}, nil)
			},
			then: func(statusCode int) {
				suite.Equal(http.StatusNotFound, statusCode)
			},
		},
		{
			name: "given valid body and two accounts that have the same email and verify one of them return 200",
			body: ResetPasswordReq{
				ActionToken:    actionToken,
				Account:        email,
				Password:       password,
				RepeatPassword: password,
			},
			given: func() {
				users := []*dbuser.User{
					{
						ID:              userID,
						Email:           null.StringFrom(email),
						EmailVerifiedAt: null.TimeFrom(time.Now()),
					},
					{
						ID:    "test-user-id02",
						Email: null.StringFrom(email),
					},
				}
				suite.mockService.EXPECT().GetUsersByAccount(email).Return(users, nil)
				suite.mockService.EXPECT().ResetPassword(users[0], email, password).Return(&dbuser.User{}, nil)
				suite.mockAmplitudeService.EXPECT().SendAccountUpdatedEvent("reset password", email, gomock.AssignableToTypeOf(&dbuser.User{}), gomock.AssignableToTypeOf(&http.Request{}))
			},
			then: func(statusCode int) {
				suite.Equal(http.StatusOK, statusCode)
			},
		},
		{
			name: "given valid body and one account that isn't verified, and then return 200",
			body: ResetPasswordReq{
				ActionToken:    actionToken,
				Account:        email,
				Password:       password,
				RepeatPassword: password,
			},
			given: func() {
				users := []*dbuser.User{
					{
						ID:    userID,
						Email: null.StringFrom(email),
					},
				}
				suite.mockService.EXPECT().GetUsersByAccount(email).Return(users, nil)
				suite.mockService.EXPECT().ResetPassword(users[0], email, password).Return(&dbuser.User{}, nil)
				suite.mockAmplitudeService.EXPECT().SendAccountUpdatedEvent("reset password", email, gomock.AssignableToTypeOf(&dbuser.User{}), gomock.AssignableToTypeOf(&http.Request{}))
			},
			then: func(statusCode int) {
				suite.Equal(http.StatusOK, statusCode)
			},
		},
	}

	for _, tc := range testCases {
		suite.Run(tc.name, func() {
			tc.given()
			body, _ := json.Marshal(tc.body)
			req := httptest.NewRequest(http.MethodPut, "/v4/users/password", bytes.NewReader(body))
			rr := httptest.NewRecorder()
			suite.app.ServeHTTP(rr, req)
			tc.then(rr.Code)
		})
	}
}

func (suite *HandlerTestSuite) TestVerifyPasswordResetAccount() {
	type testcase struct {
		name  string
		body  map[string]string
		given func()
		then  func(statusCode int, respBodyBytes []byte)
	}

	reqBody := func(account string) map[string]string {
		return map[string]string{
			"account": account,
		}
	}

	const (
		email = "<EMAIL>"
		phone = "+*************"
	)

	lowercaseEmail := strings.ToLower(email)
	formattedPhone := formatTaiwanPhoneNumber(phone)

	testcases := []testcase{
		{
			name:  "input a non-e164 formatted phone THEN return BAD REQUEST with error code 400.2",
			body:  reqBody("**********"),
			given: func() {},
			then:  suite.assertBadRequest(ErrRespAccountFormat.Code),
		},
		{
			name:  "input a taiwan phone with wrong format THEN return BAD REQUEST with error code 400.2",
			body:  reqBody("+************"),
			given: func() {},
			then:  suite.assertBadRequest(ErrRespAccountFormat.Code),
		},
		{
			name:  "input a taiwan phone with wrong length THEN return BAD REQUEST with error code 400.2",
			body:  reqBody("+*************"),
			given: func() {},
			then:  suite.assertBadRequest(ErrRespAccountFormat.Code),
		},
		{
			name:  "input an invalid email THEN return BAD REQUEST with error code 400.2",
			body:  reqBody("gmail.com"),
			given: func() {},
			then:  suite.assertBadRequest(ErrRespAccountFormat.Code),
		},
		{
			name: "input a valid phone but found more than one user THEN return BAD REQUEST with error code 400.10",
			body: reqBody(phone),
			given: func() {
				suite.mockService.EXPECT().GetUsersByAccount(formattedPhone).Return([]*dbuser.User{
					{
						ID:    "test-user-id-1",
						Phone: null.StringFrom(formattedPhone),
					},
					{
						ID:    "test-user-id-2",
						Phone: null.StringFrom("**********"),
					},
				}, nil)
			},
			then: func(statusCode int, respBodyBytes []byte) {
				suite.assertBadRequest(ErrRespAccountDuplicate.Code)
			},
		},
		{
			name: "input a valid email THEN return NOT FOUND",
			body: reqBody(email),
			given: func() {
				suite.mockService.EXPECT().GetUsersByAccount(lowercaseEmail).Return([]*dbuser.User{}, nil)
			},
			then: func(statusCode int, respBodyBytes []byte) {
				suite.Equal(http.StatusNotFound, statusCode)
			},
		},
		{
			name: "input a valid phone THEN return OK",
			body: reqBody(phone),
			given: func() {
				suite.mockService.EXPECT().GetUsersByAccount(formattedPhone).Return([]*dbuser.User{
					{
						ID:    "test-user-id",
						Phone: null.StringFrom(formattedPhone),
					},
				}, nil)
			},
			then: func(statusCode int, respBodyBytes []byte) {
				suite.Equal(http.StatusOK, statusCode)
			},
		},
	}

	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			tc.given()

			body, _ := json.Marshal(tc.body)
			req := httptest.NewRequest(http.MethodPost, "/v4/users/password/reset", bytes.NewReader(body))
			rr := httptest.NewRecorder()
			suite.app.ServeHTTP(rr, req)

			tc.then(rr.Code, rr.Body.Bytes())
		})
	}

}

func (suite *HandlerTestSuite) TestVerifyPassword() {
	type testcase struct {
		name   string
		userID string
		body   map[string]string
		given  func()
		then   func(statusCode int, respBodyBytes []byte)
	}

	const (
		uid = "test-user-id"
		pwd = "1234qwer"
	)

	hashPwd := encrypt.GeneratePassword(pwd)
	reqBody := func(pwd string) map[string]string {
		return map[string]string{
			"password": pwd,
		}
	}

	testcases := []testcase{
		{
			name:   "given non-login user THEN return unauthorized error",
			userID: "guest:KKTV-clients:test-user-id",
			body:   reqBody(pwd),
			given:  func() {},
			then: func(statusCode int, respBodyBytes []byte) {
				suite.Equal(http.StatusUnauthorized, statusCode)
			},
		},
		{
			name:   "given invalid password THEN return password format error",
			userID: uid,
			body:   reqBody("1234qwer "),
			given:  func() {},
			then:   suite.assertBadRequest(ErrRespPasswordFormat.Code),
		},
		{
			name:   "given non-existent user THEN return not found error",
			userID: uid,
			body:   reqBody(pwd),
			given: func() {
				suite.repo.EXPECT().GetActiveByID(uid).Return(nil, nil)
			},
			then: func(statusCode int, respBodyBytes []byte) {
				suite.Equal(http.StatusNotFound, statusCode)
			},
		},
		{
			name:   "given incorrect password THEN return wrong password error",
			userID: uid,
			body:   reqBody("12345qwert"),
			given: func() {
				suite.repo.EXPECT().GetActiveByID(uid).Return(&dbuser.User{
					ID:       uid,
					Password: null.StringFrom(hashPwd),
				}, nil)
			},
			then: suite.assertBadRequest(ErrRespWrongPassword.Code),
		},
		{
			name:   "given correct password THEN return OK",
			userID: uid,
			body:   reqBody(pwd),
			given: func() {
				suite.repo.EXPECT().GetActiveByID(uid).Return(&dbuser.User{
					ID:       uid,
					Password: null.StringFrom(hashPwd),
				}, nil)
				suite.mockClock.EXPECT().Now().Return(time.Now())
			},
			then: func(statusCode int, respBodyBytes []byte) {
				body := map[string]interface{}{}
				_ = json.Unmarshal(respBodyBytes, &body)
				data := body["data"].(map[string]interface{})
				suite.r.NotEmpty(data["action_token"])
				suite.Equal(http.StatusOK, statusCode)
			},
		},
	}

	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			tc.given()
			body, _ := json.Marshal(tc.body)
			req := httptest.NewRequest(http.MethodPost, "/v4/users/me/password/verify", bytes.NewReader(body))
			ctx := context.WithValue(req.Context(), "user", model.JwtUser{Sub: tc.userID})
			req = req.WithContext(ctx)
			rr := httptest.NewRecorder()
			suite.app.ServeHTTP(rr, req)
			tc.then(rr.Code, rr.Body.Bytes())
		})
	}
}

func (suite *HandlerTestSuite) TestGetMyMemberships() {
	var (
		pastDate = time.Date(2020, 1, 6, 0, 0, 0, 0, datetimer.LocationTaipei)
		now      = time.Date(2020, 4, 29, 0, 0, 0, 0, datetimer.LocationTaipei)
	)

	assertGotMembership := func(statusCode int, respBodyBytes []byte, membershipResp getMembershipsResp) {
		suite.Equal(http.StatusOK, statusCode)
		body := struct {
			Error rest.Err           `json:"error"`
			Data  getMembershipsResp `json:"data"`
		}{}
		suite.NoError(json.Unmarshal(respBodyBytes, &body))
		suite.Equal(membershipResp, body.Data)
	}

	suite.mockClock.EXPECT().Now().Return(now).AnyTimes()

	const userID = "user-id"
	testcases := []struct {
		name             string
		accessMembership dbuser.Membership
		given            func()
		then             func(statusCode int, respBodyBytes []byte)
	}{
		{
			name:             "guest GOT guest membership",
			accessMembership: dbuser.Membership{},
			given: func() {
				suite.mockListAuthoritiesForGuest()
			},
			then: func(statusCode int, respBodyBytes []byte) {
				assertGotMembership(statusCode, respBodyBytes,
					getMembershipsResp{
						Memberships: presenter.Memberships{},
						AccountStatus: accountStatus{
							IsMember:             false,
							IsFreeTrial:          false,
							IsHavingSubscription: false,
							IsFreemium:           true,
							SubscribePromotion: &subscribePromotion{
								PromoteType: PromoteTypeSignup,
							},
						},
						Authorities: []string{},
					})
			},
		},
		{
			name:             "pure FreeTrial user, SHOULD got promotion",
			accessMembership: dbuser.MembershipFreeTrial,
			given: func() {
				u := suite.mockUserRepoFoundUser(userID, dbuser.MembershipFreeTrial)
				suite.mockListAuthoritiesForMember(u, authority.FreeTrialPlay, authority.ProductPackageGeneralPurchase)
			},
			then: func(statusCode int, respBodyBytes []byte) {
				assertGotMembership(statusCode, respBodyBytes,
					getMembershipsResp{
						Memberships: presenter.Memberships{{Role: dbuser.MemberRoleFreeTrial.String()}},
						AccountStatus: accountStatus{
							IsMember:             true,
							IsFreeTrial:          true,
							IsFreemium:           false,
							IsHavingSubscription: false,
							SubscribePromotion: &subscribePromotion{
								PromoteType: PromoteTypeUpgrade,
							},
						},
						Authorities: []string{authority.FreeTrialPlay.String(), authority.ProductPackageGeneralPurchase.String()},
					})
			},
		},
		{
			name:             "FreeTrial user with unfulfilled order of recurring product",
			accessMembership: dbuser.MembershipFreeTrial,
			given: func() {
				u := suite.mockUserRepoFoundUser(userID, dbuser.MembershipFreeTrial)
				suite.mockListAuthoritiesForMember(u, authority.FreeTrialPlay)
			},
			then: func(statusCode int, respBodyBytes []byte) {
				assertGotMembership(statusCode, respBodyBytes,
					getMembershipsResp{
						Memberships: presenter.Memberships{
							{Role: dbuser.MemberRoleFreeTrial.String()},
						},
						AccountStatus: accountStatus{
							IsMember:             true,
							IsFreeTrial:          true,
							IsFreemium:           false,
							IsHavingSubscription: true,
							SubscribePromotion:   nil, // should not show promotion when freeTrial user having subscription
						},
						Authorities: []string{authority.FreeTrialPlay.String()},
					})
			},
		},
		{
			name:             "Premium user with auto-renew, SHOULD got IsHavingSubscription=true, AND no promotion",
			accessMembership: dbuser.MembershipPremiumOnly,
			given: func() {
				u := suite.mockUserRepoFoundUser(userID, dbuser.MembershipPremiumOnly, withAutoRenew(true))
				suite.mockListAuthoritiesForMember(u, authority.PremiumPlay, authority.FreeTrialPlay)
			},
			then: func(statusCode int, respBodyBytes []byte) {
				assertGotMembership(statusCode, respBodyBytes,
					getMembershipsResp{
						Memberships: presenter.Memberships{
							{Role: dbuser.MemberRolePremium.String()},
						},
						AccountStatus: accountStatus{
							IsMember:             true,
							IsFreeTrial:          false,
							IsFreemium:           false,
							IsHavingSubscription: true,
						},
						Authorities: []string{
							authority.PremiumPlay.String(),
							authority.FreeTrialPlay.String(),
						},
					})
			},
		},
		{
			name:             "Expired user",
			accessMembership: dbuser.MembershipExpired,
			given: func() {
				u := suite.mockUserRepoFoundUser(userID, dbuser.MembershipExpired, withExpiredAt(pastDate))
				suite.mockListAuthoritiesForMember(u)
			},
			then: func(statusCode int, respBodyBytes []byte) {
				assertGotMembership(statusCode, respBodyBytes,
					getMembershipsResp{
						Memberships: presenter.Memberships{
							{Role: dbuser.MemberRoleExpired.String()},
						},
						AccountStatus: accountStatus{
							IsMember:             true,
							IsFreeTrial:          false,
							IsFreemium:           true,
							IsHavingSubscription: false,
							SubscribePromotion: &subscribePromotion{
								PromoteType: PromoteTypeUpgrade,
							},
							NeedCheckPayment: false,
						},
						Authorities: []string{},
					})
			},
		},
		{
			name:             "Expired user who is in Billing's grace period, SHOULD have need_check_payment=true",
			accessMembership: dbuser.MembershipExpired,
			given: func() {
				yesterday := now.AddDate(0, 0, -1)
				u := suite.mockUserRepoFoundUser(userID, dbuser.MembershipExpired, withExpiredAt(yesterday))
				suite.mockListAuthoritiesForMember(u)

				suite.mockOrderService.EXPECT().GetUserOrderStatus(userID).Return(&order.StatusInfo{
					GracePeriod: &order.GracePeriod{
						StartAt: yesterday,
						EndAt:   yesterday.AddDate(0, 0, 7),
						OrderID: "************",
					},
				}, nil)
			},
			then: func(statusCode int, respBodyBytes []byte) {
				assertGotMembership(statusCode, respBodyBytes,
					getMembershipsResp{
						Memberships: presenter.Memberships{
							{Role: dbuser.MemberRoleExpired.String()},
						},
						AccountStatus: accountStatus{
							IsMember:             true,
							IsFreeTrial:          false,
							IsFreemium:           true,
							IsHavingSubscription: false,
							SubscribePromotion: &subscribePromotion{
								PromoteType: PromoteTypeUpgrade,
							},
							NeedCheckPayment: true,
						},
						Authorities: []string{},
					})
			},
		},
		{
			name:             "Using a Premium token to access BUT actually expired user",
			accessMembership: dbuser.MembershipPremiumOnly,
			given: func() {
				u := suite.mockUserRepoFoundUser(userID, dbuser.MembershipExpired, withExpiredAt(pastDate))
				suite.mockListAuthoritiesForMember(u)
			},
			then: func(statusCode int, respBodyBytes []byte) {
				assertGotMembership(statusCode, respBodyBytes,
					getMembershipsResp{
						Memberships: presenter.Memberships{
							{Role: dbuser.MemberRoleExpired.String()},
						},
						AccountStatus: accountStatus{
							IsMember:             true,
							IsFreeTrial:          false,
							IsFreemium:           true,
							IsHavingSubscription: false,
							SubscribePromotion: &subscribePromotion{
								PromoteType: PromoteTypeUpgrade,
							},
						},
						Authorities: []string{},
					})
			},
		},
		{
			name:             "Premium user without PackagePurchaseAuthorities, SHOULD got IsHavingSubscription=true, AND no promotion",
			accessMembership: dbuser.MembershipPremiumOnly,
			given: func() {
				u := suite.mockUserRepoFoundUser(userID, dbuser.MembershipPremiumOnly)
				suite.mockListAuthoritiesForMember(u, authority.PremiumPlay, authority.FreeTrialPlay)
			},
			then: func(statusCode int, respBodyBytes []byte) {
				assertGotMembership(statusCode, respBodyBytes,
					getMembershipsResp{
						Memberships: presenter.Memberships{
							{Role: dbuser.MemberRolePremium.String()},
						},
						AccountStatus: accountStatus{
							IsMember:             true,
							IsFreeTrial:          false,
							IsFreemium:           false,
							IsHavingSubscription: true,
						},
						Authorities: []string{
							authority.PremiumPlay.String(),
							authority.FreeTrialPlay.String(),
						},
					})
			},
		},
	}

	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			tc.given()
			req := httptest.NewRequest(http.MethodGet, "/v4/users/me/memberships", nil)
			ctx := context.WithValue(req.Context(), imw.KeyAccessUser,
				modelmw.AccessUser{
					UserID:      userID,
					Memberships: tc.accessMembership,
				})
			req = req.WithContext(ctx)
			rr := httptest.NewRecorder()
			suite.app.ServeHTTP(rr, req)
			tc.then(rr.Code, rr.Body.Bytes())
		})
	}
}

func (suite *HandlerTestSuite) TestGetMyProductService() {
	const (
		userID = "josie"
	)
	var (
		now = time.Date(2024, 4, 29, 1, 6, 0, 0, datetimer.LocationTaipei)
	)

	type resp struct {
		Err  *rest.Err `json:"error"`
		Data *myServiceResp
	}

	type testCase struct {
		name  string
		given func()
		then  func(code int, bBytes []byte)
	}
	testCases := []testCase{
		{
			name: "GIVEN user has payment info",
			given: func() {
				suite.mockPaymentRepo.EXPECT().GetByUserID(userID).Return(&dbuser.PaymentInfo{
					PaymentType: null.StringFrom(dbuser.PaymentInfoTypeCreditCard.String()),
				}, nil)
				suite.mockOrderService.EXPECT().GetUserOrderStatus(userID).Return(&order.StatusInfo{}, nil)
			},
			then: func(code int, bBytes []byte) {
				suite.Equal(http.StatusOK, code)
				var body resp
				suite.NoError(json.Unmarshal(bBytes, &body))
				myService := body.Data

				suite.Equal("credit_card", myService.Payment.Type)
				suite.Equal(dbuser.PaymentStatusPaid.String(), myService.Payment.Status)
			},
		},
		{
			name: "GIVEN user are in grace period",
			given: func() {
				suite.mockPaymentRepo.EXPECT().GetByUserID(userID).Return(&dbuser.PaymentInfo{
					PaymentType: null.StringFrom(dbuser.PaymentInfoTypeCreditCard.String()),
				}, nil)
				suite.mockOrderService.EXPECT().GetUserOrderStatus(userID).Return(&order.StatusInfo{
					GracePeriod: &order.GracePeriod{
						StartAt: now.AddDate(0, 0, -1),
						EndAt:   now.AddDate(0, 0, 7),
						OrderID: "************",
					},
				}, nil)
			},
			then: func(code int, bBytes []byte) {
				suite.Equal(http.StatusOK, code)
				var body resp
				suite.NoError(json.Unmarshal(bBytes, &body))
				myService := body.Data

				suite.Equal("credit_card", myService.Payment.Type)
				suite.Equal(dbuser.PaymentStatusFailed.String(), myService.Payment.Status)
				suite.Equal(deeplink.MyAccountPage(), myService.GracePeriod.CtaLink)
				suite.True(now.AddDate(0, 0, -1).Equal(myService.GracePeriod.Start.Time()))
				suite.True(now.AddDate(0, 0, 7).Equal(myService.GracePeriod.End.Time()))
				suite.Equal("************", myService.GracePeriod.OrderID)

			},
		},
	}

	for _, tc := range testCases {
		suite.Run(tc.name, func() {
			tc.given()

			req := httptest.NewRequest(http.MethodGet, "/v4/users/me/service", nil)
			ctx := context.WithValue(req.Context(), imw.KeyAccessUser,
				modelmw.AccessUser{
					UserID: userID,
				})
			req = req.WithContext(ctx)
			rr := httptest.NewRecorder()
			suite.app.ServeHTTP(rr, req)
			tc.then(rr.Code, rr.Body.Bytes())
		})
	}
}

func (suite *HandlerTestSuite) mockListAuthoritiesForMember(user *dbuser.User, authorities ...authority.Authority) *gomock.Call {
	return suite.mockPermissionService.EXPECT().ListMemberAuthorities(user).Return(authorities, nil)
}

func (suite *HandlerTestSuite) mockListAuthoritiesForGuest(authorities ...authority.Authority) *gomock.Call {
	return suite.mockPermissionService.EXPECT().ListGuestAuthorities().Return(authorities)
}

type userOpt func(*dbuser.User)

func withAutoRenew(autoRenew bool) userOpt {
	return func(u *dbuser.User) {
		u.AutoRenew = autoRenew
	}
}
func withExpiredAt(date time.Time) userOpt {
	return func(u *dbuser.User) {
		u.ExpiredAt = null.TimeFrom(date)
	}
}

func (suite *HandlerTestSuite) mockUserRepoFoundUser(userID string, membership dbuser.Membership, opts ...userOpt) *dbuser.User {
	//[BEGIN] TODO remove after membership is ready
	role := membership[0].Role.String()
	//[END]
	u := &dbuser.User{
		ID:         userID,
		Membership: membership,
		Role:       role, Type: dbuser.TypeGeneral.String(),
		ExpiredAt: null.TimeFrom(time.Date(2020, 4, 29, 12, 0, 0, 0, datetimer.LocationTaipei)),
	}
	for _, opt := range opts {
		opt(u)
	}
	suite.repo.EXPECT().GetActiveByID(userID).Return(u, nil)
	return u
}

func (suite *HandlerTestSuite) TestGetMe() {
	testcases := []struct {
		name   string
		userID string
		given  func()
		then   func(statusCode int, respBodyBytes []byte)
	}{
		{
			name:   "should return user info from context",
			userID: "user-id-123",
			given: func() {
				// Mock the user repository to return a user
				u := &dbuser.User{
					ID:    "user-id-123",
					Email: null.StringFrom("<EMAIL>"),
					Phone: null.StringFrom("**********"),
				}
				suite.repo.EXPECT().GetActiveByID("user-id-123").Return(u, nil)
			},
			then: func(statusCode int, respBodyBytes []byte) {
				suite.Equal(http.StatusOK, statusCode)

				var resp struct {
					Error rest.Err `json:"error"`
					Data  struct {
						ID    string `json:"id"`
						Email string `json:"email"`
						Phone string `json:"phone"`
					} `json:"data"`
				}

				suite.NoError(json.Unmarshal(respBodyBytes, &resp))
				suite.Equal("user-id-123", resp.Data.ID)
				suite.Equal("<EMAIL>", resp.Data.Email)
				suite.Equal("**********", resp.Data.Phone)
			},
		},
		{
			name:   "should return 404 when user not found",
			userID: "non-existent-user",
			given: func() {
				// Mock the user repository to return nil (user not found)
				suite.repo.EXPECT().GetActiveByID("non-existent-user").Return(nil, nil)
			},
			then: func(statusCode int, respBodyBytes []byte) {
				suite.Equal(http.StatusNotFound, statusCode)

				var resp struct {
					Error rest.Err `json:"error"`
				}
				suite.NoError(json.Unmarshal(respBodyBytes, &resp))
				suite.Equal(ErrRespUserNotFound.Code, resp.Error.Code)
			},
		},
		{
			name:   "should return 500 when repository returns error",
			userID: "error-user",
			given: func() {
				// Mock the user repository to return an error
				suite.repo.EXPECT().GetActiveByID("error-user").Return(nil, fmt.Errorf("database error"))
			},
			then: func(statusCode int, respBodyBytes []byte) {
				suite.Equal(http.StatusInternalServerError, statusCode)

				var resp struct {
					Error rest.Err `json:"error"`
				}
				suite.NoError(json.Unmarshal(respBodyBytes, &resp))
				suite.Equal(ErrRespUnknown.Code, resp.Error.Code)
			},
		},
	}

	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			if tc.given != nil {
				tc.given()
			}

			req := httptest.NewRequest(http.MethodGet, "/v4/users/me", nil)
			// Set up the context with the AccessUser
			accessUser := modelmw.AccessUser{
				UserID: tc.userID,
			}
			ctx := context.WithValue(req.Context(), imw.KeyAccessUser, accessUser)
			req = req.WithContext(ctx)

			rr := httptest.NewRecorder()
			suite.app.ServeHTTP(rr, req)

			tc.then(rr.Code, rr.Body.Bytes())
		})
	}
}

func (suite *HandlerTestSuite) assertBadRequest(errCode string) func(code int, bBytes []byte) {
	return func(code int, bBytes []byte) {
		suite.Equal(http.StatusBadRequest, code)
		var body map[string]interface{}
		_ = json.Unmarshal(bBytes, &body)

		errObj := body["error"].(map[string]interface{})
		suite.r.Equal(errCode, errObj["code"])
	}
}
