//go:generate mockgen -source service.go -destination service_mock.go -package search
package search

import (
	"fmt"
	"sync"

	legacySearch "github.com/KKTV/kktv-api-v3/kkapp/kksearch"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/KKTV/kktv-api-v3/pkg/clock"
	"github.com/KKTV/kktv-api-v3/pkg/key"
	legacymeta "github.com/KKTV/kktv-api-v3/pkg/model/dbmeta/legacy"
)

var (
	srv     Service
	onceSrv sync.Once
)

type Service interface {
	SearchTitle(keyword string, hideLustContent bool) ([]legacymeta.TitleDetail, error)
	SearchFigure(keyword string, hideLustContent bool) ([]Figure, error)
	SaveHistory(userID string, keyword string) error
}

type service struct {
	clock           clock.Clock
	userCacheWriter cache.Cacher
}

func NewService() Service {
	onceSrv.Do(func() {
		srv = &service{
			clock:           clock.New(),
			userCacheWriter: cache.New(container.CachePoolUser().Master()),
		}
	})
	return srv
}

// SearchTitle returns title details that match the keyword.
func (s *service) SearchTitle(keyword string, hideLustContent bool) ([]legacymeta.TitleDetail, error) {
	// TODO: for now, in order to renew the v4 search API ASAP, so that just reuse the legacy search code.
	//  should implement a new code and not to use the legacy code when we have time
	legacyTitle, err := legacySearch.SearchForTitle(keyword, hideLustContent)
	if err != nil {
		return nil, err
	}
	titleDetails := make([]legacymeta.TitleDetail, len(legacyTitle))
	for i, t := range legacyTitle {
		titleDetails[i] = legacymeta.TitleDetail{LegacyTitleDetail: t}
	}
	return titleDetails, nil
}

// SearchFigure returns figure that match the keyword.
func (s *service) SearchFigure(keyword string, hideLustContent bool) ([]Figure, error) {
	// TODO: for now, in order to renew the v4 search API ASAP, so that just reuse the legacy search code.
	//  should implement a new code and not to use the legacy code when we have time
	legacyFigures, err := legacySearch.SearchForFigure(keyword, hideLustContent)
	if err != nil {
		return nil, err
	}
	figures := make([]Figure, len(legacyFigures))
	for i, f := range legacyFigures {
		figures[i] = Figure{
			ID:   f.ID,
			Name: f.Name,
		}
	}
	return figures, nil
}

func (s *service) SaveHistory(userID string, keyword string) error {
	rankScore := s.clock.Now().Unix() * 1000
	cKey := key.UserSearchHistory(userID)
	if err := s.userCacheWriter.ZAdd(cKey, rankScore, []byte(keyword)); err != nil {
		return fmt.Errorf("search service: %w", err)
	}
	return nil
}
