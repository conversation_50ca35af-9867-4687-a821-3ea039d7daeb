package title

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/deeplink"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/meta"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/middleware"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/v4/presenter"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/kktverror"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/permission"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbmeta/legacy"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	modelmw "github.com/KKTV/kktv-api-v3/pkg/model/middleware"
	"github.com/go-zoo/bone"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	"gopkg.in/guregu/null.v3"
)

type HandlerTestSuite struct {
	suite.Suite
	r    *require.Assertions
	ctrl *gomock.Controller
	app  *bone.Mux

	mockService       *MockService
	mockPermissionSrv *permission.MockService
	mockTitleRepo     *meta.MockTitleRepository
}

func TestHandlerTestSuite(t *testing.T) {
	suite.Run(t, new(HandlerTestSuite))
}

func (suite *HandlerTestSuite) SetupTest() {
	suite.r = suite.Require()
	suite.app = bone.New()
	suite.ctrl = gomock.NewController(suite.T())
	suite.mockService = NewMockService(suite.ctrl)
	suite.mockPermissionSrv = permission.NewMockService(suite.ctrl)
	suite.mockTitleRepo = meta.NewMockTitleRepository(suite.ctrl)

	handler := &Handler{
		service:           suite.mockService,
		titleRepo:         suite.mockTitleRepo,
		permissionService: suite.mockPermissionSrv,
	}

	const titleIDRegex = `#titleID^\d{8}$`
	suite.app.GetFunc(`/v4/a/titles/`+titleIDRegex, handler.GetByID)
	suite.app.GetFunc(`/v4/a/titles/`+titleIDRegex+`/related-titles`, handler.ListRelatedTitles)
	suite.app.GetFunc(`/v4/w/titles/`+titleIDRegex, handler.GetByID)
	suite.app.GetFunc(`/v4/w/titles/`+titleIDRegex+`/related-titles`, handler.ListRelatedTitles)
}

func (suite *HandlerTestSuite) TearDownTest() {
	defer suite.ctrl.Finish()
}

func (suite *HandlerTestSuite) TestGetByID() {
	var (
		titleID = "01060429"
	)

	testcases := []struct {
		name           string
		prepareRequest func() *http.Request
		given          func()
		then           func(code int, body []byte)
	}{
		{
			name: "return OK WHEN title is found and user is not logged in, with extra",
			prepareRequest: func() *http.Request {
				return httptest.NewRequest(http.MethodGet, "/v4/a/titles/"+titleID+"?extra=1", nil)
			},
			given: func() {
				t := aFakeTitleDetail(titleID)
				suite.mockService.EXPECT().GetPageByID(titleID).Return(&DetailPage{
					Title: t,
					DisplayInfo: DisplayInfo{
						PlayHint: null.StringFrom("播放").Ptr(),
					},
				}, nil)
				suite.mockService.EXPECT().GetExtra(titleID).Return(&Extra{
					Series: []presenter.Series{
						{
							ID:              titleID + "tr",
							Name:            "預告",
							ContentAgent:    "TVB",
							ContentProvider: "BTV",
							Episodes:        []presenter.Episode{},
						},
					}},
					nil)
			},
			then: func(code int, bBytes []byte) {
				suite.Equal(http.StatusOK, code)

				println(string(bBytes))
				var body map[string]interface{}
				_ = json.Unmarshal(bBytes, &body)
				dataObj := body["data"].(map[string]interface{})
				titleObj := dataObj["title"].(map[string]interface{})
				suite.Equal(titleID, titleObj["id"])
				suite.EqualValues(2032, titleObj["end_year"])
				suite.EqualValues(2022, titleObj["release_year"])

				extraObj := titleObj["extra"].(map[string]interface{})
				suite.Equal("預告", extraObj["series"].([]interface{})[0].(map[string]interface{})["name"])
			},
		},
		{
			name: "return notFound when title id is invalid",
			prepareRequest: func() *http.Request {
				// title id with 9 digits
				return httptest.NewRequest(http.MethodGet, "/v4/a/titles/123456789", nil)
			},
			given: func() {},
			then: func(code int, bBytes []byte) {
				suite.Equal(http.StatusNotFound, code)
			},
		},
		{
			name: "return notFound when given title id is not found",
			prepareRequest: func() *http.Request {
				return httptest.NewRequest(http.MethodGet, "/v4/a/titles/"+titleID+"?extra=1", nil)
			},
			given: func() {
				suite.mockService.EXPECT().GetPageByID(titleID).Return(nil, kktverror.ErrResourceNotFound)
			},
			then: func(code int, bBytes []byte) {
				suite.Equal(http.StatusNotFound, code)
			},
		},
		{
			name: "return ok without extra when title license has expired",
			prepareRequest: func() *http.Request {
				return httptest.NewRequest(http.MethodGet, "/v4/a/titles/"+titleID+"?extra=1", nil)
			},
			given: func() {
				t := aFakeTitleDetail(titleID)
				t.Status = "license_expired"
				suite.mockService.EXPECT().GetPageByID(titleID).Return(&DetailPage{
					Title: t,
					DisplayInfo: DisplayInfo{
						PlayHint: null.StringFrom("播放").Ptr(),
					},
				}, nil)
			},
			then: func(code int, bBytes []byte) {
				var body map[string]interface{}
				_ = json.Unmarshal(bBytes, &body)
				dataObj := body["data"].(map[string]interface{})
				title := dataObj["title"].(map[string]interface{})

				suite.Equal(nil, title["extra"])
				suite.Equal(http.StatusOK, code)
			},
		},
		{
			name: "return ok without extra when access title on web",
			prepareRequest: func() *http.Request {
				return httptest.NewRequest(http.MethodGet, "/v4/w/titles/"+titleID, nil)
			},
			given: func() {
				t := aFakeTitleDetail(titleID)
				t.AllowFreeTrial = true
				t.TitleAliases = []string{
					"邁向未來的倒數10秒",
					"拳王的教室",
				}
				suite.mockService.EXPECT().GetPageByID(titleID).Return(&DetailPage{
					Title: t,
					DisplayInfo: DisplayInfo{
						PlayHint: null.StringFrom("播放").Ptr(),
					},
				}, nil)
			},
			then: func(code int, bBytes []byte) {
				var body map[string]interface{}
				_ = json.Unmarshal(bBytes, &body)
				dataObj := body["data"].(map[string]interface{})
				title := dataObj["title"].(map[string]interface{})
				expectTitleAlias := []interface{}{
					"邁向未來的倒數10秒",
					"拳王的教室",
				}

				suite.Equal(nil, title["extra"])
				suite.Equal(http.StatusOK, code)
				suite.Equal(expectTitleAlias, title["title_aliases"])
				suite.Equal(true, title["allow_free_trial"])
			},
		},
	}
	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			tc.given()

			req := tc.prepareRequest()
			rr := httptest.NewRecorder()
			suite.app.ServeHTTP(rr, req)

			tc.then(rr.Code, rr.Body.Bytes())
		})
	}
}

func (suite *HandlerTestSuite) TestListRelatedTitles() {
	var (
		titleID      = "01060429"
		titleIDs     = []string{"04290106", "01000465"}
		titleDetail1 = &legacymeta.TitleDetail{
			LegacyTitleDetail: &model.TitleDetail{
				ID: "04290106", Title: "Josie smile",
				ContentLabelsWithoutFullAccess: []string{"vip"},
			},
		}
		titleDetails = []*legacymeta.TitleDetail{titleDetail1}
	)
	mockFoundRelatedTitles := func() {
		suite.mockService.EXPECT().ListRelated(titleID).Return(titleIDs, nil)
		suite.mockTitleRepo.EXPECT().ListViewableTitleDetailWithoutSeries(titleIDs, true).Return(titleDetails, nil)
		for _, td := range titleDetails {
			req := permission.RequestFullAccessTitleDetail(td, dbuser.NonMember)
			suite.mockPermissionSrv.EXPECT().Grant(req).Return(kktverror.ErrResourceAccessDenied)
		}
	}
	testcases := []struct {
		name           string
		prepareRequest func() *http.Request
		given          func()
		then           func(code int, body []byte)
	}{
		{
			name: "return items WHEN title is found and user is not logged in",
			prepareRequest: func() *http.Request {
				newRequest := httptest.NewRequest(http.MethodGet, fmt.Sprintf("/v4/a/titles/%s/related-titles", titleID), nil)
				return newRequest.WithContext(
					context.WithValue(newRequest.Context(), middleware.KeyAccessUser, modelmw.AccessUser{Memberships: dbuser.NonMember}),
				)
			},
			given: mockFoundRelatedTitles,
			then: func(code int, bBytes []byte) {
				suite.Equal(http.StatusOK, code)

				var body map[string]interface{}
				_ = json.Unmarshal(bBytes, &body)
				dataObj := body["data"].(map[string]interface{})
				items := dataObj["items"].([]interface{})

				suite.r.Len(items, 1)
				suite.r.Equal("Josie smile", items[0].(map[string]interface{})["name"])

				labels := items[0].(map[string]interface{})["labels"].([]interface{})
				suite.r.Len(labels, 1)
				labelDisplayName := labels[0].(map[string]interface{})["display_name"]
				suite.r.Equal("VIP", labelDisplayName)
			},
		},
		{
			name: "return items WHEN title is found and user is not login on web page",
			prepareRequest: func() *http.Request {
				newRequest := httptest.NewRequest(http.MethodGet, fmt.Sprintf("/v4/w/titles/%s/related-titles", titleID), nil)
				return newRequest.WithContext(
					context.WithValue(newRequest.Context(), middleware.KeyAccessUser, modelmw.AccessUser{Memberships: dbuser.NonMember}),
				)
			},
			given: mockFoundRelatedTitles,
			then: func(code int, bBytes []byte) {
				suite.Equal(http.StatusOK, code)
			},
		},
	}
	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			tc.given()

			req := tc.prepareRequest()
			rr := httptest.NewRecorder()
			suite.app.ServeHTTP(rr, req)

			tc.then(rr.Code, rr.Body.Bytes())
		})
	}
}

// TODO remove after implement
func aFakeTitleDetail(titleID string) *Detail {
	return &Detail{
		ListedTitle: presenter.ListedTitle{
			WebOnlyListedTitleMeta: presenter.WebOnlyListedTitleMeta{
				Casts: []presenter.CollectionTag{
					{
						CollectionType: "figure",
						CollectionName: "小新",
						DeepLink:       deeplink.CollectionPage("figure", "小新", nil),
					},
				},
				Themes: []presenter.CollectionTag{
					{
						CollectionType: "theme",
						CollectionName: "溫馨喜劇",
						DeepLink:       deeplink.CollectionPage("theme", "溫馨喜劇", nil),
					},
				},
				ReleaseYear: 2022, EndYear: null.IntFrom(2032).Ptr(),
			},
			TitleBasicInfo: presenter.TitleBasicInfo{
				ID:        titleID,
				Name:      "Josie smile",
				TitleType: "miniseries",
				Status:    "license_valid",
				IsEnding:  true,
				ChildLock: true,
			},
			ListedTitleMeta: presenter.ListedTitleMeta{
				LatestUpdateInfo: "共11集",
				Labels: []presenter.Label{
					presenter.GetLabel(presenter.LabelTypeDualSubtitle),
					presenter.GetLabel(presenter.LabelTypeVIP),
				},
				Cover: "https://images.kktv.com.tw/covers/70/706dab0a5d5c7578edecb06e35bcb4cddbf67079.xs.jpg",
				Stills: []string{
					"https://images.kktv.com.tw/stills/bd/bd4003649bb933be8ff595a8427031e08a6144e2.xs.jpg",
					"https://images.kktv.com.tw/stills/11/11e450752c406cfc6eb7ae6afd1f77ebd34d0a97.xs.jpg",
				},
				UserRating: null.FloatFrom(3.6),
				DeepLink:   deeplink.TitlePage("01000468"),
			},
		},
		HasExtra: true,
		Series: []presenter.Series{
			{
				ID:              "**********",
				Name:            "第一季",
				ContentAgent:    "采昌",
				ContentProvider: "JOSIE",
				Episodes: []presenter.Episode{
					{
						ID:           "**********0002",
						Name:         "第2集",
						IsAvod:       true,
						Duration:     3000,
						Still:        "https://fakeimg.com/1.jpg",
						Subtitles:    []string{"zh-Hant"},
						AllowOffline: null.BoolFrom(true).Ptr(),
						LicenseEnd:   null.IntFrom(time.Date(2020, 4, 29, 0, 0, 0, 0, time.UTC).Unix()).Ptr(),
						LicenseStart: null.IntFrom(time.Date(2025, 4, 29, 0, 0, 0, 0, time.UTC).Unix()).Ptr(),
						LastPlayed: &presenter.EpisodeLastPlayed{
							PlayedPercentage: 0.429,
							Deeplink:         deeplink.ContinuePlay("**********0002", 222),
							PlayedOffset:     222,
						},
					},
				},
			},
		},
		Copyright:           "josiesmile corp.",
		ReverseDisplayOrder: true,
		AllowNoSubtitle:     true,
		ContentRating:       null.StringFrom("2").Ptr(),
		Osts: []Ost{
			{
				ArtistName: "Julia wu",
				Image:      "https://fakeimg.com/julia.jpg",
				Title:      "Josie smile",
				Url:        "https://fakeurl.com/julia.mp3",
			},
		},
		Genres: []presenter.CollectionTag{
			{
				CollectionType: "genre",
				CollectionName: "戲劇",
				DeepLink:       deeplink.CollectionPage("genre", "戲劇", nil),
			},
		},
		Countries: []presenter.CollectionTag{
			{
				CollectionType: "country",
				CollectionName: "Taiwan",
				DeepLink:       deeplink.CollectionPage("country", "Taiwan", nil),
			},
		},
		ContentAgents: []presenter.CollectionTag{
			{
				CollectionType: "content_agent",
				CollectionName: "采昌",
				DeepLink:       deeplink.CollectionPage("content_agent", "采昌", nil),
			},
		},
		ContentProviders: []presenter.CollectionTag{
			{
				CollectionType: "content_provider",
				CollectionName: "JOSIE",
				DeepLink:       deeplink.CollectionPage("content_provider", "JOSIE", nil),
			},
		},
		Directors: []presenter.CollectionTag{
			{
				CollectionType: "figure",
				CollectionName: "周星馳",
				DeepLink:       deeplink.CollectionPage("figure", "周星馳", nil),
			},
		},
		Writers: []presenter.CollectionTag{
			{
				CollectionType: "figure",
				CollectionName: "周星馳",
				DeepLink:       deeplink.CollectionPage("figure", "周星馳", nil),
			},
		},
		Producers: []presenter.CollectionTag{
			{
				CollectionType: "figure",
				CollectionName: "周星馳",
				DeepLink:       deeplink.CollectionPage("figure", "周星馳", nil),
			},
		},
		Tags: []presenter.CollectionTag{
			{
				CollectionType: "tag",
				CollectionName: "喜劇",
				DeepLink:       deeplink.CollectionPage("tag", "喜劇", nil),
			},
		},
	}
}
