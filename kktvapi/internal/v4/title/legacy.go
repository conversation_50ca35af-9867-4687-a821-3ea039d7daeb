//go:generate mockgen -source legacy.go -destination legacy_mock.go -package title
package title

import (
	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/kktverror"
	"github.com/jinzhu/copier"
)

type legacyDetail struct {
	*model.UserSingleTitleDetail
	legacyTitleDetail *model.TitleDetail
}

type legacyHelp struct {
}

type legacyHelper interface {
	getUserSingleTitleDetail(titleID string, userID string) (*legacyDetail, error)
}

func (l *legacyHelp) getUserSingleTitleDetail(titleID string, userID string) (*legacyDetail, error) {
	t, err := model.NewUserSingleTitleDetail(userID, titleID)
	// the NewUserSingleTitleDetail function may return a non-nil t and a non-nil err at the same time
	if err != nil && t == nil {
		return nil, err
	} else if !t.Available {
		return nil, kktverror.ErrResourceNotFound
	}
	tmpLegacyDetail := model.TitleDetail{}
	if err := copier.Copy(&tmpLegacyDetail, t); err != nil {
		return nil, err
	}
	return &legacyDetail{
		UserSingleTitleDetail: t,
		legacyTitleDetail:     &tmpLegacyDetail,
	}, nil
}
