package v4

import (
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/v4/auth"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/v4/billing"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/v4/coldstart"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/v4/collection"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/v4/favorite"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/v4/library"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/v4/mod"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/v4/page"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/v4/productpackage"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/v4/remoteconfig"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/v4/search"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/v4/seo"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/v4/textcontent"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/v4/title"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/v4/titlelist"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/v4/trial"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/v4/user"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/v4/watchhistory"
)

type Handlers struct {
	Collection   *collection.Handler
	Page         *page.Handler
	Seo          *seo.Handler
	Search       *search.Handler
	WatchHistory *watchhistory.Handler
	Favorite     *favorite.Handler
	ColdStart    *coldstart.Handler
	Titlelist    *titlelist.Handler
	Billing      *billing.Handler
	RemoteConfig *remoteconfig.Handler
	Title        *title.Handler
	User         *user.Handler
	TextContent  *textcontent.Handler
	ProductPkg   *productpackage.Handler
	Auth         *auth.Handler
	MOD          *mod.Handler
	Library      *library.Handler
	Trial        *trial.Handler
}

func NewHandlers() *Handlers {
	return &Handlers{
		Collection:   collection.NewHandler(),
		Page:         page.NewHandler(),
		Seo:          seo.NewHandler(),
		Search:       search.NewHandler(),
		WatchHistory: watchhistory.NewHandler(),
		Favorite:     favorite.NewHandler(),
		ColdStart:    coldstart.NewHandler(),
		Titlelist:    titlelist.NewHandler(),
		Billing:      billing.NewHandler(),
		RemoteConfig: remoteconfig.NewHandler(),
		Title:        title.NewHandler(),
		User:         user.NewHandler(),
		TextContent:  textcontent.NewHandler(),
		ProductPkg:   productpackage.NewHandler(),
		Auth:         auth.NewHandler(),
		MOD:          mod.NewHandler(),
		Library:      library.NewHandler(),
		Trial:        trial.NewHandler(),
	}
}
