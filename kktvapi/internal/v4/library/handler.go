package library

import (
	"net/http"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/meta"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/request"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/rest"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/v4/presenter"
	"github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/render"
	"github.com/jinzhu/copier"
)

type Handler struct {
	titleRepo meta.TitleRepository
}

func NewHandler() *Handler {
	return &Handler{
		titleRepo: meta.NewTitleRepository(),
	}
}

func (h *Handler) ListTitles(w http.ResponseWriter, r *http.Request) {
	statusCode, resp := http.StatusOK, rest.Ok()
	var respData ListTitlesResp

	defer func() {
		render.JSON(w, statusCode, resp)
	}()

	paging := request.Paging{Page: 1, PageSize: 200}
	if err := paging.ScanFromRequest(r, 200); err != nil {
		log.Warn("v4LibraryHandler: ListTitles: get paging failed").Err(err).Send()
		statusCode, resp = http.StatusBadRequest, rest.Error(ErrInvalidParameters.Message, ErrInvalidParameters.Code)
		return
	}

	titleDetails, err := h.titleRepo.ListAvailableTitlesWithPagination(paging)
	if err != nil {
		log.Error("v4LibraryHandler: ListTitles: ListAvailableTitlesWithPagination").Err(err).Send()
		statusCode, resp = http.StatusInternalServerError, rest.Error(ErrInternalServerError.Message, ErrInternalServerError.Code)
		return
	}

	count, err := h.titleRepo.CountAvailableTitles()

	if err != nil {
		log.Error("v4LibraryHandler: ListTitles: CountAvailableTitles").Err(err).Send()
		statusCode, resp = http.StatusInternalServerError, rest.Error(ErrInternalServerError.Message, ErrInternalServerError.Code)
		return
	}

	var titles []titles
	err = copier.Copy(&titles, titleDetails)
	if err != nil {
		log.Error("v4LibraryHandler: ListTitles: Copy titles").Err(err).Send()
		statusCode, resp = http.StatusInternalServerError, rest.Error(ErrInternalServerError.Message, ErrInternalServerError.Code)
		return
	}

	respData.Pagination = presenter.Pagination{
		Page:     paging.Page,
		PageSize: paging.PageSize,
		Total:    count,
	}

	respData.Items = titles
	resp.Data = respData
}
