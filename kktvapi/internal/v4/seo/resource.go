package seo

import "github.com/KKTV/kktv-api-v3/pkg/datatype"

type series struct {
	ID       string    `json:"id"`
	Name     string    `json:"name"`
	Episodes []episode `json:"episodes"`
}

type manifest struct {
	URI string `json:"uri"`
}

type mezzanines struct {
	HLS  *manifest `json:"hls"`
	Dash *manifest `json:"dash"`
}

type episode struct {
	ID          string                `json:"id"`
	Name        string                `json:"name"`
	SeriesTitle string                `json:"series_title"`
	Still       string                `json:"still"`
	Duration    datatype.RoundedFloat `json:"duration"`
	PublishDate int64                 `json:"publish_date"`
	IsAvod      bool                  `json:"is_avod"`
	Mezzanines  mezzanines            `json:"mezzanines"`
}

type ost struct {
	ArtistName string `json:"artist_name"`
	Image      string `json:"image"`
	Title      string `json:"title"`
	URL        string `json:"url"`
}

type extraEpisode struct {
	Name  string `json:"name"`
	ID    string `json:"id"`
	Still string `json:"still"`
}

type extraSeries struct {
	Episodes []extraEpisode `json:"episodes"`
}

type titleExtra struct {
	Series []extraSeries `json:"series"`
}

type titleMeta struct {
	ID                 string         `json:"id"`
	Name               string         `json:"name"`
	TitleType          string         `json:"title_type"`
	Cover              string         `json:"cover"`
	Status             string         `json:"status"`
	Stills             []string       `json:"stills"`
	Series             []series       `json:"series"`
	Summary            string         `json:"summary"`
	Country            string         `json:"country"`
	Directors          []string       `json:"directors"`
	Casts              []string       `json:"casts"`
	ContentProviders   []string       `json:"content_providers"`
	Ost                *ost           `json:"ost"`
	UserRating         float64        `json:"user_rating"`
	RatingUserCount    int64          `json:"rating_user_count"`
	TotalEpisodeCounts map[string]int `json:"total_episode_counts"`
	PublishDate        int64          `json:"publish_date"`
	WikiZh             *string        `json:"wiki_zh"`
	ChildLock          bool           `json:"child_lock"`
	Copyright          string         `json:"copyright"`
	ReleaseYear        int64          `json:"release_year"`
	Genres             []string       `json:"genres"`
	Writers            []string       `json:"writers"`
	TitleExtra         titleExtra     `json:"title_extra"`
	IsContainingAvod   bool           `json:"is_containing_avod"`
	TitleAliases       []string       `json:"title_aliases"`
	Themes             []string       `json:"themes"`
	Tags               []string       `json:"tags"`
	IsListed           bool           `json:"is_listed"`
}
