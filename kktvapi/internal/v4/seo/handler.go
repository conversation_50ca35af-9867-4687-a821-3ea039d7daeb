package seo

import (
	"errors"
	"net/http"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/rest"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/kktverror"
	"github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/render"
	"github.com/go-zoo/bone"
)

type Handler struct {
	service Service
}

func NewHandler() *Handler {
	return &Handler{
		service: NewService(),
	}
}

// GetTitleMeta for SEO purpose, to get meta for title page
func (h *Handler) GetTitleMeta(w http.ResponseWriter, r *http.Request) {
	titleID := bone.GetValue(r, "titleId")

	response := rest.Ok()

	meta, err := h.service.GetTitleMeta(titleID)
	if err != nil {
		if errors.Is(err, kktverror.ErrResourceNotFound) {
			response.Err = ErrRespTitleNotFound
			render.JSON(w, http.StatusNotFound, response)
			return
		}
		response.Err = ErrRespUnknown
		log.Error("GetTitleMeta, service get title meta fail").Err(err).Send()
		render.JSON(w, http.StatusInternalServerError, response)
		return
	}

	response.Data = meta
	render.JSON(w, http.StatusOK, response)
}

func (h *Handler) GetAvailableTitles(w http.ResponseWriter, r *http.Request) {
	response := rest.Ok()

	if titleIds, err := h.service.GetTitlesFromViewableEpisodes(); err != nil {
		response.Err = ErrRespTitleNotFound
		render.JSON(w, http.StatusNotFound, response)
		return
	} else if len(titleIds) == 0 {
		response.Err = ErrRespTitleNotFound
		render.JSON(w, http.StatusNotFound, response)
		return
	} else {
		response.Data = titleIds
	}
	render.JSON(w, http.StatusOK, response)
}
