// Code generated by MockGen. DO NOT EDIT.
// Source: service.go

// Package billing is a generated GoMock package.
package billing

import (
	reflect "reflect"

	facebook "github.com/KKTV/kktv-api-v3/kkapp/facebook"
	amplitudelib "github.com/KKTV/kktv-api-v3/pkg/amplitudelib"
	billing "github.com/KKTV/kktv-api-v3/pkg/billing"
	dbuser "github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	gomock "github.com/golang/mock/gomock"
)

// MockService is a mock of Service interface.
type MockService struct {
	ctrl     *gomock.Controller
	recorder *MockServiceMockRecorder
}

// MockServiceMockRecorder is the mock recorder for MockService.
type MockServiceMockRecorder struct {
	mock *MockService
}

// NewMockService creates a new mock instance.
func NewMockService(ctrl *gomock.Controller) *MockService {
	mock := &MockService{ctrl: ctrl}
	mock.recorder = &MockServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockService) EXPECT() *MockServiceMockRecorder {
	return m.recorder
}

// BuildAccountTransactionCompletedEvent mocks base method.
func (m *MockService) BuildAccountTransactionCompletedEvent(order billing.Order, user *dbuser.User, productPackage *dbuser.ProductPackage) (amplitudelib.AccountTransactionCompletedEvent, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BuildAccountTransactionCompletedEvent", order, user, productPackage)
	ret0, _ := ret[0].(amplitudelib.AccountTransactionCompletedEvent)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BuildAccountTransactionCompletedEvent indicates an expected call of BuildAccountTransactionCompletedEvent.
func (mr *MockServiceMockRecorder) BuildAccountTransactionCompletedEvent(order, user, productPackage interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BuildAccountTransactionCompletedEvent", reflect.TypeOf((*MockService)(nil).BuildAccountTransactionCompletedEvent), order, user, productPackage)
}

// GetPackageByBillingOrder mocks base method.
func (m *MockService) GetPackageByBillingOrder(order billing.Order) *dbuser.ProductPackage {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPackageByBillingOrder", order)
	ret0, _ := ret[0].(*dbuser.ProductPackage)
	return ret0
}

// GetPackageByBillingOrder indicates an expected call of GetPackageByBillingOrder.
func (mr *MockServiceMockRecorder) GetPackageByBillingOrder(order interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPackageByBillingOrder", reflect.TypeOf((*MockService)(nil).GetPackageByBillingOrder), order)
}

// GetPackageTitlesByBillingOrders mocks base method.
func (m *MockService) GetPackageTitlesByBillingOrders(orders []billing.Order) (map[string]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPackageTitlesByBillingOrders", orders)
	ret0, _ := ret[0].(map[string]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPackageTitlesByBillingOrders indicates an expected call of GetPackageTitlesByBillingOrders.
func (mr *MockServiceMockRecorder) GetPackageTitlesByBillingOrders(orders interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPackageTitlesByBillingOrders", reflect.TypeOf((*MockService)(nil).GetPackageTitlesByBillingOrders), orders)
}

// GetPackagesByBillingOrders mocks base method.
func (m *MockService) GetPackagesByBillingOrders(orders []billing.Order) (map[string]*dbuser.ProductPackage, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPackagesByBillingOrders", orders)
	ret0, _ := ret[0].(map[string]*dbuser.ProductPackage)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPackagesByBillingOrders indicates an expected call of GetPackagesByBillingOrders.
func (mr *MockServiceMockRecorder) GetPackagesByBillingOrders(orders interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPackagesByBillingOrders", reflect.TypeOf((*MockService)(nil).GetPackagesByBillingOrders), orders)
}

// GetUserByID mocks base method.
func (m *MockService) GetUserByID(userID string) (*dbuser.User, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserByID", userID)
	ret0, _ := ret[0].(*dbuser.User)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserByID indicates an expected call of GetUserByID.
func (mr *MockServiceMockRecorder) GetUserByID(userID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserByID", reflect.TypeOf((*MockService)(nil).GetUserByID), userID)
}

// SendAccountTransactionCompletedEvent mocks base method.
func (m *MockService) SendAccountTransactionCompletedEvent(evt amplitudelib.AccountTransactionCompletedEvent, userID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendAccountTransactionCompletedEvent", evt, userID)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendAccountTransactionCompletedEvent indicates an expected call of SendAccountTransactionCompletedEvent.
func (mr *MockServiceMockRecorder) SendAccountTransactionCompletedEvent(evt, userID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendAccountTransactionCompletedEvent", reflect.TypeOf((*MockService)(nil).SendAccountTransactionCompletedEvent), evt, userID)
}

// SendFacebookConversionEvent mocks base method.
func (m *MockService) SendFacebookConversionEvent(data facebook.PurchaseEventData) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendFacebookConversionEvent", data)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendFacebookConversionEvent indicates an expected call of SendFacebookConversionEvent.
func (mr *MockServiceMockRecorder) SendFacebookConversionEvent(data interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendFacebookConversionEvent", reflect.TypeOf((*MockService)(nil).SendFacebookConversionEvent), data)
}

// UpdateBillingProducts mocks base method.
func (m *MockService) UpdateBillingProducts(products []billing.Product) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateBillingProducts", products)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateBillingProducts indicates an expected call of UpdateBillingProducts.
func (mr *MockServiceMockRecorder) UpdateBillingProducts(products interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateBillingProducts", reflect.TypeOf((*MockService)(nil).UpdateBillingProducts), products)
}

// UpdateCreditCard mocks base method.
func (m *MockService) UpdateCreditCard(req UpdateCreditCardReq) (Detail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateCreditCard", req)
	ret0, _ := ret[0].(Detail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateCreditCard indicates an expected call of UpdateCreditCard.
func (mr *MockServiceMockRecorder) UpdateCreditCard(req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateCreditCard", reflect.TypeOf((*MockService)(nil).UpdateCreditCard), req)
}
