package auth

import "github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/rest"

var (
	ErrInvalidParameters = &rest.Err{Code: "400.0", Message: "invalid parameters"}
	ErrURIMismatched     = &rest.Err{Code: "400.1", Message: "mismatched uri"}
	ErrInvalidApp        = &rest.Err{Code: "400.2", Message: "invalid client application"}
	ErrInvalidAuthCode   = &rest.Err{Code: "400.3", Message: "invalid authorization code"}
	ErrUserNotLogin      = &rest.Err{Code: "401.0", Message: "unauthorized"}
	ErrUnknown           = &rest.Err{Code: "500.0", Message: "unknown error"}
)
