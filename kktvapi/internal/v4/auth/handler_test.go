package auth

import (
	"bytes"
	"context"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"net/url"
	"testing"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/appauth"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/middleware"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/oauth"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/KKTV/kktv-api-v3/pkg/clock"
	"github.com/KKTV/kktv-api-v3/pkg/datetimer"
	"github.com/KKTV/kktv-api-v3/pkg/encrypt"
	"github.com/KKTV/kktv-api-v3/pkg/key"
	"github.com/KKTV/kktv-api-v3/pkg/model/cacheuser"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	modelmw "github.com/KKTV/kktv-api-v3/pkg/model/middleware"
	"github.com/KKTV/kktv-api-v3/pkg/rand"
	"github.com/go-zoo/bone"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
	"gopkg.in/guregu/null.v3"
)

type HandlerTestSuite struct {
	suite.Suite
	ctrl                       *gomock.Controller
	mockAppRepo                *appauth.MockRepository
	mockUserCacheWriter        *cache.MockCacher
	mockUserCacheReader        *cache.MockCacher
	mockClock                  *clock.MockClock
	mockRand                   *rand.MockRand
	mockOauthAuthorizationRepo *oauth.MockAuthorizationRepository

	handler Handler
	app     *bone.Mux
}

var (
	now = time.Date(2020, 4, 29, 0, 0, 0, 0, datetimer.LocationTaipei)
)

func TestHandlerTestSuite(t *testing.T) {
	suite.Run(t, new(HandlerTestSuite))
}

func (suite *HandlerTestSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
	suite.mockAppRepo = appauth.NewMockRepository(suite.ctrl)
	suite.mockUserCacheWriter = cache.NewMockCacher(suite.ctrl)
	suite.mockUserCacheReader = cache.NewMockCacher(suite.ctrl)
	suite.mockClock = clock.NewMockClock(suite.ctrl)
	suite.mockRand = rand.NewMockRand(suite.ctrl)
	suite.mockOauthAuthorizationRepo = oauth.NewMockAuthorizationRepository(suite.ctrl)

	suite.handler = Handler{
		appAuthRepo:            suite.mockAppRepo,
		oauthAuthorizationRepo: suite.mockOauthAuthorizationRepo,
		userCacheWriter:        suite.mockUserCacheWriter,
		userCacheReader:        suite.mockUserCacheReader,
		codeGen:                encrypt.NewCodeGen(),
		clock:                  suite.mockClock,
		rand:                   suite.mockRand,
	}

	suite.app = bone.New()
	suite.app.GetFunc("/v4/auth/redirect", suite.handler.SignRedirectURI)
	suite.app.PostFunc("/v4/auth/code", suite.handler.CreateAuthCode)
	suite.app.GetFunc("/v4/auth/token", suite.handler.ExchangeAccessToken)
}

func (suite *HandlerTestSuite) TearDownTest() {
	defer suite.ctrl.Finish()
}

func (suite *HandlerTestSuite) TestSignRedirectURI() {
	const (
		redirectURI = "https://feversocial.com/auth/client/enterprise/callback"
		appID       = "feversocial-test"
	)
	var (
		expiredUser = model.JwtUser{
			Sub:  "josie",
			Role: dbuser.RoleExpired.String(),
		}
		existedAuthedApp = &dbuser.AuthedApp{
			AppID:        appID,
			AppSecret:    "123",
			RedirectURIs: []string{"https://stage-feversocial.com/auth/client/enterprise/callback", redirectURI},
		}
	)

	testcases := []struct {
		name         string
		queryStrings map[string]string
		reqUser      model.JwtUser
		given        func()
		thenAssert   func(code int, body []byte)
	}{
		{
			name: "WHEN authed user THEN return redirect uri with encoded user id",
			queryStrings: map[string]string{
				"state":        "123",
				"redirect_uri": redirectURI + "/?",
				"client_id":    appID,
			},
			reqUser: expiredUser,
			given: func() {
				suite.givenFoundAuthedAppByID(appID, existedAuthedApp)
				suite.givenUserCodeCachingOK("josie")
			},
			thenAssert: suite.thenOKResponseHasRedirectURI("https://feversocial.com/auth/client/enterprise/callback?code=b2731cdad8c183fd363827e681be61e7&state=123"),
		},
		{
			name:         "WHEN lack of state THEN return bad request",
			queryStrings: map[string]string{"redirect_uri": redirectURI, "client_id": appID},
			reqUser:      expiredUser,
			given:        func() {},
			thenAssert: func(code int, body []byte) {
				suite.Equal(http.StatusBadRequest, code)
			},
		},
		{
			name:         "WHEN user is guest THEN return unauthorized",
			queryStrings: map[string]string{"redirect_uri": redirectURI, "client_id": appID, "state": "123"},
			reqUser: model.JwtUser{
				Sub: "guest:iamguest",
			},
			given: func() {},
			thenAssert: func(code int, body []byte) {
				suite.Equal(http.StatusUnauthorized, code)
			},
		},
		{
			name: "WHEN invalid app id THEN return bad request",
			queryStrings: map[string]string{
				"state":        "123",
				"redirect_uri": redirectURI,
				"client_id":    "invalid-app-id",
			},
			reqUser: expiredUser,
			given: func() {
				suite.mockAppRepo.EXPECT().GetActiveByAppID("invalid-app-id").Return(nil, nil)
			},
			thenAssert: func(code int, body []byte) {
				suite.Equal(http.StatusBadRequest, code)
			},
		},
		{
			name: "GIVEN auth app is found but got no matched redirect uri THEN return bad request",
			queryStrings: map[string]string{
				"state":        "123",
				"redirect_uri": "https://not-matched.com/callback",
				"client_id":    appID,
			},
			reqUser: expiredUser,
			given: func() {
				suite.givenFoundAuthedAppByID(appID, existedAuthedApp)
			},
			thenAssert: func(code int, body []byte) {
				suite.Equal(http.StatusBadRequest, code)
			},
		},
	}
	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			tc.given()
			reqURL, _ := url.Parse("/v4/auth/redirect")
			urlValues := url.Values{}
			for k, v := range tc.queryStrings {
				urlValues.Add(k, v)
			}
			reqURL.RawQuery = urlValues.Encode()

			req := httptest.NewRequest(http.MethodGet, reqURL.String(), nil)
			ctx := context.WithValue(req.Context(), "user", tc.reqUser)
			req = req.WithContext(ctx)
			rr := httptest.NewRecorder()
			suite.app.ServeHTTP(rr, req)

			tc.thenAssert(rr.Code, rr.Body.Bytes())
		})
	}
}

func (suite *HandlerTestSuite) thenOKResponseHasRedirectURI(uri string) func(code int, body []byte) {
	return func(code int, body []byte) {
		suite.Equal(http.StatusOK, code)
		var bodyJson map[string]interface{}
		suite.NoError(json.Unmarshal(body, &bodyJson))
		suite.Equal(uri, bodyJson["data"].(map[string]interface{})["redirect_uri"])
	}
}

func (suite *HandlerTestSuite) givenFoundAuthedAppByID(appID string, app *dbuser.AuthedApp) {
	suite.mockAppRepo.EXPECT().GetActiveByAppID(appID).Return(app, nil)
}

func (suite *HandlerTestSuite) givenUserCodeCachingOK(userID string) string {
	suite.mockClock.EXPECT().Now().Return(now)
	hash := md5.Sum([]byte(fmt.Sprintf("%s:%d", userID, now.Unix())))
	code := hex.EncodeToString(hash[:])
	cKey := key.UserAuthCode(code)
	suite.mockUserCacheWriter.EXPECT().Set(cKey, userID, ttlAuthUserCode).Return(nil)

	return code
}

// Helper methods for CreateAuthCode tests
func (suite *HandlerTestSuite) givenRandomStringGenerated(length int, result string) {
	suite.mockRand.EXPECT().RandomString(length).Return(result)
}

func (suite *HandlerTestSuite) givenAuthDataCachingOK(authCode string, authData cacheuser.OAuthData) {
	cKey := key.OAuthAuthCode(authCode)
	suite.mockUserCacheWriter.EXPECT().Set(cKey, authData, ttlAuthUserCode).Return(nil)
}

func (suite *HandlerTestSuite) givenAuthDataCachingFailed(authCode string, authData cacheuser.OAuthData, err error) {
	cKey := key.OAuthAuthCode(authCode)
	suite.mockUserCacheWriter.EXPECT().Set(cKey, authData, ttlAuthUserCode).Return(err)
}

func (suite *HandlerTestSuite) givenAuthorizationCreated(appID, userID, scope, redirectURI string) chan struct{} {
	wait := make(chan struct{})
	suite.mockOauthAuthorizationRepo.EXPECT().CreateAuthorization(appID, userID, scope, redirectURI).Return(nil).Do(func(_, _, _, _ string) {
		close(wait)
	})
	return wait
}

func (suite *HandlerTestSuite) givenAuthDataFound(authCode string, authData cacheuser.OAuthData) {
	cKey := key.OAuthAuthCode(authCode)
	suite.mockUserCacheReader.EXPECT().Get(cKey, gomock.Any()).SetArg(1, authData).Return(nil)
}

func (suite *HandlerTestSuite) givenAuthDataNotFound(authCode string) {
	cKey := key.OAuthAuthCode(authCode)
	suite.mockUserCacheReader.EXPECT().Get(cKey, gomock.Any()).Return(cache.ErrCacheMiss)
}

func (suite *HandlerTestSuite) givenAuthDataCacheError(authCode string, err error) {
	cKey := key.OAuthAuthCode(authCode)
	suite.mockUserCacheReader.EXPECT().Get(cKey, gomock.Any()).Return(err)
}

func (suite *HandlerTestSuite) givenAuthCodeDeleted(authCode string) {
	cKey := key.OAuthAuthCode(authCode)
	suite.mockUserCacheWriter.EXPECT().Del(cKey).Return(nil)
}

func (suite *HandlerTestSuite) TestExchangeAccessToken() {
	const (
		appID       = "test-app"
		appName     = "Test App"
		redirectURI = "https://example.com/callback"
		authCode    = "test-auth-code"
		userID      = "test-user"
	)

	var (
		authedApp = &dbuser.AuthedApp{
			AppID:        appID,
			Name:         appName,
			AppSecret:    "secret",
			SignKey:      null.StringFrom("sign-key"),
			RedirectURIs: []string{redirectURI},
		}
		authData = cacheuser.OAuthData{
			AppID:       appID,
			UserID:      userID,
			RedirectURI: redirectURI,
			Scope:       scopeUserInfoGet,
		}
	)

	testcases := []struct {
		name           string
		queryStrings   map[string]string
		authedApp      *dbuser.AuthedApp
		given          func()
		expectedStatus int
		assertBody     func(body []byte)
	}{
		{
			name: "WHEN valid request THEN return access token",
			queryStrings: map[string]string{
				"auth_code": authCode,
			},
			authedApp: authedApp,
			given: func() {
				suite.givenAuthDataFound(authCode, authData)
				suite.givenAuthCodeDeleted(authCode)

				suite.mockClock.EXPECT().Now().Return(now)
			},
			expectedStatus: http.StatusOK,
			assertBody: func(body []byte) {
				var resp map[string]interface{}
				suite.NoError(json.Unmarshal(body, &resp))
				data := resp["data"].(map[string]interface{})
				suite.NotEmpty(data["access_token"])
				suite.Equal(float64(ttlAccessToken.Seconds()), data["expires_in"])
			},
		},
		{
			name:           "WHEN invalid request THEN return bad request",
			queryStrings:   map[string]string{},
			authedApp:      authedApp,
			given:          func() {},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name: "WHEN invalid app THEN return unauthorized",
			queryStrings: map[string]string{
				"auth_code": authCode,
			},
			authedApp:      nil,
			given:          func() {},
			expectedStatus: http.StatusUnauthorized,
		},
		{
			name: "WHEN app has no sign key THEN return internal server error",
			queryStrings: map[string]string{
				"auth_code": authCode,
			},
			authedApp: &dbuser.AuthedApp{
				AppID:        appID,
				Name:         appName,
				AppSecret:    "secret",
				SignKey:      null.String{},
				RedirectURIs: []string{redirectURI},
			},
			given:          func() {},
			expectedStatus: http.StatusInternalServerError,
		},
		{
			name: "WHEN auth code not found THEN return bad request",
			queryStrings: map[string]string{
				"auth_code": authCode,
			},
			authedApp: authedApp,
			given: func() {
				suite.givenAuthDataNotFound(authCode)
			},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name: "WHEN cache error THEN return internal server error",
			queryStrings: map[string]string{
				"auth_code": authCode,
			},
			authedApp: authedApp,
			given: func() {
				suite.givenAuthDataCacheError(authCode, fmt.Errorf("cache error"))
			},
			expectedStatus: http.StatusInternalServerError,
		},
		{
			name: "WHEN app ID mismatch THEN return bad request",
			queryStrings: map[string]string{
				"auth_code": authCode,
			},
			authedApp: authedApp,
			given: func() {
				mismatchAuthData := cacheuser.OAuthData{
					AppID:       "different-app",
					UserID:      userID,
					RedirectURI: redirectURI,
					Scope:       scopeUserInfoGet,
				}
				suite.givenAuthDataFound(authCode, mismatchAuthData)
			},
			expectedStatus: http.StatusBadRequest,
		},
	}

	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			tc.given()

			reqURL, _ := url.Parse("/v4/auth/token")
			urlValues := url.Values{}
			for k, v := range tc.queryStrings {
				urlValues.Add(k, v)
			}
			reqURL.RawQuery = urlValues.Encode()

			req := httptest.NewRequest(http.MethodGet, reqURL.String(), nil)
			if tc.authedApp != nil {
				ctx := context.WithValue(req.Context(), middleware.KeyAuthedApp, tc.authedApp)
				req = req.WithContext(ctx)
			}
			rr := httptest.NewRecorder()
			suite.app.ServeHTTP(rr, req)

			suite.Equal(tc.expectedStatus, rr.Code)
			if tc.assertBody != nil {
				tc.assertBody(rr.Body.Bytes())
			}
		})
	}
}

func (suite *HandlerTestSuite) TestCreateAuthCode() {
	const (
		appID       = "test-app"
		redirectURI = "https://example.com/callback"
		authCode    = "test-auth-code"
		userID      = "test-user"
	)

	var (
		accessUser = modelmw.AccessUser{
			UserID: userID,
		}
		existedAuthedApp = &dbuser.AuthedApp{
			AppID:        appID,
			AppSecret:    "secret",
			RedirectURIs: []string{redirectURI, "https://example.com/other-callback"},
		}
	)

	testcases := []struct {
		name           string
		reqBody        map[string]interface{}
		accessUser     modelmw.AccessUser
		given          func() chan struct{}
		expectedStatus int
		assertBody     func(body []byte)
	}{
		{
			name: "WHEN valid request THEN return auth code",
			reqBody: map[string]interface{}{
				"app_id":       appID,
				"redirect_uri": redirectURI,
				"scope":        scopeUserInfoGet,
			},
			accessUser: accessUser,
			given: func() chan struct{} {
				suite.givenFoundAuthedAppByID(appID, existedAuthedApp)
				suite.givenRandomStringGenerated(32, authCode)

				authData := cacheuser.OAuthData{
					AppID:       appID,
					UserID:      userID,
					RedirectURI: redirectURI,
					Scope:       scopeUserInfoGet,
				}
				suite.givenAuthDataCachingOK(authCode, authData)
				return suite.givenAuthorizationCreated(appID, userID, scopeUserInfoGet, redirectURI)
			},
			expectedStatus: http.StatusOK,
			assertBody: func(body []byte) {
				var resp createAuthCodeResp
				suite.NoError(json.Unmarshal(body, &resp))
				suite.Equal(authCode, resp.AuthCode)
				suite.Equal(int(ttlAuthUserCode.Seconds()), resp.ExpiresIn)
			},
		},
		{
			name:           "WHEN invalid request THEN return bad request",
			reqBody:        map[string]interface{}{},
			accessUser:     accessUser,
			given:          func() chan struct{} { return nil },
			expectedStatus: http.StatusBadRequest,
		},
		{
			name: "WHEN app not found THEN return bad request",
			reqBody: map[string]interface{}{
				"app_id":       "invalid-app",
				"redirect_uri": redirectURI,
			},
			accessUser: accessUser,
			given: func() chan struct{} {
				suite.mockAppRepo.EXPECT().GetActiveByAppID("invalid-app").Return(nil, nil)
				return nil
			},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name: "WHEN redirect URI mismatch THEN return bad request",
			reqBody: map[string]interface{}{
				"app_id":       appID,
				"redirect_uri": "https://example.com/invalid-callback",
			},
			accessUser: accessUser,
			given: func() chan struct{} {
				suite.givenFoundAuthedAppByID(appID, existedAuthedApp)
				return nil
			},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name: "WHEN cache write fails THEN return internal server error",
			reqBody: map[string]interface{}{
				"app_id":       appID,
				"redirect_uri": redirectURI,
				"scope":        scopeUserInfoGet,
			},
			accessUser: accessUser,
			given: func() chan struct{} {
				suite.givenFoundAuthedAppByID(appID, existedAuthedApp)
				suite.givenRandomStringGenerated(32, authCode)

				authData := cacheuser.OAuthData{
					AppID:       appID,
					UserID:      userID,
					RedirectURI: redirectURI,
					Scope:       scopeUserInfoGet,
				}
				suite.givenAuthDataCachingFailed(authCode, authData, fmt.Errorf("cache write error"))
				return nil
			},
			expectedStatus: http.StatusInternalServerError,
		},
	}

	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			wait := tc.given()

			reqBody, _ := json.Marshal(tc.reqBody)
			req := httptest.NewRequest(http.MethodPost, "/v4/auth/code", bytes.NewReader(reqBody))
			req.Header.Set("Content-Type", "application/json")
			ctx := context.WithValue(req.Context(), middleware.KeyAccessUser, tc.accessUser)
			req = req.WithContext(ctx)
			rr := httptest.NewRecorder()
			suite.app.ServeHTTP(rr, req)

			// Wait for the goroutine to complete if the test case returns a channel
			if wait != nil {
				<-wait
			}

			suite.Equal(tc.expectedStatus, rr.Code)
			if tc.assertBody != nil {
				tc.assertBody(rr.Body.Bytes())
			}
		})
	}
}
