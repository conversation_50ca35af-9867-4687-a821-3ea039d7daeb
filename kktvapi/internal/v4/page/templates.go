package page

var (
	// FIXME the template might be maintaining at CMS in the future
	// following templates are for APP
	fixedTemplateFeaturedPageApp = []SectionType{
		SectionTypeHeadline,
		SectionTypeAnnouncement,
		SectionTypePinnedTitleListAiring,
		SectionTypeAiringSectionAnime,
		SectionTypeBanner,
		SectionTypeColdStartTitleList,
		SectionTypeHighlightTitleList,
		SectionTypeWatchHistory,
		SectionTypeRankingTitleList,
		SectionTypePinnedTitleListNewFinale,
		SectionTypeEditorTitleList,
		SectionTypeExpireSoon,
	}
	fixedTemplateGenrePageApp = []SectionType{
		SectionTypeHeadline,
		SectionTypeFilter,
		SectionTypePinnedTitleListAiring,
		SectionTypeRankingTitleList,
		SectionTypePinnedTitleListNewFinale,
		SectionTypeEditorTitleList,
		SectionTypeAutoTitleList,
		SectionTypeExpireSoon,
	}
	fixedTemplateGenreAnimePageApp = []SectionType{
		SectionTypeHeadline,
		SectionTypeFilter,
		SectionTypeAiringSection,
		SectionTypeRankingTitleList,
		SectionTypeEditorTitleList,
		SectionTypePinnedTitleListNewFinale,
		SectionTypeAutoTitleList,
		SectionTypeExpireSoon,
	}
	fixedTemplateContentAgentApp = []SectionType{
		SectionTypeHeadline,
		SectionTypeFilter,
		SectionTypePinnedTitleListAiring,
		SectionTypePinnedTitleListNewFinale,
		SectionTypeEditorTitleList,
		SectionTypeAutoTitleList,
		SectionTypeExpireSoon,
	}
	fixedTemplatePlanPageApp = []SectionType{
		SectionTypeHeadline,
		SectionTypeFilter,
		SectionTypePinnedTitleList,
		SectionTypeRankingTitleList,
		SectionTypeEditorTitleList,
	}

	// following templates are for WEB
	fixedTemplateFeaturedPageWeb = []SectionType{
		SectionTypeHeadline,
		SectionTypeAnnouncement,
		SectionTypePinnedTitleListAiring,
		SectionTypeRankingTitleList,
		SectionTypeAiringSectionAnime,
		SectionTypeBanner,
		SectionTypeColdStartTitleList,
		SectionTypeHighlightTitleList,
		SectionTypeWatchHistory,
		SectionTypePinnedTitleListNewFinale,
		SectionTypeEditorTitleList,
		SectionTypeExpireSoon,
	}
	fixedTemplateGenrePageWeb      = fixedTemplateGenrePageApp
	fixedTemplateGenreAnimePageWeb = []SectionType{
		SectionTypeHeadline,
		SectionTypeFilter,
		SectionTypeAiringSection,
		SectionTypeRankingTitleList,
		SectionTypeHighlightTitleList,
		SectionTypeEditorTitleList,
		SectionTypePinnedTitleListNewFinale,
		SectionTypeAutoTitleList,
		SectionTypeExpireSoon,
	}
	fixedTemplateContentAgentWeb = fixedTemplateContentAgentApp
	fixedTemplatePlanPageWeb     = fixedTemplatePlanPageApp
)
