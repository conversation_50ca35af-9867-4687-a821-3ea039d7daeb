package page

import "github.com/KKTV/kktv-api-v3/kktvapi/internal/v4/presenter"

// TODO to be removed after console can setup such page filter
// refer to https://docs.google.com/spreadsheets/d/11cFzSfVdsLrXWKsIq25LZohMTvyk60Y_FiGXzzhpwkY/edit#gid=2107818889
var filterMap = map[string][]presenter.FilterOption{
	"genre:戲劇": {
		{DisplayName: "日本", CollectionType: "country", CollectionName: "Japan"},
		{DisplayName: "韓國", CollectionType: "country", CollectionName: "Korea"},
		{DisplayName: "台灣", CollectionType: "country", CollectionName: "Taiwan"},
		{DisplayName: "中國", CollectionType: "country", CollectionName: "China"},
		{DisplayName: "新加坡", CollectionType: "country", CollectionName: "Singapore"},
		{DisplayName: "泰國", CollectionType: "country", CollectionName: "Thailand"},
		{DisplayName: "香港", CollectionType: "country", CollectionName: "Hongkong"},
		{DisplayName: "其他", CollectionType: "country", CollectionName: "Other"},
	},
	"genre:動漫": {
		{DisplayName: "奇幻冒險", CollectionType: "theme", CollectionName: "奇幻冒險"},
		{DisplayName: "戀愛", CollectionType: "theme", CollectionName: "戀愛"},
		{DisplayName: "青春校園", CollectionType: "theme", CollectionName: "青春校園"},
		{DisplayName: "輕鬆喜劇", CollectionType: "theme", CollectionName: "輕鬆喜劇"},
		{DisplayName: "運動競技", CollectionType: "theme", CollectionName: "運動競技"},
		{DisplayName: "懸疑推理", CollectionType: "theme", CollectionName: "懸疑推理"},
		{DisplayName: "靈異怪誕", CollectionType: "theme", CollectionName: "靈異怪誕"},
		{DisplayName: "恐怖驚悚", CollectionType: "theme", CollectionName: "恐怖驚悚"},
		{DisplayName: "音樂偶像", CollectionType: "theme", CollectionName: "音樂偶像"},
		{DisplayName: "歷史軍武", CollectionType: "theme", CollectionName: "歷史軍武"},
		{DisplayName: "職場社會", CollectionType: "theme", CollectionName: "職場社會"},
		{DisplayName: "BL", CollectionType: "theme", CollectionName: "BL"},
		{DisplayName: "動畫電影", CollectionType: "theme", CollectionName: "動畫電影"},
		{DisplayName: "雙語", CollectionType: "theme", CollectionName: "雙語"},
		{DisplayName: "無修正動畫", CollectionType: "theme", CollectionName: "無修正動畫"},
		{DisplayName: "闔家歡", CollectionType: "theme", CollectionName: "闔家歡"},
		{DisplayName: "LGBTQ", CollectionType: "theme", CollectionName: "LGBTQ"},
		{DisplayName: "原創動畫", CollectionType: "theme", CollectionName: "原創動畫"},
		{DisplayName: "特攝", CollectionType: "theme", CollectionName: "特攝"},
	},
	"genre:娛樂": {
		{DisplayName: "旅遊美食", CollectionType: "theme", CollectionName: "旅遊美食"},
		{DisplayName: "知識生活", CollectionType: "theme", CollectionName: "知識生活"},
		{DisplayName: "實境遊戲", CollectionType: "theme", CollectionName: "實境遊戲"},
		{DisplayName: "音樂偶像", CollectionType: "theme", CollectionName: "音樂偶像"},
		{DisplayName: "人物訪談", CollectionType: "theme", CollectionName: "人物訪談"},
	},
	"genre:電影": {
		{DisplayName: "劇情", CollectionType: "theme", CollectionName: "劇情"},
		{DisplayName: "浪漫愛情", CollectionType: "theme", CollectionName: "浪漫愛情"},
		{DisplayName: "輕鬆喜劇", CollectionType: "theme", CollectionName: "輕鬆喜劇"},
		{DisplayName: "動作武打", CollectionType: "theme", CollectionName: "動作武打"},
		{DisplayName: "奇幻冒險", CollectionType: "theme", CollectionName: "奇幻冒險"},
		{DisplayName: "靈異怪誕", CollectionType: "theme", CollectionName: "靈異怪誕"},
		{DisplayName: "恐怖驚悚", CollectionType: "theme", CollectionName: "恐怖驚悚"},
		{DisplayName: "懸疑推理", CollectionType: "theme", CollectionName: "懸疑推理"},
		{DisplayName: "職場社會", CollectionType: "theme", CollectionName: "職場社會"},
		{DisplayName: "家庭親情", CollectionType: "theme", CollectionName: "家庭親情"},
		{DisplayName: "青春校園", CollectionType: "theme", CollectionName: "青春校園"},
		{DisplayName: "歷史鄉土", CollectionType: "theme", CollectionName: "歷史鄉土"},
		{DisplayName: "動畫電影", CollectionType: "theme", CollectionName: "動畫電影"},
		{DisplayName: "紀錄片", CollectionType: "theme", CollectionName: "紀錄片"},
		{DisplayName: "懷念經典", CollectionType: "theme", CollectionName: "懷念經典"},
		{DisplayName: "闔家歡", CollectionType: "theme", CollectionName: "闔家歡"},
		{DisplayName: "LGBTQ", CollectionType: "theme", CollectionName: "LGBTQ"},
	},
	"genre:親子": {
		{DisplayName: "2歲以下", CollectionType: "rating", CollectionName: "2"},
		{DisplayName: "3-5歲", CollectionType: "rating", CollectionName: "5"},
		{DisplayName: "6歲以上", CollectionType: "rating", CollectionName: "6"},
	},
	"plan:lust": {
		{DisplayName: "戲劇", CollectionType: "genre", CollectionName: "戲劇"},
		{DisplayName: "動漫", CollectionType: "genre", CollectionName: "動漫"},
		{DisplayName: "電影", CollectionType: "genre", CollectionName: "電影"},
		{DisplayName: "無修正動畫", CollectionType: "theme", CollectionName: "無修正動畫"},
		{DisplayName: "LGBTQ", CollectionType: "theme", CollectionName: "LGBTQ"},
	},
	"content_agent:采昌": {
		{DisplayName: "劇情", CollectionType: "theme", CollectionName: "劇情"},
		{DisplayName: "浪漫愛情", CollectionType: "theme", CollectionName: "浪漫愛情"},
		{DisplayName: "輕鬆喜劇", CollectionType: "theme", CollectionName: "溫馨喜劇"},
		{DisplayName: "動作武打", CollectionType: "theme", CollectionName: "動作武打"},
		{DisplayName: "奇幻冒險", CollectionType: "theme", CollectionName: "奇幻冒險"},
		{DisplayName: "靈異怪誕", CollectionType: "theme", CollectionName: "靈異怪誕"},
		{DisplayName: "恐怖驚悚", CollectionType: "theme", CollectionName: "恐怖驚悚"},
		{DisplayName: "懸疑推理", CollectionType: "theme", CollectionName: "懸疑推理"},
		{DisplayName: "職場社會", CollectionType: "theme", CollectionName: "職場社會"},
		{DisplayName: "家庭親情", CollectionType: "theme", CollectionName: "家庭親情"},
		{DisplayName: "青春校園", CollectionType: "theme", CollectionName: "青春校園"},
		{DisplayName: "歷史鄉土", CollectionType: "theme", CollectionName: "歷史鄉土"},
		{DisplayName: "動畫電影", CollectionType: "theme", CollectionName: "動畫電影"},
		{DisplayName: "紀錄片", CollectionType: "theme", CollectionName: "紀錄片"},
		{DisplayName: "懷念經典", CollectionType: "theme", CollectionName: "懷念經典"},
		{DisplayName: "闔家歡", CollectionType: "theme", CollectionName: "闔家歡"},
		{DisplayName: "LGBTQ", CollectionType: "theme", CollectionName: "LGBTQ"},
	},
	"content_agent:TVB": {
		{DisplayName: "浪漫愛情", CollectionType: "theme", CollectionName: "浪漫愛情"},
		{DisplayName: "懸疑推理", CollectionType: "theme", CollectionName: "懸疑推理"},
		{DisplayName: "職場社會", CollectionType: "theme", CollectionName: "職場社會"},
		{DisplayName: "輕鬆喜劇", CollectionType: "theme", CollectionName: "輕鬆喜劇"},
		{DisplayName: "靈異怪誕", CollectionType: "theme", CollectionName: "靈異怪誕"},
		{DisplayName: "恐怖驚悚", CollectionType: "theme", CollectionName: "恐怖驚悚"},
		{DisplayName: "奇幻冒險", CollectionType: "theme", CollectionName: "奇幻冒險"},
		{DisplayName: "家庭鄉土", CollectionType: "theme", CollectionName: "家庭鄉土"},
		{DisplayName: "青春校園", CollectionType: "theme", CollectionName: "青春校園"},
		{DisplayName: "時代史劇", CollectionType: "theme", CollectionName: "時代史劇"},
		{DisplayName: "經典懷舊", CollectionType: "theme", CollectionName: "經典懷舊"},
	},
	"content_agent:Medialink": {
		{DisplayName: "奇幻冒險", CollectionName: "奇幻冒險", CollectionType: "theme"},
		{DisplayName: "戀愛", CollectionName: "戀愛", CollectionType: "theme"},
		{DisplayName: "輕鬆喜劇", CollectionName: "輕鬆喜劇", CollectionType: "theme"},
		{DisplayName: "運動競技", CollectionName: "運動競技", CollectionType: "theme"},
		{DisplayName: "懸疑推理", CollectionName: "懸疑推理", CollectionType: "theme"},
		{DisplayName: "靈異怪誕", CollectionName: "靈異怪誕", CollectionType: "theme"},
		{DisplayName: "恐怖驚悚", CollectionName: "恐怖驚悚", CollectionType: "theme"},
		{DisplayName: "音樂偶像", CollectionName: "音樂偶像", CollectionType: "theme"},
		{DisplayName: "歷史軍武", CollectionName: "歷史軍武", CollectionType: "theme"},
		{DisplayName: "職場社會", CollectionName: "職場社會", CollectionType: "theme"},
		{DisplayName: "BL", CollectionName: "BL", CollectionType: "theme"},
		{DisplayName: "動畫電影", CollectionName: "動畫電影", CollectionType: "theme"},
		{DisplayName: "闔家歡", CollectionType: "theme", CollectionName: "闔家歡"},
		{DisplayName: "LGBTQ", CollectionType: "theme", CollectionName: "LGBTQ"},
		{DisplayName: "雙語", CollectionType: "theme", CollectionName: "雙語"},
		{DisplayName: "無修正動畫", CollectionType: "theme", CollectionName: "無修正動畫"},
	},
	"content_agent:華策": {
		{DisplayName: "浪漫愛情", CollectionName: "浪漫愛情", CollectionType: "theme"},
		{DisplayName: "懸疑推理", CollectionName: "懸疑推理", CollectionType: "theme"},
		{DisplayName: "職場社會", CollectionName: "職場社會", CollectionType: "theme"},
		{DisplayName: "輕鬆喜劇", CollectionName: "輕鬆喜劇", CollectionType: "theme"},
		{DisplayName: "靈異怪誕", CollectionName: "靈異怪誕", CollectionType: "theme"},
		{DisplayName: "恐怖驚悚", CollectionName: "恐怖驚悚", CollectionType: "theme"},
		{DisplayName: "奇幻冒險", CollectionName: "奇幻冒險", CollectionType: "theme"},
		{DisplayName: "家庭鄉土", CollectionName: "家庭鄉土", CollectionType: "theme"},
		{DisplayName: "青春校園", CollectionName: "青春校園", CollectionType: "theme"},
		{DisplayName: "時代史劇", CollectionName: "時代史劇", CollectionType: "theme"},
		{DisplayName: "經典懷舊", CollectionName: "經典懷舊", CollectionType: "theme"},
	},
	"content_agent:新傳媒": {
		{DisplayName: "浪漫愛情", CollectionName: "浪漫愛情", CollectionType: "theme"},
		{DisplayName: "懸疑推理", CollectionName: "懸疑推理", CollectionType: "theme"},
		{DisplayName: "職場社會", CollectionName: "職場社會", CollectionType: "theme"},
		{DisplayName: "輕鬆喜劇", CollectionName: "輕鬆喜劇", CollectionType: "theme"},
		{DisplayName: "靈異怪誕", CollectionName: "靈異怪誕", CollectionType: "theme"},
		{DisplayName: "恐怖驚悚", CollectionName: "恐怖驚悚", CollectionType: "theme"},
		{DisplayName: "奇幻冒險", CollectionName: "奇幻冒險", CollectionType: "theme"},
		{DisplayName: "家庭鄉土", CollectionName: "家庭鄉土", CollectionType: "theme"},
		{DisplayName: "青春校園", CollectionName: "青春校園", CollectionType: "theme"},
		{DisplayName: "時代史劇", CollectionName: "時代史劇", CollectionType: "theme"},
		{DisplayName: "經典懷舊", CollectionName: "經典懷舊", CollectionType: "theme"},
	},
	"content_provider:ViuTV": {
		{DisplayName: "浪漫愛情", CollectionName: "浪漫愛情", CollectionType: "theme"},
		{DisplayName: "懸疑推理", CollectionName: "懸疑推理", CollectionType: "theme"},
		{DisplayName: "職場社會", CollectionName: "職場社會", CollectionType: "theme"},
		{DisplayName: "輕鬆喜劇", CollectionName: "輕鬆喜劇", CollectionType: "theme"},
		{DisplayName: "靈異怪誕", CollectionName: "靈異怪誕", CollectionType: "theme"},
		{DisplayName: "恐怖驚悚", CollectionName: "恐怖驚悚", CollectionType: "theme"},
		{DisplayName: "奇幻冒險", CollectionName: "奇幻冒險", CollectionType: "theme"},
		{DisplayName: "家庭鄉土", CollectionName: "家庭鄉土", CollectionType: "theme"},
		{DisplayName: "青春校園", CollectionName: "青春校園", CollectionType: "theme"},
		{DisplayName: "時代史劇", CollectionName: "時代史劇", CollectionType: "theme"},
		{DisplayName: "經典懷舊", CollectionName: "經典懷舊", CollectionType: "theme"},
	},
}

type autoGenListConf struct {
	FromCollectionType string
	GenerationFactors  []generationFactor
}

type generationFactor struct {
	FromCollectionName string
	DisplayName        string // name of the auto title list
}

var autoGenTitlelistConfs = map[string]autoGenListConf{
	"genre:戲劇": {
		FromCollectionType: "country",
		GenerationFactors: []generationFactor{
			{FromCollectionName: "Japan", DisplayName: "咪納桑～追劇囉爹司"},
			{FromCollectionName: "Korea", DisplayName: "韓劇即天堂"},
			{FromCollectionName: "Taiwan", DisplayName: "台劇復興一起來"},
			{FromCollectionName: "Hongkong", DisplayName: "港劇猴賽雷"},
			{FromCollectionName: "China", DisplayName: "遙遠的東方有一條龍"},
			{FromCollectionName: "Other", DisplayName: "古今中外面面劇到"},
		},
	},
	"genre:動漫": {
		FromCollectionType: "themes",
		GenerationFactors: []generationFactor{
			{FromCollectionName: "奇幻冒險", DisplayName: "去異世界比出國容易？"},
			{FromCollectionName: "戀愛", DisplayName: "我沒有談的那場戀愛"},
			{FromCollectionName: "輕鬆喜劇", DisplayName: "笑死"},
			{FromCollectionName: "運動競技", DisplayName: "全動畫運動會"},
			{FromCollectionName: "懸疑推理", DisplayName: "你的真相不是你的真相"},
			{FromCollectionName: "靈異怪誕", DisplayName: "細思極恐"},
			{FromCollectionName: "恐怖驚悚", DisplayName: "恐怖喔～恐怖到了極點喔"},
			{FromCollectionName: "音樂偶像", DisplayName: "愛★勇氣★希望"},
			{FromCollectionName: "歷史軍武", DisplayName: "我歷史都考一百分"},
			{FromCollectionName: "職場社會", DisplayName: "社畜的一百種生活"},
			{FromCollectionName: "BL", DisplayName: "兩個臭男生，真香"},
			{FromCollectionName: "動畫電影", DisplayName: "經費燃燒劇場版"},
			{FromCollectionName: "闔家歡", DisplayName: "全家的餐桌"},
			{FromCollectionName: "青春校園", DisplayName: "那些年我們一起走過的青春"},
			{FromCollectionName: "LGBTQ", DisplayName: "無關性別，你就是你"},
		},
	},
	"genre:娛樂": {
		FromCollectionType: "themes",
		GenerationFactors: []generationFactor{
			{FromCollectionName: "旅遊美食", DisplayName: "感覺對了我要出發"},
			{FromCollectionName: "知識生活", DisplayName: "長知識"},
			{FromCollectionName: "實境遊戲", DisplayName: "要活就要會玩遊戲"},
			{FromCollectionName: "音樂偶像", DisplayName: "No Music No Life"},
			{FromCollectionName: "人物訪談", DisplayName: "他和她的美麗與哀愁"},
		},
	},
	"genre:電影": {
		FromCollectionType: "themes",
		GenerationFactors: []generationFactor{
			{FromCollectionName: "劇情", DisplayName: "沒有一部電影不能解決的事"},
			{FromCollectionName: "浪漫愛情", DisplayName: "愛在點下播放時"},
			{FromCollectionName: "溫馨喜劇", DisplayName: "近看是悲劇，遠看是喜劇"},
			{FromCollectionName: "動作武打", DisplayName: "要打去電影院打"},
			{FromCollectionName: "奇幻冒險", DisplayName: "想像力就是我的超能力"},
			{FromCollectionName: "靈異驚悚", DisplayName: "恐怖喔～恐怖到了極點喔"},
			{FromCollectionName: "懸疑推理", DisplayName: "到底真相放在哪裡"},
			{FromCollectionName: "職場社會", DisplayName: "我的志願與我的現實"},
			{FromCollectionName: "家庭親情", DisplayName: "家家有本難念的經"},
			{FromCollectionName: "青春校園", DisplayName: "青春情懷總是詩"},
			{FromCollectionName: "歷史鄉土", DisplayName: "以史為鏡，可以知興替"},
			{FromCollectionName: "動畫電影", DisplayName: "經費燃燒劇場版"},
			{FromCollectionName: "紀錄片", DisplayName: "真人真事加倍離奇"},
			{FromCollectionName: "懷念經典", DisplayName: "片是老的好"},
		},
	},
	"genre:親子": {
		FromCollectionType: "themes",
		GenerationFactors: []generationFactor{
			{FromCollectionName: "闔家歡", DisplayName: "闔家歡"},
			{FromCollectionName: "寓教於樂", DisplayName: "寓教於樂"},
			{FromCollectionName: "動畫電影", DisplayName: "動畫電影"},
			{FromCollectionName: "雙語", DisplayName: "雙語"},
			{FromCollectionName: "尼克兒童頻道", DisplayName: "尼克兒童頻道"},
		},
	},
	"content_agent:采昌": {
		FromCollectionType: "themes",
		GenerationFactors: []generationFactor{
			{FromCollectionName: "劇情", DisplayName: "沒有一部電影不能解決的事"},
			{FromCollectionName: "浪漫愛情", DisplayName: "愛在點下播放時"},
			{FromCollectionName: "溫馨喜劇", DisplayName: "近看是悲劇，遠看是喜劇"},
			{FromCollectionName: "動作武打", DisplayName: "要打去電影院打"},
			{FromCollectionName: "奇幻冒險", DisplayName: "想像力就是我的超能力"},
			{FromCollectionName: "靈異驚悚", DisplayName: "恐怖喔～恐怖到了極點喔"},
			{FromCollectionName: "懸疑推理", DisplayName: "到底真相放在哪裡"},
			{FromCollectionName: "職場社會", DisplayName: "我的志願與我的現實"},
			{FromCollectionName: "家庭親情", DisplayName: "家家有本難念的經"},
			{FromCollectionName: "青春校園", DisplayName: "青春情懷總是詩"},
			{FromCollectionName: "歷史鄉土", DisplayName: "以史為鏡，可以知興替"},
			{FromCollectionName: "動畫電影", DisplayName: "經費燃燒劇場版"},
			{FromCollectionName: "紀錄片", DisplayName: "真人真事加倍離奇"},
			{FromCollectionName: "懷念經典", DisplayName: "片是老的好"},
		},
	},
	"content_agent:TVB": {
		FromCollectionType: "themes",
		GenerationFactors: []generationFactor{
			{FromCollectionName: "浪漫愛情", DisplayName: "浪漫愛情"},
			{FromCollectionName: "懸疑推理", DisplayName: "懸疑推理"},
			{FromCollectionName: "職場社會", DisplayName: "職場社會"},
			{FromCollectionName: "輕鬆喜劇", DisplayName: "輕鬆喜劇"},
			{FromCollectionName: "靈異怪誕", DisplayName: "細思極恐"},
			{FromCollectionName: "恐怖驚悚", DisplayName: "恐怖喔～恐怖到了極點喔"},
			{FromCollectionName: "奇幻冒險", DisplayName: "奇幻冒險"},
			{FromCollectionName: "家庭鄉土", DisplayName: "家庭鄉土"},
			{FromCollectionName: "青春校園", DisplayName: "青春校園"},
			{FromCollectionName: "時代史劇", DisplayName: "時代史劇"},
			{FromCollectionName: "經典懷舊", DisplayName: "經典懷舊"},
		},
	},
	"content_agent:Medialink": {
		FromCollectionType: "themes",
		GenerationFactors: []generationFactor{
			{FromCollectionName: "奇幻冒險", DisplayName: "去異世界比出國容易？"},
			{FromCollectionName: "戀愛", DisplayName: "我沒有談的那場戀愛"},
			{FromCollectionName: "青春校園", DisplayName: "那些年我們一起走過的青春"},
			{FromCollectionName: "輕鬆喜劇", DisplayName: "笑死"},
			{FromCollectionName: "運動競技", DisplayName: "全動畫運動會"},
			{FromCollectionName: "懸疑推理", DisplayName: "你的真相不是你的真相"},
			{FromCollectionName: "靈異怪誕", DisplayName: "細思極恐"},
			{FromCollectionName: "恐怖驚悚", DisplayName: "恐怖喔～恐怖到了極點喔"},
			{FromCollectionName: "音樂偶像", DisplayName: "愛★勇氣★希望"},
			{FromCollectionName: "歷史軍武", DisplayName: "我歷史都考一百分"},
			{FromCollectionName: "職場社會", DisplayName: "社畜的一百種生活"},
			{FromCollectionName: "BL", DisplayName: "兩個臭男生，真香"},
			{FromCollectionName: "動畫電影", DisplayName: "經費燃燒劇場版"},
			{FromCollectionName: "闔家歡", DisplayName: "全家的餐桌"},
			{FromCollectionName: "LGBTQ", DisplayName: "無關性別，你就是你"},
		},
	},
	"content_agent:華策": {
		FromCollectionType: "themes",
		GenerationFactors: []generationFactor{
			{FromCollectionName: "浪漫愛情", DisplayName: "浪漫愛情"},
			{FromCollectionName: "懸疑推理", DisplayName: "懸疑推理"},
			{FromCollectionName: "職場社會", DisplayName: "職場社會"},
			{FromCollectionName: "輕鬆喜劇", DisplayName: "輕鬆喜劇"},
			{FromCollectionName: "靈異怪誕", DisplayName: "細思極恐"},
			{FromCollectionName: "恐怖驚悚", DisplayName: "恐怖喔～恐怖到了極點喔"},
			{FromCollectionName: "奇幻冒險", DisplayName: "奇幻冒險"},
			{FromCollectionName: "家庭鄉土", DisplayName: "家庭鄉土"},
			{FromCollectionName: "青春校園", DisplayName: "青春校園"},
			{FromCollectionName: "時代史劇", DisplayName: "時代史劇"},
			{FromCollectionName: "經典懷舊", DisplayName: "經典懷舊"},
		},
	},
	"content_agent:新傳媒": {
		FromCollectionType: "themes",
		GenerationFactors: []generationFactor{
			{FromCollectionName: "浪漫愛情", DisplayName: "浪漫愛情"},
			{FromCollectionName: "懸疑推理", DisplayName: "懸疑推理"},
			{FromCollectionName: "職場社會", DisplayName: "職場社會"},
			{FromCollectionName: "輕鬆喜劇", DisplayName: "輕鬆喜劇"},
			{FromCollectionName: "靈異怪誕", DisplayName: "細思極恐"},
			{FromCollectionName: "恐怖驚悚", DisplayName: "恐怖喔～恐怖到了極點喔"},
			{FromCollectionName: "奇幻冒險", DisplayName: "奇幻冒險"},
			{FromCollectionName: "家庭鄉土", DisplayName: "家庭鄉土"},
			{FromCollectionName: "青春校園", DisplayName: "青春校園"},
			{FromCollectionName: "時代史劇", DisplayName: "時代史劇"},
			{FromCollectionName: "經典懷舊", DisplayName: "經典懷舊"},
		},
	},
	"content_provider:ViuTV": {
		FromCollectionType: "themes",
		GenerationFactors: []generationFactor{
			{FromCollectionName: "懸疑推理", DisplayName: "真相只有一個"},
			{FromCollectionName: "職場社會", DisplayName: "打工仔的血淚史"},
			{FromCollectionName: "旅遊美食", DisplayName: "一起吃飯不無聊"},
			{FromCollectionName: "知識生活", DisplayName: "港式生活，你知唔知？"},
		},
	},
}
