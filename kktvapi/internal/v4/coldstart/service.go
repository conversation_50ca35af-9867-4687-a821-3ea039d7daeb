//go:generate mockgen -source service.go -destination service_mock.go -package coldstart
package coldstart

import (
	"fmt"
	"math"
	"strings"
	"sync"

	"github.com/KKTV/kktv-api-v3/kktvapi/config"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/coldstart"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/meta"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/kktverror"
	"github.com/KKTV/kktv-api-v3/pkg/clock"
	"github.com/KKTV/kktv-api-v3/pkg/elasticsearch"
	"github.com/KKTV/kktv-api-v3/pkg/encrypt"
	plog "github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/model/cacheuser"
)

const (
	titlesPerRound = 20
)

type Service interface {
	IsUserColdStarted(userID, deviceID string) (bool, error)
	// GetRecommendTitleListForUser returns a list of title id those are titles recommended for a user, use refresh to get next page offset of result
	GetRecommendTitleListForUser(userID, deviceID string, refresh bool) ([]string, error)
	UpdateUserPreference(userID, deviceID string, optGroup []OptionGroup) error
}

var (
	srv     Service
	onceSrv sync.Once
)

type service struct {
	coldStartRepo coldstart.Repository
	titlelistRepo meta.TitlelistRepository

	esClient elasticsearch.Client
	clock    clock.Clock
	codeGen  encrypt.CodeGen
}

func NewService() Service {
	onceSrv.Do(func() {
		srv = &service{
			coldStartRepo: coldstart.NewRepository(),
			titlelistRepo: meta.NewTitleListRepository(),
			clock:         clock.New(),
			esClient:      elasticsearch.NewClient(config.SearchHost),
			codeGen:       encrypt.NewCodeGen(),
		}
	})
	return srv
}

func (s *service) IsUserColdStarted(userID, deviceID string) (bool, error) {
	if up, err := s.coldStartRepo.GetUserPreference(userID, deviceID); err != nil {
		return false, err
	} else if up == nil {
		return false, nil
	}
	return true, nil
}

type condition struct {
	GenreLevel  string
	ThemesLevel []string
}

func (c *condition) getGenreAndCountry() (genreKey, countryKey string) {
	strs := strings.Split(c.GenreLevel, "+")
	if len(strs) == 2 {
		genre, country := strs[0], strs[1]
		genreStrs, countryStrs := strings.Split(genre, ":"), strings.Split(country, ":")
		genreKey, countryKey = genreStrs[1], countryStrs[1]
	} else if len(strs) == 1 {
		genre := strs[0]
		genreKey = strings.Split(genre, ":")[1]
	}
	return
}

func (c *condition) getThemes() (themes []string) {
	for _, theme := range c.ThemesLevel {
		themes = append(themes, strings.Split(theme, ":")[1])
	}
	return
}

func (s *service) GetRecommendTitleListForUser(userID, deviceID string, refresh bool) ([]string, error) {
	userPref, err := s.coldStartRepo.GetUserPreference(userID, deviceID)
	if err != nil {
		return nil, err
	} else if userPref == nil {
		return nil, fmt.Errorf("%w: user preference", kktverror.ErrResourceNotFound)
	}

	if refresh {
		userPref.CurrentPage++
	}

	// checkout from recommend title pool cached before
	titlesPool, err := s.getCachedTitlePool(userPref)
	plog.Debug("get recommend title list: user preferred").
		Str("userID", userID).Str("deviceID", deviceID).
		Interface("user_pref", userPref).Interface("title_pool", titlesPool).Send()
	if err != nil {
		return nil, err
	}

	prefNeedUpdate := refresh
	if userPref.TitlePoolHashcode != titlesPool.Hashcode {
		userPref.TitlePoolHashcode, userPref.CurrentPage = titlesPool.Hashcode, 0
		prefNeedUpdate = true
	}
	if prefNeedUpdate {
		if err := s.coldStartRepo.UpdateUserPreference(userID, deviceID, userPref); err != nil {
			plog.Error("get recommend title list: update user pref fail").
				Str("userID", userID).Str("deviceID", deviceID).
				Interface("user_pref", userPref).Err(err).Send()
		}
	}

	titlesInPool := titlesPool.TitleIDs
	var result []string
	if len(titlesInPool) > 0 {
		mod := int(math.Ceil(float64(len(titlesInPool)) / float64(titlesPerRound)))
		fromIdx := (userPref.CurrentPage % mod) * titlesPerRound
		toIdx := fromIdx + titlesPerRound
		if notEnough := toIdx > len(titlesInPool); notEnough {
			toIdx = len(titlesInPool)
		}
		result = titlesInPool[fromIdx:toIdx]
	}
	if titlesPerRound > len(result) {
		result = s.complementWithRankingTitleList(result, userPref.Preferred.OptionsKey())
	}

	return result, nil
}

func (s *service) complementWithRankingTitleList(result []string, optKey string) []string {
	needMore := titlesPerRound - len(result)
	if complement, err := s.titlelistRepo.GetRanking("genre:featured", s.clock.Now()); err != nil || complement == nil {
		plog.Error("get recommend title list: get featured ranking titlelist fail").
			Interface("list", complement).Err(err).Send()
	} else {
		rankingTitles := complement.Meta.TitleID
		if len(rankingTitles) <= needMore {
			needMore = len(rankingTitles)
		}
		more := rankingTitles[:needMore]
		plog.Info("get recommend title list: complement with ranking titlelist").Str("pool_key", optKey).
			Strs("current_page_titles", result).Strs("complement_titles", more).Send()
		result = union(result, more)
	}
	return result
}

func (s *service) genHashcode(arr []string) string {
	return s.codeGen.StringsHashcode(arr, "+")
}

func (s *service) makeRecommendConditions(userPref *cacheuser.ColdStartUserPreference, relation *cacheuser.ColdStartOptRelation) []*condition {
	conditions := make([]*condition, 0)
	genreLevelIdx, themeLevelIdx := 0, 1

	for _, themeName := range userPref.Preferred[themeLevelIdx].ItemKeys {
		lv1Item, ok := relation.Lv1[themeName]
		if !ok {
			plog.Warn("get recommend title list for user: theme option not found").Str("theme", themeName).Send()
			continue
		} else if lv1Item.Type == "genre" {
			// case that not to intersect with other level1 genres, itself is a genre
			conditions = append(conditions, &condition{
				GenreLevel: lv1Item.Collection[0],
			})
			continue
		}

		for _, genreKey := range userPref.Preferred[genreLevelIdx].ItemKeys {
			lv0Item, ok := relation.Lv0[genreKey]
			if !ok {
				plog.Warn("get recommend title list for user: genre option not found").Str("genre", genreKey).Send()
				continue
			}
			conditions = append(conditions, &condition{
				GenreLevel:  genreKey,
				ThemesLevel: lv1Item.GetCollections(lv0Item.Genre),
			})
		}
	}
	return conditions
}

func (s *service) genRecommendTitlesByCondition(conditions []*condition) []string {
	candidateListPool := make([][]string, 0)
	candidateCount := 0
	for _, c := range conditions {
		// generate one newest title list and one hottest title list for each condition
		newestTitleIDs, hottestTitleList := s.getNewestTitleList(c), s.getHottestTitleList(c)
		plog.Debug("get recommend title list: hottest and newest title list").
			Strs("newest", newestTitleIDs).Strs("hottest", hottestTitleList).
			Interface("condition", c).Send()
		if len(hottestTitleList) > 0 {
			candidateListPool = append(candidateListPool, hottestTitleList)
		}
		if len(newestTitleIDs) > 0 {
			candidateListPool = append(candidateListPool, newestTitleIDs)
		}
		candidateCount += len(newestTitleIDs) + len(hottestTitleList)
	}

	result := make([]string, 0)
	included := map[string]struct{}{}
	// compose a result list of title id from the candidate lists by looping lists in the candidateListPool
	for i := 0; candidateCount > 0; i = (i + 1) % len(candidateListPool) {
		list := &candidateListPool[i]
		if len(*list) == 0 {
			continue
		}
		// pop the first item from the list
		for titleID := ""; len(*list) > 0; {
			titleID, candidateListPool[i] = (*list)[0], (*list)[1:]
			candidateCount--
			if _, isExist := included[titleID]; !isExist {
				included[titleID] = struct{}{}
				result = append(result, titleID)
				break
			}
		}
	}
	return result
}

// newest titles from the ES
func (s *service) getNewestTitleList(c *condition) []string {
	shoulds := make([]elasticsearch.BoolFilter, 0)
	genre, country := c.getGenreAndCountry()
	if len(c.getThemes()) == 0 {
		terms := []map[string]interface{}{
			{"genre": genre},
		}
		if country != "" {
			terms = append(terms, map[string]interface{}{"country": country})
		}
		shoulds = append(shoulds, elasticsearch.BoolFilter{TermOpts: terms})
	} else {
		for _, theme := range c.getThemes() {
			terms := []map[string]interface{}{
				{"theme": theme},
				{"genre": genre},
			}
			if country != "" {
				terms = append(terms, map[string]interface{}{"country": country})
			}
			filter := elasticsearch.BoolFilter{TermOpts: terms}
			shoulds = append(shoulds, filter)
		}
	}

	var result elasticsearch.SearchResp[struct{}]
	if err := s.esClient.SearchTitle(elasticsearch.SearchTitlePayload{
		Should: shoulds,
		Sort: elasticsearch.Sort{
			Field: "sortid", Order: "desc",
		},
		From: 0,
		Size: 20,
	}, &result); err != nil {
		return nil
	}
	titleIDs := make([]string, len(result.Hits.Hits))
	for i, item := range result.Hits.Hits {
		titleIDs[i] = item.ID
	}
	return titleIDs
}

// hottest titles from the cronjob
func (s *service) getHottestTitleList(c *condition) []string {
	if c.GenreLevel == "" {
		return nil
	}
	result := make([]string, 0)
	if lv0IDs, err := s.coldStartRepo.GetHottestTitleIDsByOption(c.GenreLevel); err != nil {
		plog.Warn("get coldstart recommended titlelist: get hottest title list failed").Str("genre_key", c.GenreLevel).Err(err).Send()
		return nil
	} else if len(lv0IDs) > 0 {
		result = append(result, lv0IDs...)
	}
	if len(c.ThemesLevel) > 0 {
		lv1Results := make([]string, 0)
		for _, theme := range c.ThemesLevel {
			titleIDs, err := s.coldStartRepo.GetHottestTitleIDsByOption(theme)
			if err != nil {
				plog.Warn("get coldstart recommended titlelist: get hottest title list failed").Str("theme", theme).Err(err).Send()
				return nil
			}
			lv1Results = union(lv1Results, titleIDs)
		}
		result = intersection(result, lv1Results)
	}
	return result
}

func (s *service) getCachedTitlePool(userPref *cacheuser.ColdStartUserPreference) (*cacheuser.ColdStartOptionsTitlePool, error) {
	optionsKey := userPref.Preferred.OptionsKey()
	titlesPool, err := s.coldStartRepo.GetRecommendTitlePool(optionsKey)
	if err != nil {
		return nil, err
	} else if titlesPool == nil {
		relation, err := s.coldStartRepo.GetOptionsRelations()
		if err != nil {
			return nil, err
		}
		conditions := s.makeRecommendConditions(userPref, relation)
		plog.Debug("get recommend title list: refresh title pool").Interface("conditions", conditions).Send()

		titles := s.genRecommendTitlesByCondition(conditions)
		//cache title pool for next time query by same condition
		titlesPool = &cacheuser.ColdStartOptionsTitlePool{
			Hashcode:  s.genHashcode(titles),
			TitleIDs:  titles,
			CreatedAt: s.clock.Now(),
		}
		if err := s.coldStartRepo.UpdateRecommendTitlePool(optionsKey, titlesPool); err != nil {
			plog.Warn("get recommend title list: coldStartRepo update titles fail").Str("optionsKey", optionsKey).
				Interface("title_pool", titles).Err(err).Send()
		}
	}
	return titlesPool, nil
}

func (s *service) UpdateUserPreference(userID, deviceID string, optGroup []OptionGroup) error {
	userPref, err := s.coldStartRepo.GetUserPreference(userID, deviceID)
	if err != nil {
		return err
	} else if userPref == nil {
		userPref = &cacheuser.ColdStartUserPreference{}
	}
	preferred := make(cacheuser.ColdStartUserPreferredGroups, 0)
	for _, group := range optGroup {
		// TODO check id is valid
		// if group.ID == "genre" {
		preferred = append(preferred, cacheuser.ColdStartUserPreferredGroup{
			ID:       group.Id,
			ItemKeys: group.OptionKeys,
		})
		// TODO check each option key is valid
		// for _, key := range group.OptionKeys {
		// 	if !s.isValidOptionKey(key) {
	}
	userPref.Preferred = preferred
	userPref.CurrentPage = 0 // reset the current page once updating preference
	userPref.UpdatedAt = s.clock.Now().Unix()

	return s.coldStartRepo.UpdateUserPreference(userID, deviceID, userPref)
}
