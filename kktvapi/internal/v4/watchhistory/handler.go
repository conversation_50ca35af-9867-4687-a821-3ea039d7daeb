package watchhistory

import (
	"errors"
	"fmt"
	"net/http"

	"github.com/KKTV/kktv-api-v3/kkapp/model"
	legacydbmeta "github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/deeplink"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/middleware"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/rest"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/v4/presenter"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/kktverror"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/permission"
	"github.com/KKTV/kktv-api-v3/pkg/datatype"
	plog "github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	modelmw "github.com/KKTV/kktv-api-v3/pkg/model/middleware"
	"github.com/KKTV/kktv-api-v3/pkg/render"
)

type Handler struct {
	service           Service
	permissionService permission.Service
}

func NewHandler() *Handler {
	return &Handler{
		service:           NewService(),
		permissionService: container.PermissionService(),
	}
}

var (
	displayLabels = map[presenter.LabelType]struct{}{
		presenter.LabelTypeNewArrival:	{},
	}
)

func (h *Handler) GetByUser(w http.ResponseWriter, r *http.Request) {
	access := r.Context().Value(middleware.KeyAccessUser).(modelmw.AccessUser)
	userID := access.UserID

	watched, err := h.service.GetByUserID(userID)
	if err != nil && !errors.Is(err, ErrNotFound) {
		plog.Error("v4WatchHistoryHandler: service: fail to GetByUser").Str("user_id", userID).Err(err).Send()
		resp := rest.Error(ErrUnknown.Message, ErrUnknown.Code)
		render.JSON(w, http.StatusInternalServerError, resp)
		return
	}

	items := make([]HistoryItem, 0, len(watched))
	for _, titleDetail := range watched {
		ep := titleDetail.LastPlayedEpisode
		lastPlayed := ep.LastPlayed

		item := HistoryItem{
			Title: &Title{
				TitleBasicInfo: presenter.TitleBasicInfo{
					ID:             titleDetail.ID,
					Name:           titleDetail.Title,
					TitleType:      titleDetail.TitleType,
					Status:         titleDetail.Status,
					IsEnding:       titleDetail.IsEnding,
					ChildLock:      titleDetail.ChildLock,
					AllowFreeTrial: titleDetail.FreeTrial,
				},
				LatestUpdateInfo: titleDetail.LatestUpdateInfo,
				Labels:           make([]presenter.Label, 0),
			},
			Ep: &Episode{
				ID:           ep.ID,
				Name:         ep.Title,
				Duration:     ep.Duration,
				Still:        ep.Still,
				PlayedOffset: lastPlayed.LastPlayedOffset,
				Deeplink:     deeplink.ContinuePlay(ep.ID, lastPlayed.LastPlayedOffset),
				IsAVOD:       ep.IsAvod,
			},
			Display: &DisplayInfo{
				Title:     titleDetail.Title,
				Desc:      fmt.Sprintf("%s / %s", ep.Title, titleDetail.LatestUpdateInfo),
				DescShort: titleDetail.LatestUpdateInfo,
				PlayHint:  ep.Title,
			},
		}
		for _, label := range titleDetail.ContentLabels {
			if _, ok := displayLabels[presenter.LabelType(label)]; !ok {
				continue
			}
			item.Title.Labels = append(item.Title.Labels, presenter.GetLabel(presenter.LabelType(label)))
		}
		canPlay, err := h.isCanPlay(titleDetail, access.Memberships)
		if err != nil {
			plog.Error("v4WatchHistoryHandler: isCanPlay").Err(err).
				Interface("title", titleDetail).Interface("access", access).Send()
			render.JSON(w, http.StatusInternalServerError, rest.Error(ErrUnknown.Message, ErrUnknown.Code))
			return
		}
		item.UserAuthority = &presenter.VideoAuthority{
			CanPlay: canPlay,
		}
		if lastPlayed.LastPlayedOffset != 0 && ep.Duration != 0 {
			item.Display.PlayedPercentage = datatype.RoundedFloat(float64(lastPlayed.LastPlayedOffset) / float64(ep.Duration))
		}
		items = append(items, item)
	}

	result := map[string]interface{}{
		"items": items,
	}
	resp := rest.Ok()
	resp.Data = result
	render.JSONOk(w, resp)
}

func (h *Handler) isCanPlay(titleDetail *model.UserTitleDetail, membership dbuser.Membership) (bool, error) {
	ep := titleDetail.LastPlayedEpisode
	err := h.permissionService.Grant(permission.RequestFullAccessTitleDetail(titleDetail.TitleDetail, membership))
	if kktverror.IsInternalErr(err) {
		return false, fmt.Errorf("request full access permission: %w", err)
	}
	fullAccess := err == nil
	if fullAccess {
		return true, nil
	}
	err = h.permissionService.Grant(permission.RequestPlayEpisode(
		&legacydbmeta.EpisodeMeta{IsAvod: ep.IsAvod}, fullAccess,
	))
	if kktverror.IsInternalErr(err) {
		return false, fmt.Errorf("request play episode permission: %w", err)
	}
	canPlay := err == nil
	return canPlay, nil
}
