//go:generate mockgen -source service.go -destination service_mock.go -package watchhistory
package watchhistory

import (
	"errors"
	"sync"

	legacymodel "github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/user"
	"github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbmeta"
)

var (
	srv     Service
	onceSrv sync.Once
)

var (
	ErrNotFound = errors.New("user's watch history not found")
)

type Service interface {
	GetByUserID(userID string) (WatchedTitles, error)
}

type service struct {
	repo         user.WatchHistoryRepository
	legacyHelper legacyHelper
}

func NewService() Service {
	onceSrv.Do(func() {
		srv = &service{
			repo:         user.NewWatchHistoryRepository(),
			legacyHelper: &legacyHelperImpl{},
		}
	})
	return srv
}

type WatchedTitles []*legacymodel.UserTitleDetail
type WatchedTitle *legacymodel.UserTitleDetail

func (s *service) GetByUserID(userID string) (WatchedTitles, error) {
	titleIDs, err := s.repo.GetByUserID(userID)
	if err != nil {
		return nil, err
	} else if titleIDs == nil {
		return nil, ErrNotFound
	}

	size := len(titleIDs)
	if size > 20 {
		titleIDs = titleIDs[:20]
	}

	// [BEGIN] FIXME: in order to renew API ASAP, we reuse the legacy code for now, should be removed and re-implement later
	legacyTitleDetails, err := s.legacyHelper.NewUserTitleDetails(userID, titleIDs)
	if err != nil {
		return nil, err
	}

	result := make(WatchedTitles, 0)
	for _, detail := range legacyTitleDetails {
		// watch history should not contain LIVE titles
		if detail.TitleType == dbmeta.TitleTypeLive.String() {
			continue
		}
		ep := detail.LastPlayedEpisode
		if ep.ID == "" {
			log.Warn("v4WatchHistoryService: no episode id found for title").
				Str("title_id", detail.ID).Str("user_id", userID).Send()
			continue
		}
		result = append(result, detail)
	}
	if needPurge := size > 28; needPurge {
		go func() {
			if err := s.repo.PurgeAfter(userID, 21); err != nil {
				log.Error("v4WatchHistoryService: failed to purge watch history").Err(err).Str("user_id", userID).Send()
			}
		}()
	}
	return result, nil
}
