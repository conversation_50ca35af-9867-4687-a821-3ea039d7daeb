package order

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/v0/internal/errs"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/auditing"
	"github.com/KKTV/kktv-api-v3/pkg/amplitudelib"
	"github.com/KKTV/kktv-api-v3/pkg/datetimer"
	"github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/duke-git/lancet/v2/datetime"
	"github.com/jinzhu/copier"
	"gopkg.in/guregu/null.v3"
)

// modSubscriber implements the subscriber interface to process the subscribe process for CHT MOD user
type modSubscriber struct {
	srv          *service
	user         *dbuser.User
	product      *dbuser.Product
	req          *SubscribeReq
	subscriberId string
}

type modSubscribeInfo struct {
	SubscriberId   string `json:"subscriberId,omitempty"`
	ItemId         string `json:"itemId,omitempty"`
	Type           string `json:"type,omitempty"`
	Price          string `json:"price,omitempty"`
	StartTime      int64  `json:"startTime,omitempty"`
	ProductName    string `json:"productName,omitempty"`
	SubscriberArea string `json:"subscriberArea,omitempty"`
}

func (m *modSubscriber) fulfillOrder() (*subscribeDetail, error) {
	var subscribeInfo = new(modSubscribeInfo)
	if m.req.Info != nil {
		if err := json.Unmarshal(m.req.Info, subscribeInfo); err != nil {
			return nil, errs.ErrBadRequest
		}
	}
	m.subscriberId = subscribeInfo.SubscriberId

	srv, user, pdt := m.srv, m.user, m.product
	ids := srv.serialGen.OrderIDs(2, pdt.PaymentType.String(), pdt.PaymentTypeCode)

	now := srv.clock.Now()
	startDate, endDate := m.getOrderPeriod(subscribeInfo, pdt)

	info := map[string]interface{}{
		"subscribeInfo": subscribeInfo,
	}
	infoJson, _ := json.Marshal(info) //info won't be nil because it's checked in Subscribe
	fulfilledOrder := &dbuser.Order{
		ID:          ids[0],
		UserID:      user.ID,
		ProductID:   pdt.ID,
		ProductName: null.StringFrom(pdt.Name),
		Price:       int64(pdt.Price),
		PriceNoTax:  int64(pdt.PriceNoTax),
		PaymentType: pdt.PaymentType.String(),
		TaxRate:     pdt.TaxRate,
		Fee:         pdt.Fee,
		StartDate:   startDate,
		EndDate:     endDate,
		OrderDate:   now,
		Status:      null.StringFrom(dbuser.OrderStatusOK.String()),
		RealizedAt:  null.TimeFrom(now),
		Info:        null.StringFrom(string(infoJson)),
	}

	nextStartDate, nextEndDate := endDate, endDate.Add(pdt.GetDuration())
	unfulfilledOrder := &dbuser.Order{
		ID:          ids[1],
		UserID:      user.ID,
		ProductID:   pdt.ID,
		ProductName: null.StringFrom(pdt.Name),
		PaymentType: pdt.PaymentType.String(),
		Price:       int64(pdt.Price),
		PriceNoTax:  int64(pdt.PriceNoTax),
		TaxRate:     pdt.TaxRate,
		Fee:         pdt.Fee,
		OrderDate:   now,
		StartDate:   nextStartDate,
		EndDate:     nextEndDate,
		Info:        null.StringFrom(string(infoJson)),
	}

	// insert orders into db
	tx, err := srv.dbConn.Begin()
	if err != nil {
		return nil, err
	}
	var txErr error
	defer func() {
		if txErr != nil {
			_ = tx.Rollback()
		}
	}()

	orderRepo := srv.orderService.WithTx(tx)
	userRepo := srv.userService.WithTx(tx)
	paymentRepo := srv.paymentInfoService.WithTx(tx)

	orders := []*dbuser.Order{fulfilledOrder, unfulfilledOrder}
	for _, o := range orders {
		if txErr = orderRepo.Insert(o); txErr != nil {
			return nil, txErr
		}
	}
	detail := &subscribeDetail{
		order: fulfilledOrder,
	}

	var originUser dbuser.User
	_ = copier.Copy(&originUser, user)

	// update user to be premium and expire date
	expireDate := calExpiredDate(orders)
	var affected bool
	if affected, txErr = userRepo.UpdateByFields(user.ID, map[dbuser.UsersField]interface{}{
		dbuser.UserFieldRole:       dbuser.RolePremium.String(),
		dbuser.UserFieldType:       dbuser.TypeGeneral.String(),
		dbuser.UserFieldAutoRenew:  pdt.AutoRenew,
		dbuser.UserFieldExpiredAt:  null.TimeFrom(expireDate),
		dbuser.UserFieldMembership: dbuser.MembershipPremiumOnly,
	}); txErr != nil {
		return nil, txErr
	} else if affected {
		user.Role = dbuser.RolePremium.String()
		user.Type = dbuser.TypeGeneral.String()
		user.Membership = dbuser.MembershipPremiumOnly
		user.AutoRenew = pdt.AutoRenew
		user.ExpiredAt = null.TimeFrom(expireDate)
	}

	// upsert payment info
	var paymentInfo *dbuser.PaymentInfo
	var originPaymentInfo *dbuser.PaymentInfo // only for audit log
	paymentInfo, txErr = paymentRepo.GetByUserID(user.ID)
	if txErr != nil {
		return nil, fmt.Errorf("payment_repo.GetByUserID fail: %w", txErr)
	} else if paymentInfo != nil { // such payment of user exists already
		originPaymentInfo = &dbuser.PaymentInfo{}
		_ = copier.Copy(originPaymentInfo, paymentInfo)

		detail.prevPaymentType = paymentInfo.PaymentType.ValueOrZero()
	} else {
		// a whole new payment info for user
		paymentInfo = &dbuser.PaymentInfo{
			UserID: user.ID,
		}
	}

	paymentInfo.PaymentType = null.StringFrom(pdt.PaymentType.String())
	if subscribeInfo.SubscriberId != "" {
		paymentInfo.ModSubscriberID = null.StringFrom(subscribeInfo.SubscriberId)
	}
	if subscribeInfo.SubscriberArea != "" {
		paymentInfo.ModSubscriberArea = null.StringFrom(subscribeInfo.SubscriberArea)
	}
	if txErr = paymentRepo.Upsert(paymentInfo); txErr != nil {
		return nil, fmt.Errorf("payment_repo.Upsert fail: %w", txErr)
	}
	detail.payment = paymentInfo

	if txErr = tx.Commit(); txErr != nil {
		return nil, txErr
	}
	m.writeAuditLogs(user, originUser, paymentInfo, originPaymentInfo, fulfilledOrder, unfulfilledOrder)

	return detail, nil
}

func calExpiredDate(orders []*dbuser.Order) time.Time {
	// the last order with Price = 0
	var lastFreeOrder *dbuser.Order
	for _, o := range orders {
		if o.Price == 0 {
			lastFreeOrder = o
		}
	}
	// if no free order, use the fulfilled order's end date
	if lastFreeOrder == nil {
		lastFreeOrder = orders[0]
	}
	return lastFreeOrder.EndDate
}

func (m *modSubscriber) sendTrackingEvent(sd *subscribeDetail, pdt *dbuser.Product, user *dbuser.User) error {
	evt := &amplitudelib.MODAccountTransactionCompletedEvent{
		AccountTransactionCompletedEvent: amplitudelib.AccountTransactionCompletedEvent{
			UserPlan:            sd.prevPackageName,
			PaymentType:         sd.prevPaymentType,
			Plan:                sd.prevPackageName,
			TriggerCondition:    "upgrade",
			Price:               int(pdt.Price),
			Quantity:            1,
			Revenue:             int(pdt.Price),
			OrderNumber:         sd.order.ID,
			InSubscription:      null.BoolFrom(sd.inSubscription),
			ProductDurationDays: int(pdt.GetDuration().Hours() / 24),
		},
		ModSubscriberID: m.subscriberId,
	}
	evt.UserID = user.ID

	return m.srv.amplitudeClient.SendEvent(evt)
}

func (m *modSubscriber) writeAuditLogs(currentUser *dbuser.User, originUser dbuser.User,
	currentPaymentInfo *dbuser.PaymentInfo, originPaymentInfo *dbuser.PaymentInfo,
	transacOrder *dbuser.Order, unrealizeOrder *dbuser.Order) {
	logDiffs, err := auditing.GetDiffFields(&originUser, currentUser)
	if err != nil {
		log.Warn("order service: subscribe: get diff fields fail").
			Interface("origin", originUser).Interface("new", currentUser).
			Err(err).Send()
	}

	paymentLogBuilder := auditing.NewLogBuilder().ByUser().ModifierID(currentUser.ID).
		Target("payment_info", currentPaymentInfo.UserID).
		Note("user subscribes premium via MOD")
	if originPaymentInfo != nil {
		fields, _ := auditing.GetDiffFields(originPaymentInfo, currentPaymentInfo)
		paymentLogBuilder.ActUpdate().DetailDiff(fields...)
	} else {
		paymentLogBuilder.ActCreate().DetailWhole(currentPaymentInfo)
	}

	auditBuilder := auditing.NewLogBuilder().
		ByUser().ModifierID(currentUser.ID).
		TargetUpdated("user", currentUser.ID).
		DetailDiff(logDiffs...).
		Note("user subscribes premium via MOD").
		// log orders
		Associating(
			auditing.NewLogBuilder().ByUser().ModifierID(currentUser.ID).
				TargetCreated("order", transacOrder.ID).DetailWhole(transacOrder).
				Note("MOD subscribe: transaction order created"),
			auditing.NewLogBuilder().ByUser().ModifierID(currentUser.ID).
				TargetCreated("order", unrealizeOrder.ID).DetailWhole(unrealizeOrder).
				Note("MOD order subscribe: unrealized order created")).
		// log payment info
		Associating(paymentLogBuilder)

	auditLogs := auditBuilder.Build()
	if err := m.srv.auditLogRepo.Insert(auditLogs...); err != nil {
		log.Warn("order service: subscribe: insert audit log fail").Interface("logs", auditLogs).Err(err).Send()
	}
}

func (m *modSubscriber) getOrderPeriod(subscribeInfo *modSubscribeInfo, pdt *dbuser.Product) (time.Time, time.Time) {
	var (
		initStartDate time.Time
		startDate     time.Time
	)

	loc := datetimer.LocationTaipei
	now := m.srv.clock.Now().In(loc)
	duration := pdt.GetDuration()

	anchor := now
	if subscribeInfo.StartTime > 0 {
		anchor = time.Unix(subscribeInfo.StartTime, 0).In(loc)
	}
	// 00:00:00 next day
	initStartDate = datetime.BeginOfDay(anchor).AddDate(0, 0, 1)

	if initStartDate.Before(now) {
		ts := initStartDate
		te := initStartDate.Add(duration)
		for {
			if (ts.Before(now) || ts.Equal(now)) && (te.After(now) || te.Equal(now)) {
				startDate = ts
				break
			}
			ts = te
			te = te.Add(duration)
		}
	} else {
		startDate = initStartDate
	}
	return startDate, startDate.Add(duration)
}
