//go:generate mockgen -source service.go -destination service_mock.go -package paymentinfo
package paymentinfo

import (
	"fmt"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/user"
	"github.com/KKTV/kktv-api-v3/pkg/clock"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/jinzhu/copier"
	"gopkg.in/guregu/null.v3"
)

type Service interface {
	ClearMODSubscriberData(userID string) (oldPaymentInfo, newPaymentInfo *dbuser.PaymentInfo, err error)
}

type service struct {
	clock           clock.Clock
	paymentInfoRepo user.PaymentInfoRepository
}

func NewService() Service {
	return &service{
		clock:           clock.New(),
		paymentInfoRepo: user.NewPaymentInfoRepository(),
	}
}

func (s *service) ClearMODSubscriberData(userID string) (oldPaymentInfo, newPaymentInfo *dbuser.PaymentInfo, err error) {
	if oldPaymentInfo, err = s.paymentInfoRepo.GetByUserID(userID); err != nil {
		return nil, nil, fmt.Errorf("get payment info failed: %w", err)
	} else if oldPaymentInfo == nil {
		return nil, nil, nil
	}

	newPaymentInfo = new(dbuser.PaymentInfo)
	_ = copier.Copy(newPaymentInfo, oldPaymentInfo)
	newPaymentInfo.UserID = userID
	newPaymentInfo.ModSubscriberID = null.StringFromPtr(nil)
	newPaymentInfo.ModSubscriberArea = null.StringFromPtr(nil)
	newPaymentInfo.UpdatedAt = null.TimeFrom(s.clock.Now().UTC())
	if err = s.paymentInfoRepo.Upsert(newPaymentInfo); err != nil {
		return nil, nil, fmt.Errorf("update payment info failed: %w", err)
	}

	return oldPaymentInfo, newPaymentInfo, nil
}
