package rest

import "net/http"

type Status struct {
	Type    string      `json:"type"`
	Subtype interface{} `json:"subtype"`
	Message string      `json:"message"`
	Info    interface{} `json:"info,omitempty"`
}

type Resp struct {
	Status Status      `json:"status"`
	Data   interface{} `json:"data"`
}

func Ok() (int, Resp) {
	statusCode := http.StatusOK
	statusText := http.StatusText(statusCode)
	return statusCode, Resp{
		Status: Status{
			Type:    statusText,
			Subtype: statusText,
			Message: statusText,
		},
	}
}

func BadRequest() (int, Resp) {
	statusCode := http.StatusBadRequest
	statusText := http.StatusText(statusCode)
	return statusCode, Resp{
		Status: Status{
			Type:    statusText,
			Subtype: statusText,
			Message: statusText,
		},
	}
}

func Unknown() (int, Resp) {
	statusCode := http.StatusInternalServerError
	statusText := http.StatusText(statusCode)
	return statusCode, Resp{
		Status: Status{
			Type:    statusText,
			Subtype: statusText,
			Message: statusText,
		},
	}
}

func Unauthorized() (int, Resp) {
	statusCode := http.StatusUnauthorized
	statusText := http.StatusText(statusCode)
	return statusCode, Resp{
		Status: Status{
			Type:    statusText,
			Subtype: statusText,
			Message: statusText,
		},
	}
}

func Forbidden() (int, Resp) {
	statusCode := http.StatusForbidden
	statusText := http.StatusText(statusCode)
	return statusCode, Resp{
		Status: Status{
			Type:    statusText,
			Subtype: statusText,
			Message: statusText,
		},
	}
}
