package ads

import (
	"net/http"
	"time"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/meta"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/middleware"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/v3/internal/rest"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/kktverror"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/permission"
	"github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/model/cachemeta"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	modelmw "github.com/KKTV/kktv-api-v3/pkg/model/middleware"
	"github.com/KKTV/kktv-api-v3/pkg/render"
)

type Handler struct {
	adsRepo           meta.AdsRepository
	permissionService permission.Service
}

func NewHandler() *Handler {
	return &Handler{
		adsRepo:           meta.NewAdsRepository(),
		permissionService: container.PermissionService(),
	}
}

func (h *Handler) List(w http.ResponseWriter, r *http.Request) {
	membership := dbuser.NonMember
	resp := rest.Ok()
	accessUser, ok := r.Context().Value(middleware.KeyAccessUser).(modelmw.AccessUser)
	if ok {
		membership = accessUser.Memberships
	}

	ads, err := h.adsRepo.List()
	if err != nil {
		render.JSON(w, http.StatusBadRequest, rest.Resp{Status: rest.Status{
			Type: "BadRequest",
		}})
		return
	}
	var items []*cachemeta.AdItem
	if len(ads.Ads) > 0 {
		now := time.Now().Unix()
		for _, adItem := range ads.Ads {
			if now > adItem.StartTime && now < adItem.EndTime {
				err := h.permissionService.Grant(permission.RequestAds(adItem, membership))
				if kktverror.IsInternalErr(err) {
					log.Error("v4AdsHandler: List: permissionService fail to grant").Err(err).
						Str("user_id", accessUser.UserID).Send()
					render.JSON(w, http.StatusInternalServerError, rest.Resp{Status: rest.Status{
						Type: "InternalServerError",
					}})
					return
				}
				if err == nil {
					item := new(cachemeta.AdItem)
					item.ID = adItem.ID
					item.Name = adItem.Name
					item.Source = adItem.Source
					item.Provider = adItem.Provider
					item.Target = adItem.Target
					item.ImageURL = adItem.ImageURL
					if adItem.ActionLink != "" {
						item.ActionLink = adItem.ActionLink
						item.ActionLinkBg = adItem.ActionLinkBg
						item.ActionLinkText = adItem.ActionLinkText
					}
					items = append(items, item)
				}
			}
		}
	}
	resp.Data = map[string][]*cachemeta.AdItem{
		"ads": items,
	}
	render.JSONOk(w, resp)
}
