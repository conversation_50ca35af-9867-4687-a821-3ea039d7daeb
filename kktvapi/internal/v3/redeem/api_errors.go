package redeem

import "github.com/KKTV/kktv-api-v3/kkapp/model"

var (
	RedeemCodeApiErrors map[string]model.KKStatus = map[string]model.KKStatus{
		"prime user cannot use redeem code": {
			Type:    "Forbidden",
			Subtype: "DeniedPrimeUser",
			Message: "prime user cannot use redeem code",
		},
		"iap user cannot use redeem code": {
			Type:    "Forbidden",
			Subtype: "DeniedIAPUser",
			Message: "iap user cannot use redeem code",
		},
		"iab user cannot use redeem code": {
			Type:    "Forbidden",
			Subtype: "DeniedIABUser",
			Message: "iab user cannot use redeem code",
		},
		"telecom user cannot use redeem code": {
			Type:    "Forbidden",
			Subtype: "DeniedTelecomUser",
			Message: "telecom user cannot use redeem code",
		},
		"mod cannot use redeem code": {
			Type:    "Forbidden",
			Subtype: "DeniedMODUser",
			Message: "mod cannot use redeem code",
		},
		"bandott user cannot use redeem code": {
			Type:    "Forbidden",
			Subtype: "DeniedBandottUser",
			Message: "bandott user cannot use redeem code",
		},
		"credit_card user cannot use redeem code": {
			Type:    "Forbidden",
			Subtype: "DeniedCreditcardUser",
			Message: "credit_card user cannot use redeem code",
		},
		"tstar user cannot use redeem code": {
			Type:    "Forbidden",
			Subtype: "DeniedTstarUser",
			Message: "tstar user cannot use redeem code",
		},
		"vip user cannot use redeem code": {
			Type:    "Forbidden",
			Subtype: "DeniedVIPUser",
			Message: "vip user cannot use redeem code",
		},
		"Redeem code is not yet valid.": {
			Type:    "Forbidden",
			Subtype: "RedeemCodeIsNotYetValid",
			Message: "Redeem code is not yet valid.",
		},
		"Already used limited number of codes in this group.": {
			Type:    "Forbidden",
			Subtype: "UsageLimitReached",
			Message: "Already used limited number of codes in this group.",
		},
		"Invalid parameters": {
			Type:    "NotFound",
			Subtype: "RedeemCodeNotFound",
			Message: "Invalid parameters",
		},
		"The input redeem code does not exist.": {
			Type:    "NotFound",
			Subtype: "RedeemCodeNotFound",
			Message: "The input redeem code does not exist.",
		},
		"Redeem code has been used or not available.": {
			Type:    "Gone",
			Subtype: "RedeemCodeIsNotAvailable",
			Message: "Redeem code has been used or not available.",
		},
		"Redeem code has expired.": {
			Type:    "Gone",
			Subtype: "RedeemCodeHasExpired",
			Message: "Redeem code has expired.",
		},
	}
)

func CustomStatus(errorType, errorSubtype, errorMessage string) model.KKStatus {
	return model.KKStatus{
		Type:    errorType,
		Subtype: errorSubtype,
		Message: errorMessage,
	}
}
