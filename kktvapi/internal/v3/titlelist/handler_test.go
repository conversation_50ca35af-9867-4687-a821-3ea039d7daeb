package titlelist

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/meta"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/middleware"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/v4/presenter"
	metamodel "github.com/KKTV/kktv-api-v3/pkg/model/dbmeta"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbmeta/legacy"
	"github.com/go-zoo/bone"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

type HandlerTestSuite struct {
	suite.Suite
	ctrl    *gomock.Controller
	handler Handler
	app     *bone.Mux

	assert        *require.Assertions
	mockTitleRepo *meta.MockTitleRepository

	mockSrv *MockService
}

func TestHandlerTestSuite(t *testing.T) {
	suite.Run(t, new(HandlerTestSuite))
}

func (suite *HandlerTestSuite) SetupTest() {
	suite.assert = suite.Require()
	suite.ctrl = gomock.NewController(suite.T())
	suite.mockTitleRepo = meta.NewMockTitleRepository(suite.ctrl)
	suite.mockSrv = NewMockService(suite.ctrl)
	suite.handler = Handler{
		service:   suite.mockSrv,
		titleRepo: suite.mockTitleRepo,
	}
	suite.app = bone.New()
	suite.app.GetFunc("/v3/title_list/youwilllove", suite.handler.GetColdStartRecommends)
	suite.app.GetFunc("/v3/title_list/:shareID", suite.handler.GetByShareID)
}

func (suite *HandlerTestSuite) TearDownTest() {
	defer suite.ctrl.Finish()
}

func (suite *HandlerTestSuite) TestGetColdStartRecommends() {
	var (
		userID = "user-id1"
		//deviceID = "device-id1"
		dbTitleList = suite.aTitleList()
	)

	testcases := []struct {
		name           string
		prepareRequest func(r *http.Request) *http.Request
		given          func()
		thenAssert     func(code int, body []byte)
	}{
		{
			name: "return titlelist info and 200 ok",
			prepareRequest: func(req *http.Request) *http.Request {
				return req.WithContext(context.WithValue(req.Context(), middleware.KeyIdentity, middleware.Identity{
					UserID:   userID,
					DeviceID: "",
				}))
			},
			given: func() {
				titleIDs := []string{"04290106"}

				tl := *dbTitleList
				tl.Meta.TitleID = titleIDs
				suite.mockSrv.EXPECT().GetColdStartRecommendationPage(userID, "", true).Return(&PageInfo{
					ID:        "youwilllove",
					Name:      "您的專屬推薦片單",
					TitleIDs:  titleIDs,
					ShareLink: "https://www.kktv.me/titleList/josiecute?utm_source=app",
				}, nil)
				tds := suite.genDummyTitleDetails(titleIDs)
				suite.mockTitleRepo.EXPECT().ListViewableTitleDetailWithoutSeries(titleIDs, true).Return(tds, nil)
			},
			thenAssert: func(code int, bodyBytes []byte) {
				println(string(bodyBytes))
				suite.assert.Equal(http.StatusOK, code)

				var body map[string]interface{}
				suite.assert.NoError(json.Unmarshal(bodyBytes, &body))

				data := body["data"].(map[string]interface{})
				suite.assert.Len(data["items"], 1)

				suite.assert.EqualValues("您的專屬推薦片單", data["name"])
				suite.assert.EqualValues("youwilllove", data["id"])
			},
		},
	}
	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			tc.given()

			req := httptest.NewRequest(http.MethodGet, "/v3/title_list/youwilllove?refresh=true", nil)
			req = tc.prepareRequest(req)
			rr := httptest.NewRecorder()
			suite.app.ServeHTTP(rr, req)

			tc.thenAssert(rr.Code, rr.Body.Bytes())
		})
	}
}

func (suite *HandlerTestSuite) TestGetByShareID() {

	testcases := []struct {
		name       string
		given      func()
		thenAssert func(code int, body []byte)
	}{
		{
			name: "return redirect url and 404 NotFound if fallback is not nil",
			given: func() {
				suite.mockSrv.EXPECT().GetPageByShareID("nothere").Return(nil,
					&presenter.Fallback{
						Message:     "噢噢～你走吧",
						RedirectURL: "https://www.kktv.me",
					})
			},
			thenAssert: func(code int, bodyBytes []byte) {
				println(string(bodyBytes))
				suite.assert.Equal(http.StatusNotFound, code)

				var body map[string]interface{}
				suite.assert.NoError(json.Unmarshal(bodyBytes, &body))

				data := body["data"].(map[string]interface{})
				suite.assert.Nil(data["items"])

				suite.assert.EqualValues("噢噢～你走吧", data["fallback_msg"])
				suite.assert.EqualValues("https://www.kktv.me", data["redirect_url"])
			},
		},
	}
	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			tc.given()

			req := httptest.NewRequest(http.MethodGet, "/v3/title_list/nothere", nil)
			rr := httptest.NewRecorder()
			suite.app.ServeHTTP(rr, req)

			tc.thenAssert(rr.Code, rr.Body.Bytes())
		})
	}
}

func (suite *HandlerTestSuite) aTitleList() *dbmeta.TitleList {
	return &dbmeta.TitleList{
		Caption: "Josie cutie",
		ID:      429,
		Meta: &dbmeta.TitleListMeta{
			ShareId:            "josiecute",
			Description:        "josie is cute",
			BackgroundImageUrl: "http://fakeimg.com/josie.jpg",
			Copyright:          "kktv",
			OGImage:            "http://fakeimg.com/josie-og.jpg",
		},
	}
}

func (suite *HandlerTestSuite) genDummyTitleDetails(ids []string) interface{} {
	list := make([]*legacymeta.TitleDetail, len(ids))
	for i := 0; i < len(ids); i++ {
		list[i] = &legacymeta.TitleDetail{
			LegacyTitleDetail: &model.TitleDetail{
				ID:                          ids[i],
				Title:                       fmt.Sprintf("%d", i),
				TitleType:                   metamodel.TitleTypeSeries.String(),
				ContentLabelsForExpiredUser: []string{"coming_soon"},
			},
		}
	}
	return list
}
