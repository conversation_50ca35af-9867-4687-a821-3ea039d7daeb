package main

import (
	"bufio"
	"fmt"
	"os"
	"sync"
	"time"

	"github.com/KKTV/kktv-api-v3/kktvapi/config"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	plog "github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/jmoiron/sqlx"
)

const batchSize = 10000 // 每次從資料庫中獲取的使用者數量
const numWorkers = 5    // 並發處理的 Worker 數量

func init() {
	if err := config.Init(); err != nil {
		plog.Fatal("Failed to initialize config").Err(err).Send()
	}

	container.RegisterUserDB(config.DbUser)
	container.RegisterUserCache(config.RedisUser)
	plog.Init(config.Env)
}

func main() {
	startTime := time.Now()
	plog.Info("Execution started").Send()

	currentTime := time.Now().Format("20060102150405")
	outputFileName := fmt.Sprintf("%s_before_migrate.txt", currentTime)

	dbreader := container.DBPoolUser().Slave()
	cachePoolUser := container.CachePoolUser()
	metaCacheReader := cache.New(cachePoolUser.Slave())

	outputFile, err := os.OpenFile(outputFileName, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		plog.Fatal(fmt.Sprintf("Failed to open file: %s", outputFileName)).Err(err).Send()
		return
	}
	defer outputFile.Close()

	outputWriter := bufio.NewWriter(outputFile)

	// 第一步：批量查詢資料庫中的使用者ID，並逐批處理
	totalNeedMigrateUsers, totalNeedMigrateTitles, err := processBatches(dbreader, metaCacheReader)
	if err != nil {
		plog.Warn("Failed to process batches").Err(err).Send()
		return
	}

	// 第二步：記錄最終結果到 before_migrate.txt
	outputWriter.WriteString(fmt.Sprintf("Total need migrate users count: %d\n", totalNeedMigrateUsers))
	outputWriter.WriteString(fmt.Sprintf("Total need migrate titles count: %d\n", totalNeedMigrateTitles))

	if err := outputWriter.Flush(); err != nil {
		plog.Warn("Failed to flush before_migrate.txt writer").Err(err).Send()
	}

	plog.Info(fmt.Sprintf("Total need migrate users count: %d", totalNeedMigrateUsers)).Send()
	plog.Info(fmt.Sprintf("Total need migrate titles count: %d", totalNeedMigrateTitles)).Send()

	executionTime := time.Since(startTime)
	plog.Info(fmt.Sprintf("Execution completed in %s", executionTime)).Send()
}

// processBatches 批量處理資料庫中的使用者ID，並統計需要遷移的使用者和標題數量
func processBatches(dbreader *sqlx.DB, cacheReader cache.Cacher) (int, int, error) {
	var totalNeedMigrateUsers int
	var totalNeedMigrateTitles int
	var offset int
	var batchCounter int

	for {
		// 記錄資料庫查詢的開始時間
		dbQueryStartTime := time.Now()

		// 每次獲取一批使用者ID
		userIDs, err := getValidUserIDs(dbreader, offset, batchSize)
		if err != nil {
			return 0, 0, err
		}

		// 記錄資料庫查詢的結束時間
		dbQueryDuration := time.Since(dbQueryStartTime)

		if len(userIDs) == 0 {
			break // 如果沒有更多的使用者，退出循環
		}

		// 更新 offset
		offset += len(userIDs)
		batchCounter++

		// 打印當前 batch 的日誌信息，包括資料庫查詢時間
		plog.Info(fmt.Sprintf("Processing batch %d: fetched %d userIDs, DB query duration: %s", batchCounter, len(userIDs), dbQueryDuration)).Send()

		// 記錄 Redis 處理的開始時間
		redisProcessingStartTime := time.Now()

		// 並發處理 Redis 中的批次資料
		batchNeedMigrateUsers, batchNeedMigrateTitles := processUsersInRedisConcurrently(cacheReader, userIDs)

		// 記錄 Redis 處理的結束時間
		redisProcessingDuration := time.Since(redisProcessingStartTime)

		// 打印當前 batch 的 Redis 處理時間日誌信息
		plog.Info(fmt.Sprintf("Processing batch %d: Redis processing duration: %s", batchCounter, redisProcessingDuration)).Send()

		totalNeedMigrateUsers += batchNeedMigrateUsers
		totalNeedMigrateTitles += batchNeedMigrateTitles
	}

	return totalNeedMigrateUsers, totalNeedMigrateTitles, nil
}

// 並發處理 Redis 查詢
func processUsersInRedisConcurrently(cacheReader cache.Cacher, userIDs []string) (int, int) {
	var totalNeedMigrateUsers int
	var totalNeedMigrateTitles int
	var mu sync.Mutex // 用於保護計數器的並發訪問
	var wg sync.WaitGroup

	// 創建一個使用者 ID 的 channel，用於將資料分發給 Worker
	userIDChan := make(chan string, len(userIDs))

	// 啟動 numWorkers 個 Goroutine 並發處理 Redis 查詢
	for i := 0; i < numWorkers; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()

			for userID := range userIDChan {
				// 構造 Redis key
				key := fmt.Sprintf("favorite-title:v1:%s:json", userID)
				exists, err := cacheReader.Exists(key)
				if err != nil {
					plog.Warn(fmt.Sprintf("Failed to check Redis key for userID: %s", userID)).Err(err).Send()
					continue
				}

				if exists {
					// 如果 Redis 中有這個使用者的鍵，統計為需要遷移的使用者
					mu.Lock()
					totalNeedMigrateUsers++
					mu.Unlock()

					// 獲取 title_id 的數量（需要遷移的標題數量）
					redisTitleScores, err := getRedisTitleScores(cacheReader, key)
					if err != nil {
						plog.Warn(fmt.Sprintf("Failed to get Redis title count for userID: %s", userID)).Err(err).Send()
						continue
					}

					// 累加標題數量
					mu.Lock()
					totalNeedMigrateTitles += len(redisTitleScores)
					mu.Unlock()
				}
			}
		}()
	}

	// 將使用者ID發送到 channel 中
	for _, userID := range userIDs {
		userIDChan <- userID
	}
	close(userIDChan) // 關閉 channel，通知 Worker 沒有更多的資料

	// 等待所有 Worker 完成處理
	wg.Wait()

	return totalNeedMigrateUsers, totalNeedMigrateTitles
}

// 從資料庫中批量查詢 revoked_at 為 null 的使用者ID
func getValidUserIDs(dbreader *sqlx.DB, offset, limit int) ([]string, error) {
	var userIDs []string
	query := `
        SELECT id
        FROM users
        WHERE revoked_at IS NULL
        ORDER BY id
        LIMIT $1 OFFSET $2
    `
	rows, err := dbreader.Queryx(query, limit, offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	for rows.Next() {
		var userID string
		if err := rows.Scan(&userID); err != nil {
			return nil, err
		}
		userIDs = append(userIDs, userID)
	}

	return userIDs, nil
}

// getRedisTitleScores 從 Redis 中獲取 title_id 和對應的 score
func getRedisTitleScores(cacheReader cache.Cacher, key string) ([][2]string, error) {
	data, err := cacheReader.ZRevRangeWithScores(key, 0, -1)
	if err != nil {
		if err == cache.ErrCacheMiss {
			return nil, nil
		}
		return nil, err
	}
	var titleScores [][2]string
	for _, item := range data {
		titleScores = append(titleScores, [2]string{item[0], item[1]}) // item[0] 是 title_id, item[1] 是 score
	}
	return titleScores, nil
}
