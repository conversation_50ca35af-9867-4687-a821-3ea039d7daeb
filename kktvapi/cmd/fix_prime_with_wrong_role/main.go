// 有部分的 users 沒有被正確的更新 role & type
// 所以寫了這個 script 更新這些 user

// [Usage]
// 1. Execute the command as shown below
// `$ GOOS=linux GOARCH=386 go build -o fix_prime_with_wrong_role kktvapi/cmd/fix_prime_with_wrong_role/main.go `
// 2. Copy file by SSH command
// `$ scp fix_prime_with_wrong_role {jumper-machine}:{your-jumper-machine-folder}`
// Finally, execute this file by `./fix_prime_with_wrong_role`
package main

import (
	"time"

	"github.com/KKTV/kktv-api-v3/kktvapi/config"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/user"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/auditing"
	"github.com/KKTV/kktv-api-v3/pkg/kkboxmember"
	"github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/jinzhu/copier"
	"gopkg.in/guregu/null.v3"
)

func init() {
	container.RegisterUserDB(config.DbUser)
	log.Init(config.Env)
}

func main() {
	var (
		users []*dbuser.User
		err   error
	)

	// 預計 6,000 多筆資料須被更新
	// is_prime 的 value 已經在上週全部 update 過，可參考
	// https://github.com/KKTV/kktv-api-v3/pull/2126
	sqlUsers := `select u.*
	from kktv_members_from_kkid p
		left join users u
		on p.msno_sub = u.media_source ->'kkbox'->>'sub'
	where p.is_prime = true and u.role != 'premium';`

	dbreader := container.DBPoolUser().Slave().Unsafe()
	dbwriter := container.DBPoolUser().Master()
	userRepo := user.NewUserRepository()
	logRepo := auditing.NewRepository(dbwriter)

	err = dbreader.Select(&users, sqlUsers)
	if err != nil {
		log.Error("fixPrimeWithWrongRole: get all users failed").Err(err).Send()
		return
	}

	client := kkboxmember.NewClient()
	if err = client.AuthToken(); err != nil {
		log.Warn("fixPrimeWithWrongRole: get member API auth token failed").Err(err).Send()
		return
	}

	for _, user := range users {
		var originUser dbuser.User
		_ = copier.Copy(&originUser, user)

		fields := map[dbuser.UsersField]interface{}{
			dbuser.UserFieldType: dbuser.TypePrime,
			dbuser.UserFieldRole: dbuser.RolePremium,
		}

		if user.ExpiredAt.Time.Compare(time.Now()) == -1 {
			kkboxMediaSource, ok := user.MediaSource["kkbox"].(map[string]interface{})
			if !ok {
				log.Warn("fixPrimeWithWrongRole: assertion failed").Err(err).
					Interface("media_source", kkboxMediaSource).
					Str("user_id", user.ID).
					Send()
				continue
			}

			sub, ok := kkboxMediaSource["sub"].(string)
			if !ok {
				log.Warn("fixPrimeWithWrongRole: assert sub failed").Err(err).
					Interface("media_source", kkboxMediaSource).
					Str("user_id", user.ID).
					Send()
				continue
			}

			if resp, err := client.GetMembership(sub); err != nil {
				log.Warn("fixPrimeWithWrongRole: get membership failed").Err(err).
					Str("user_id", user.ID).
					Str("msno", sub).
					Send()
				continue
			} else {
				user.ExpiredAt = null.NewTime(time.Unix(int64(resp.DueDate), 0), true)
				fields[dbuser.UserFieldExpiredAt] = user.ExpiredAt
			}
		}

		if affected, err := userRepo.UpdateByFields(user.ID, fields); err != nil {
			log.Warn("fixPrimeWithWrongRole: update by fields").
				Err(err).
				Str("user_id", user.ID).
				Interface("fields", fields).
				Send()
		} else if affected {
			user.Role = dbuser.RolePremium.String()
			user.Type = dbuser.TypePrime.String()

			logDiffs, err := auditing.GetDiffFields(&originUser, user)
			if err != nil {
				log.Warn("fixPrimeWithWrongRole: get diff fields failed").
					Interface("origin", originUser).Interface("new", user).
					Err(err).Send()
			}

			auditBuilder := auditing.NewLogBuilder().ByUser().ModifierID(user.ID).
				TargetUpdated("user", user.ID).
				DetailDiff(logDiffs...).
				Note("[KKTV-12764] update some prime member with wrong type and role")

			auditLogs := auditBuilder.Build()
			if err := logRepo.Insert(auditLogs...); err != nil {
				log.Warn("fixPrimeWithWrongRole: insert audit log failed").Interface("logs", auditLogs).Err(err).Send()
			}
		}
	}
}
