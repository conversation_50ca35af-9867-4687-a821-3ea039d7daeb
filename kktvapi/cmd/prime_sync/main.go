// [Usage]
// 1. Execute the command as shown below
// `$ GOOS=linux GOARCH=386 go build -o prime_member_sync kktvapi/cmd/prime_sync/main.go `
// 2. Copy file by SSH command
// `$ scp prime_member_sync {jumper-machine}:{your-jumper-machine-folder}`
// Finally, execute this file by `./prime_member_sync`
package main

import (
	"time"

	"github.com/KKTV/kktv-api-v3/kktvapi/config"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/user"
	"github.com/KKTV/kktv-api-v3/pkg/kkboxmember"
	plog "github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
)

func init() {
	container.RegisterUserDB(config.DbUser)
	plog.Init(config.Env)
}

func main() {
	var (
		users []*dbuser.User
		err   error
	)
	dbreader := container.DBPoolUser().Slave()
	userRepo := user.NewUserRepository()
	// step1. obtain all prime user from users / kkid_member backup tables
	plog.Info("All prime sync: Start").Send()
	err = dbreader.Select(&users, `
		select u.*
		from users u
		right join kkbox_billing_prime_member kbpm
			on u.media_source ->'kkbox'->>'sub' = kbpm.msno_sub;
	`)
	if err != nil {
		plog.Warn("All prime sync failed: get all users failed").Err(err).Send()
		return
	}
	// step2. ask member api token
	client := kkboxmember.NewClient()
	if err = client.AuthToken(); err != nil {
		plog.Warn("All prime sync failed: get member API auth token failed").Err(err).Send()
		return
	}
	// step3. invoke member api by msno
	for _, user := range users {
		if kkboxMediaSource, ok := user.MediaSource["kkbox"].(map[string]string); !ok {
			plog.Warn("All prime sync failed: type assertion failed").Send()
			continue
		} else {
			if resp, err := client.GetMembership(kkboxMediaSource["sub"]); err != nil {
				plog.Warn("All prime sync failed: get membership failed").Err(err).
					Str("user_id", user.ID).
					Str("msno", kkboxMediaSource["sub"]).
					Send()
				continue
			} else {
				if resp.State == kkboxmember.Paid && user.ExpiredAt.Time.Unix() < int64(resp.DueDate) {
					// step4. update user
					fields := map[dbuser.UsersField]interface{}{
						dbuser.UserFieldExpiredAt: time.Unix(int64(resp.DueDate), 0),
						dbuser.UserFieldType:      dbuser.TypePrime,
						dbuser.UserFieldRole:      dbuser.RolePremium,
					}
					if _, err := userRepo.UpdateByFields(user.ID, fields); err != nil {
						plog.Warn("All prime sync failed: update user failed").Str("user_id", user.ID).Err(err).Send()
					}
				}
			}
		}

	}

	plog.Info("All prime sync: Finish").Send()
}
