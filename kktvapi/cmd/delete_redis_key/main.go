package main

import (
	"fmt"
	"sync"
	"time"

	"github.com/KKTV/kktv-api-v3/kktvapi/config"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	plog "github.com/KKTV/kktv-api-v3/pkg/log"
)

const (
	workerCount = 5
	scanCount   = 1000
	// 定義 key 模板
	keyPattern = "favorite-title:v1:*:json"
)

func init() {
	// 初始化配置，從 .env 檔案和環境變數中加載
	if err := config.Init(); err != nil {
		plog.Fatal("Failed to initialize config").Err(err).Send()
	}

	// 註冊 Redis 快取池
	container.RegisterUserCache(config.RedisUser)
	plog.Init(config.Env)
}

func main() {
	startTime := time.Now()
	plog.Info("Execution started").Send()

	// 獲取 Redis 連接池
	cachePoolUser := container.CachePoolUser()
	userCache := cache.New(cachePoolUser.Master()) // 使用 Master 進行刪除操作

	var wg sync.WaitGroup
	keysChan := make(chan []string, workerCount)

	// 啟動 workerCount 個 Goroutine 處理刪除操作
	for i := 0; i < workerCount; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for keys := range keysChan {
				if len(keys) > 0 {
					err := userCache.Del(keys...)
					if err != nil {
						plog.Warn(fmt.Sprintf("Failed to delete keys: %v", keys)).Err(err).Send()
					} else {
						plog.Info(fmt.Sprintf("Successfully deleted keys: %v", keys)).Send()
					}
				}
			}
		}()
	}

	// 使用 Scan 來遍歷符合條件的所有 key
	cursor := 0
	for {
		keys, newCursor, err := userCache.Scan(cursor, keyPattern, scanCount)
		if err != nil {
			plog.Fatal("Failed to scan keys").Err(err).Send()
			break
		}

		if len(keys) > 0 {
			keysChan <- keys
		}

		cursor = newCursor
		if cursor == 0 { // 如果游標為0，說明已經遍歷完成
			break
		}
	}

	close(keysChan)
	wg.Wait()

	executionTime := time.Since(startTime)
	plog.Info(fmt.Sprintf("Execution completed in %s", executionTime)).Send()
}
