//go:generate mockgen -source user_service.go -destination user_service_mock.go -package wrapper
package wrapper

import (
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/user"
	"github.com/KKTV/kktv-api-v3/pkg/database"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
)

type UserService interface {
	GetActiveByID(userID string) (*dbuser.User, error)
	GetByID(uid string) (*dbuser.User, error)
	GetByPhone(phone string) (*dbuser.User, error)
	Create(user *dbuser.User) (*dbuser.User, error)
	Update(u *dbuser.User) (bool, error)
	WithTx(tx database.Tx) UserService
	UpdateByFields(userID string, fields map[dbuser.UsersField]interface{}) (bool, error)
	ListFamilyMembersByHostUserID(userID string) ([]*dbuser.User, error)
	BatchUpdateByFields(userIDs []string, fields map[dbuser.UsersField]interface{}) (int64, error)
}

type userService struct {
	UserRepository user.UserRepository
}

func NewUserService(db database.DB) UserService {
	return &userService{
		UserRepository: user.NewUserRepositoryWith(db),
	}
}

func (s *userService) GetActiveByID(userID string) (*dbuser.User, error) {
	return s.UserRepository.GetActiveByID(userID)
}

func (s *userService) GetByID(userID string) (*dbuser.User, error) {
	return s.UserRepository.GetByID(userID)
}

func (s *userService) GetByPhone(phone string) (*dbuser.User, error) {
	if users, err := s.UserRepository.GetUsersByPhone(phone); err != nil {
		return nil, err
	} else if len(users) != 0 {
		return users[0], nil
	}

	return nil, nil
}

func (s *userService) Create(user *dbuser.User) (*dbuser.User, error) {
	return s.UserRepository.Create(user)
}

func (s *userService) Update(u *dbuser.User) (bool, error) {
	var fields = map[dbuser.UsersField]interface{}{
		dbuser.UserFieldAutoRenew: u.AutoRenew,
		dbuser.UsersFieldPhone:    u.Phone.String,
		dbuser.UserFieldType:      u.Type,
		dbuser.UserFieldRole:      u.Role,
		dbuser.UserFieldExpiredAt: u.ExpiredAt.Time,
	}

	return s.UserRepository.UpdateByFields(u.ID, fields)
}

func (s *userService) ListFamilyMembersByHostUserID(userID string) ([]*dbuser.User, error) {
	return s.UserRepository.ListFamilyMembersByHostUserID(userID)
}

func (s *userService) WithTx(tx database.Tx) UserService {
	return &userService{
		UserRepository: s.UserRepository.WithTx(tx),
	}
}

func (s *userService) UpdateByFields(userID string, fields map[dbuser.UsersField]interface{}) (bool, error) {
	return s.UserRepository.UpdateByFields(userID, fields)
}

func (s *userService) BatchUpdateByFields(userIDs []string, fields map[dbuser.UsersField]interface{}) (int64, error) {
	return s.UserRepository.BatchUpdateByFields(userIDs, fields)
}
