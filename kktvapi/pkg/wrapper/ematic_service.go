package wrapper

import (
	"time"

	"github.com/KKTV/createsend-go/ematicagent"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/ematic"
	"github.com/KKTV/kktv-api-v3/pkg/log"
)

type EmaticService interface {
	Cancel(email string, expiredDate time.Time)
	IsClientExist() bool
}

type ematicService struct {
	ematicClient *ematicagent.AgentAPI
}

func NewEmaticService() EmaticService {
	return &ematicService{
		ematicClient: ematic.GetClient(),
	}
}

func (s *ematicService) Cancel(email string, expiredDate time.Time) {
	if email == "" {
		return
	}

	if err := s.ematicClient.Cancel(email); err != nil {
		log.Warn("ematicService: Cancel").Err(err).Send()
	}
	kv := map[string]interface{}{
		"paidexpireddate": expiredDate.Format(ematicagent.EmaticDateFormat),
	}

	if err := s.ematicClient.UpdateSubscriber(email, kv); err != nil {
		log.Warn("ematicService: UpdateSubscriber").Err(err).Send()
	}
}

func (s *ematicService) IsClientExist() bool {
	return s.ematicClient != nil
}
