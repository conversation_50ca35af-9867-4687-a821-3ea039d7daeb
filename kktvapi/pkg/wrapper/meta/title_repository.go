//go:generate mockgen -source title_repository.go -destination title_repository_mock.go -package meta
package meta

import (
	internalmeta "github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/meta"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/KKTV/kktv-api-v3/pkg/database"
	"github.com/KKTV/kktv-api-v3/pkg/elasticsearch"
	modelsearch "github.com/KKTV/kktv-api-v3/pkg/model/search"
)

type TitleRepository interface {
	ListAllOnListedTitles() ([]*modelsearch.Title, error)
	ListAllUnitedTitleIDs() ([]internalmeta.UnitedTitleGroup, error)
}

type titleRepository struct {
	internal internalmeta.TitleRepository
}

func NewTitleRepository(dbReader database.DB, cacheReader cache.Cacher, esClient elasticsearch.Client) TitleRepository {
	return &titleRepository{
		internal: internalmeta.NewTitleRepositoryWith(dbReader, cacheReader, esClient),
	}
}

func (r *titleRepository) ListAllOnListedTitles() ([]*modelsearch.Title, error) {
	return r.internal.ListAllOnListedTitles()
}

type UnitedTitleGroup = internalmeta.UnitedTitleGroup

func (r *titleRepository) ListAllUnitedTitleIDs() ([]internalmeta.UnitedTitleGroup, error) {
	return r.internal.ListAllUnitedTitleIDs()
}
