// Code generated by MockGen. DO NOT EDIT.
// Source: title_repository.go

// Package meta is a generated GoMock package.
package meta

import (
	reflect "reflect"

	meta "github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/meta"
	search "github.com/KKTV/kktv-api-v3/pkg/model/search"
	gomock "github.com/golang/mock/gomock"
)

// MockTitleRepository is a mock of TitleRepository interface.
type MockTitleRepository struct {
	ctrl     *gomock.Controller
	recorder *MockTitleRepositoryMockRecorder
}

// MockTitleRepositoryMockRecorder is the mock recorder for MockTitleRepository.
type MockTitleRepositoryMockRecorder struct {
	mock *MockTitleRepository
}

// NewMockTitleRepository creates a new mock instance.
func NewMockTitleRepository(ctrl *gomock.Controller) *MockTitleRepository {
	mock := &MockTitleRepository{ctrl: ctrl}
	mock.recorder = &MockTitleRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTitleRepository) EXPECT() *MockTitleRepositoryMockRecorder {
	return m.recorder
}

// ListAllOnListedTitles mocks base method.
func (m *MockTitleRepository) ListAllOnListedTitles() ([]*search.Title, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAllOnListedTitles")
	ret0, _ := ret[0].([]*search.Title)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAllOnListedTitles indicates an expected call of ListAllOnListedTitles.
func (mr *MockTitleRepositoryMockRecorder) ListAllOnListedTitles() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAllOnListedTitles", reflect.TypeOf((*MockTitleRepository)(nil).ListAllOnListedTitles))
}

// ListAllUnitedTitleIDs mocks base method.
func (m *MockTitleRepository) ListAllUnitedTitleIDs() ([]meta.UnitedTitleGroup, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAllUnitedTitleIDs")
	ret0, _ := ret[0].([]meta.UnitedTitleGroup)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAllUnitedTitleIDs indicates an expected call of ListAllUnitedTitleIDs.
func (mr *MockTitleRepositoryMockRecorder) ListAllUnitedTitleIDs() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAllUnitedTitleIDs", reflect.TypeOf((*MockTitleRepository)(nil).ListAllUnitedTitleIDs))
}
