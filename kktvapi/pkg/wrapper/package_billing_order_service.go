package wrapper

import (
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/user"
	"github.com/KKTV/kktv-api-v3/pkg/database"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
)

type PackageBillingOrderService interface {
	GetByBillingOrderNumber(billingOrderNumber string) (*dbuser.PackageBillingOrder, error)
	GetByBillingOrderNumbers(billingOrderNumbers []string) ([]*dbuser.PackageBillingOrder, error)
	Create(order *dbuser.PackageBillingOrder) error
}

type packageBillingOrderService struct {
	packageBillingOrderRepo user.PackageBillingOrderRepository
}

func NewPackageBillingOrderService(dbReader, dbWriter database.DB) PackageBillingOrderService {
	return &packageBillingOrderService{
		packageBillingOrderRepo: user.NewPackageBillingOrderRepositoryWith(dbReader, dbWriter),
	}
}

func (p *packageBillingOrderService) GetByBillingOrderNumber(billingOrderNumber string) (*dbuser.PackageBillingOrder, error) {
	return p.packageBillingOrderRepo.GetByBillingOrderNumber(billingOrderNumber)
}

func (p *packageBillingOrderService) GetByBillingOrderNumbers(billingOrderNumbers []string) ([]*dbuser.PackageBillingOrder, error) {
	return p.packageBillingOrderRepo.GetByBillingOrderNumbers(billingOrderNumbers)
}

func (p *packageBillingOrderService) Create(order *dbuser.PackageBillingOrder) error {
	return p.packageBillingOrderRepo.Create(order)
}
