//go:generate mockgen -source plan_authority_map_repository.go -destination plan_authority_map_repository_mock.go -package paidplan
package paidplan

import (
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/paidplan"
	"github.com/KKTV/kktv-api-v3/pkg/database"
	"github.com/KKTV/kktv-api-v3/pkg/model/authority"
)

type PlanAuthMapRepository interface {
	ListAuthoritiesByGenre(genre string) ([]authority.Authority, error)
}

type planAuthMapRepo struct {
	repo paidplan.PlanAuthorityMapRepository
}

func NewPlanAuthMapRepository(userDBReader database.DB) PlanAuthMapRepository {
	return &planAuthMapRepo{
		repo: paidplan.NewPlanAuthMapRepositoryWith(userDBReader),
	}
}

func (p *planAuthMapRepo) ListAuthoritiesByGenre(genre string) ([]authority.Authority, error) {
	return p.repo.ListAuthoritiesByGenre(genre)
}
