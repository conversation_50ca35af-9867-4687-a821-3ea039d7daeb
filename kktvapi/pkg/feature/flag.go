package feature

type Flag string

const (
	FlagUseBvDRM                  Flag = "bv_drm"
	FlagSupportBrowseEntryProtect Flag = "support_browse_entry_protect"
	FlagProvideV4WatchHistoryAPI  Flag = "v4_watch_history_api"
	FlagEnablingMembership        Flag = "enabling_membership"
	FlagAnimeAiringSchedule       Flag = "anime_airing_schedule"
	FlagEnablingAnimeAiring       Flag = "enabling_anime_airing"
)

func (f Flag) String() string {
	return string(f)
}
