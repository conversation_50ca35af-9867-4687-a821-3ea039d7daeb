//go:generate mockgen -source service.go -destination service_mock.go -package order
package order

import (
	"errors"
	"fmt"
	"time"

	"github.com/KKTV/kktv-api-v3/pkg/billing"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/KKTV/kktv-api-v3/pkg/clock"
	"github.com/KKTV/kktv-api-v3/pkg/key"
)

const (
	ttlBillingCustomerStatus = time.Hour * 24 // 1day
)

type GracePeriod struct {
	StartAt time.Time
	EndAt   time.Time
	OrderID string
}

type StatusInfo struct {
	GracePeriod *GracePeriod `json:"grace_period"`
	// other fields, e.g. CurrentOrder, Orders, PaymentMethod, etc.
}

// Service is a ubiquitous interface to provide order-related functionalities, such as getting user order status, purchasing.
// It is designed to be adaptable to both legacy products and billing products
type Service interface {
	GetUserOrderStatus(userID string) (*StatusInfo, error)
	// Purchase(userID string, productID string) error
}

type service struct {
	billingClient billing.Client
	cacheWriter   cache.Cacher
	cacheReader   cache.Cacher
	clock         clock.Clock
}

func NewService(billingClient billing.Client, userCacheReader, userCacheWriter cache.Cacher) Service {
	return &service{
		billingClient: billingClient,
		cacheReader:   userCacheReader,
		cacheWriter:   userCacheWriter,
		clock:         clock.New(),
	}
}

func (s *service) GetUserOrderStatus(userID string) (*StatusInfo, error) {
	cKey := key.UserBillingCustomerStatus(userID)

	customerStatus := new(billing.CustomerStatus)
	if err := s.cacheReader.Get(cKey, customerStatus); err != nil {
		if !errors.Is(err, cache.ErrCacheMiss) {
			return nil, err
		}

		customerStatus, err = s.billingClient.GetCustomerStatus(userID)
		if errors.Is(err, billing.ErrorCustomerNotFound) {
			customerStatus = nil
		} else if err != nil {
			return nil, fmt.Errorf("billing: get customer status failed, err: %w", err)
		}

		ttl := ttlBillingCustomerStatus
		if customerStatus != nil && customerStatus.ContractData != nil {
			now := s.clock.Now()
			if customerStatus.ContractData.Contract.GracePeriod != nil {
				graceEnd := time.Time(customerStatus.ContractData.Contract.GracePeriod.End)
				ttl = graceEnd.Sub(now)
			} else if customerStatus.ContractData.Contract.IsActive() {
				exp := time.Unix(customerStatus.ContractData.Contract.LastExpiredAt, 0)
				ttl = exp.Sub(now)
			}
		}

		if err = s.cacheWriter.Set(cKey, customerStatus, ttl); err != nil {
			return nil, err
		}
	}
	statusInfo := &StatusInfo{}
	if customerStatus != nil && customerStatus.ContractData != nil {
		if gracePeriod := customerStatus.ContractData.Contract.GracePeriod; gracePeriod != nil {
			statusInfo.GracePeriod = &GracePeriod{
				StartAt: time.Time(gracePeriod.Start),
				EndAt:   time.Time(gracePeriod.End),
				OrderID: gracePeriod.OrderNumber,
			}
		}
	}
	return statusInfo, nil
}
