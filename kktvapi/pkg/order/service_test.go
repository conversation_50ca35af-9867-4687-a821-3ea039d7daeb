package order

import (
	"testing"
	"time"

	"github.com/KKTV/kktv-api-v3/pkg/billing"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/KKTV/kktv-api-v3/pkg/clock"
	"github.com/KKTV/kktv-api-v3/pkg/datatype"
	"github.com/KKTV/kktv-api-v3/pkg/datetimer"
	"github.com/KKTV/kktv-api-v3/pkg/key"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
)

type ServiceTestSuite struct {
	suite.Suite
	srv Service

	ctrl              *gomock.Controller
	mockBillingClient *billing.MockClient
	mockCacheWriter   *cache.MockCacher
	mockCacheReader   *cache.MockCacher
	mockClock         *clock.MockClock
}

func TestServiceTestSuite(t *testing.T) {
	suite.Run(t, new(ServiceTestSuite))
}

func (suite *ServiceTestSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
	suite.mockBillingClient = billing.NewMockClient(suite.ctrl)
	suite.mockCacheWriter = cache.NewMockCacher(suite.ctrl)
	suite.mockCacheReader = cache.NewMockCacher(suite.ctrl)
	suite.mockClock = clock.NewMockClock(suite.ctrl)
	suite.srv = &service{
		billingClient: suite.mockBillingClient,
		cacheWriter:   suite.mockCacheWriter,
		cacheReader:   suite.mockCacheReader,
		clock:         suite.mockClock,
	}
}

func (suite *ServiceTestSuite) TearDownTest() {
	suite.ctrl.Finish()
}

func (suite *ServiceTestSuite) TestGetUserOrderStatus() {
	const (
		userID = "josie"
	)
	var (
		now               = time.Date(2024, 4, 30, 0, 0, 0, 0, datetimer.LocationTaipei)
		defaultGraceStart = time.Date(2024, 4, 29, 0, 0, 0, 0, datetimer.LocationTaipei)
		defaultGraceEnd   = defaultGraceStart.Add(time.Hour * 24 * 7)
	)

	suite.mockClock.EXPECT().Now().Return(now).AnyTimes()

	testcases := []struct {
		name  string
		given func()
		then  func(status *StatusInfo, err error)
	}{
		{
			name: "no cache of customer status, fetch once and got grace period data",
			given: func() {
				cKey := key.UserBillingCustomerStatus(userID)
				suite.mockCacheReader.EXPECT().Get(cKey, gomock.Any()).Return(cache.ErrCacheMiss)
				customerStatus := &billing.CustomerStatus{
					UserID: userID,
					ContractData: &billing.CustomerContractData{
						Contract: billing.Contract{
							GracePeriod: &billing.GracePeriod{
								Start:             datatype.DateTime(defaultGraceStart),
								End:               datatype.DateTime(defaultGraceEnd),
								OrderNumber:       "12345678",
								ProductIdentifier: "kktv.cc.vip.30",
							},
						},
					},
				}
				suite.mockBillingClient.EXPECT().GetCustomerStatus(userID).Return(customerStatus, nil)
				suite.mockCacheWriter.EXPECT().Set(cKey, customerStatus, defaultGraceEnd.Sub(now)).Return(nil)
			},
			then: func(status *StatusInfo, err error) {
				suite.Require().NoError(err)
				suite.Require().NotNil(status)
				suite.Require().NotNil(status.GracePeriod)
				suite.True(defaultGraceStart.Equal(status.GracePeriod.StartAt))
				suite.True(defaultGraceEnd.Equal(status.GracePeriod.EndAt))
				suite.Equal("12345678", status.GracePeriod.OrderID)
			},
		},
		{
			name: "no cache of customer status, fetch once and got expired contract data",
			given: func() {
				cKey := key.UserBillingCustomerStatus(userID)
				suite.mockCacheReader.EXPECT().Get(cKey, gomock.Any()).Return(cache.ErrCacheMiss)
				customerStatus := &billing.CustomerStatus{
					UserID: userID,
					ContractData: &billing.CustomerContractData{
						Contract: billing.Contract{
							LastExpiredAt: now.Add(time.Hour * -30).Unix(),
						},
					},
				}
				suite.mockBillingClient.EXPECT().GetCustomerStatus(userID).Return(customerStatus, nil)
				suite.mockCacheWriter.EXPECT().Set(cKey, customerStatus, ttlBillingCustomerStatus).Return(nil)
			},
			then: func(status *StatusInfo, err error) {
				suite.Require().NoError(err)
				suite.Require().NotNil(status)
			},
		},
		{
			name: "found no customer status in cache",
			given: func() {
				cKey := key.UserBillingCustomerStatus(userID)
				suite.mockCacheReader.EXPECT().Get(cKey, gomock.Any()).Return(nil)
			},
			then: func(status *StatusInfo, err error) {
				suite.Require().NoError(err)
				suite.Require().NotNil(status)
				suite.Require().Nil(status.GracePeriod)
			},
		},
	}
	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			tc.given()
			status, err := suite.srv.GetUserOrderStatus(userID)
			tc.then(status, err)
		})
	}
}
