package auditing

import (
	"testing"
	"time"

	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/stretchr/testify/require"
)

func TestGetDiffFields(t *testing.T) {
	type type1 struct {
		Name      string
		Age       int
		CreatedAt time.Time
	}
	type type2 struct {
		Info type1
		Addr string
	}

	testcases := []struct {
		name    string
		obj1    any
		obj2    any
		ignores []string
		want    []dbuser.AuditLogDifference
		wantErr bool
	}{
		{
			name: "got fields when obj1 and obj2 are the same type but different values",
			obj1: type1{
				Name:      "<PERSON>",
				Age:       18,
				CreatedAt: time.Date(2020, 4, 29, 0, 0, 0, 0, time.UTC),
			},
			obj2: type1{
				Name:      "<PERSON>",
				Age:       20,
				CreatedAt: time.Date(2020, 1, 6, 0, 0, 0, 0, time.UTC),
			},
			want: []dbuser.AuditLogDifference{
				{
					Field: "name",
					Old:   "<PERSON>",
					New:   "<PERSON>",
				},
				{
					Field: "age",
					Old:   18,
					New:   20,
				},
				{
					Field: "created_at",
					Old:   time.Date(2020, 4, 29, 0, 0, 0, 0, time.UTC),
					New:   time.Date(2020, 1, 6, 0, 0, 0, 0, time.UTC),
				},
			},
		},
		{
			name: "got fields for nested struct",
			obj1: &type2{
				Info: type1{
					Name: "John",
					Age:  18,
				},
				Addr: "Taipei",
			},
			obj2: &type2{
				Info: type1{
					Name: "Mary",
					Age:  18,
				},
				Addr: "Taiwan",
			},
			want: []dbuser.AuditLogDifference{
				{
					Field: "info.name",
					Old:   "John",
					New:   "Mary",
				},
				{
					Field: "addr",
					Old:   "Taipei",
					New:   "Taiwan",
				},
			},
		},
		{
			name: "got no fields when obj1 and obj2 are the same type and same values",
			obj1: type1{
				Name: "John", Age: 18,
			},
			obj2: type1{
				Name: "John", Age: 18,
			},
			want: []dbuser.AuditLogDifference{},
		},
		{
			name: "got error when different types",
			obj1: type1{
				Name: "John", Age: 18,
			},
			obj2: type2{
				Info: type1{
					Name: "John", Age: 18,
				},
			},
			wantErr: true,
		},
		{
			name: "has ignore fields",
			obj1: type1{
				Name: "John", Age: 18,
			},
			obj2: type1{
				Name: "May", Age: 19,
			},
			ignores: []string{"age"},
			want: []dbuser.AuditLogDifference{
				{
					Field: "name",
					Old:   "John",
					New:   "May",
				},
			},
		},
	}
	r := require.New(t)
	for _, tc := range testcases {
		t.Run(tc.name, func(t *testing.T) {
			got, err := GetDiffFields(tc.obj1, tc.obj2, tc.ignores...)
			if (err != nil) != tc.wantErr {
				t.Errorf("GetDiffFields() error = %v, wantErr %v", err, tc.wantErr)
				return
			}
			if tc.wantErr {
				return
			}
			r.ElementsMatch(tc.want, got)
		})
	}
}
