// Code generated by MockGen. DO NOT EDIT.
// Source: log_repository.go

// Package auditing is a generated GoMock package.
package auditing

import (
	reflect "reflect"

	dbuser "github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	gomock "github.com/golang/mock/gomock"
)

// MockLogRepository is a mock of LogRepository interface.
type MockLogRepository struct {
	ctrl     *gomock.Controller
	recorder *MockLogRepositoryMockRecorder
}

// MockLogRepositoryMockRecorder is the mock recorder for MockLogRepository.
type MockLogRepositoryMockRecorder struct {
	mock *MockLogRepository
}

// NewMockLogRepository creates a new mock instance.
func NewMockLogRepository(ctrl *gomock.Controller) *MockLogRepository {
	mock := &MockLogRepository{ctrl: ctrl}
	mock.recorder = &MockLogRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockLogRepository) EXPECT() *MockLogRepositoryMockRecorder {
	return m.recorder
}

// Insert mocks base method.
func (m *MockLogRepository) Insert(logs ...*dbuser.AuditLog) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{}
	for _, a := range logs {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Insert", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// Insert indicates an expected call of Insert.
func (mr *MockLogRepositoryMockRecorder) Insert(logs ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Insert", reflect.TypeOf((*MockLogRepository)(nil).Insert), logs...)
}
