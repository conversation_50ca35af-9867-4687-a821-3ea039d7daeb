package message

import "github.com/KKTV/kktv-api-v3/pkg/log"

type dummyMailer struct{}

func (d *dummyMailer) SendMessage(recipient, subject, content string) error {
	log.Error("dummy mailer: send message").Str("recipient", recipient).Str("subject", subject).Str("content", content).Send()
	return nil
}

func (d *dummyMailer) SendHTMLMessage(recipient, subject, content string) error {
	log.Error("dummy mailer: send html message").Str("recipient", recipient).Str("subject", subject).Str("content", content).Send()
	return nil
}
