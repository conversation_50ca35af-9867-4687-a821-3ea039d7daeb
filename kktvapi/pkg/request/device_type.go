package request

import (
	"net/http"
	"strings"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/platform"
)

func GetDeviceTypeFromUserAgent(r *http.Request) platform.DeviceType {
	ua := r.Header.Get("User-Agent")
	deviceType := platform.DeviceTypeWeb

	switch {
	case strings.Contains(ua, "Android"):
		if strings.Contains(ua, "com.kktv.kktv.tv") { // android tv
			deviceType = platform.DeviceTypeTV
		} else {
			deviceType = platform.DeviceTypeMobileApp
		}

	case strings.Contains(ua, "iOS") || strings.Contains(ua, "com.kktv.ios.kktv"): // iphone, ipad
		deviceType = platform.DeviceTypeMobileApp

	case strings.Contains(ua, "tvOS"): // apple tv
		deviceType = platform.DeviceTypeTV
	case strings.Contains(ua, "com.kktv.kktv.stb"):
		deviceType = platform.DeviceTypeMOD
	}
	return deviceType
}
