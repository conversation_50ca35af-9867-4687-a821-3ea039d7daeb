const {request} = require("../Request");

describe("TitlelistAPI.V4 for app", () => {
    const base = "/v4/a/title-lists";

    function expectResponseHasContent(response, titleAmount = 5) {
        expect(response.status).toBe(200);
        expect(response.body.data.title_list.name).not.toBe('');
        expect(response.body.data.title_list.titles.length).toBeGreaterThanOrEqual(titleAmount);
        expect(response.body.data.fallback).toBeUndefined();
    }

    it("should have content for 精選排行榜", async () => {
        let response = await request.get(encodeURI(base + "/ranking"));
        expectResponseHasContent(response, 10);
    })

})
