const {request} = require("../Request");

describe("ColdStartAPI.V4", () => {

    it("should get items for the GETTING content preferences API ", async () => {
        const url = "/v4/cold-start/content-preferences";

        let response = await request.get(encodeURI(url));

        expect(response.status).toBe(200);
        let optionGroup = response.body.data.option_groups;
        expect(optionGroup.length).toBeGreaterThanOrEqual(2);
        expect(optionGroup[0].id).toEqual('genre');
        expect(optionGroup[1].id).toEqual('theme');

    })

})
