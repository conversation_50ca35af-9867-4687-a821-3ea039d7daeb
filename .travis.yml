language: node_js
node_js:
  - "12.22.12"
dist: trusty

jobs:
  include:
    - stage: "API e2e tests"
      cache: yarn

notifications:
  slack:
    - rooms:
        - kkculture:0qUPIrQZyO80KCU4UEr9OS3v#kktv-ci-cd
      on_failure: change
      on_success: never
      on_pull_requests: false
      template:
        - "<!kktv-backends>"
        - "Build <%{build_url}|#%{build_number}> (<%{compare_url}|%{commit}>) of %{repository_slug}@%{branch} in PR <%{pull_request_url}|#%{pull_request_number}> by %{author} %{result} in %{duration}"
        - "Message: %{message}"
        - "Commit message: %{commit_message}"
      if: branch = main
    - rooms:
        - kkculture:0qUPIrQZyO80KCU4UEr9OS3v#kktv-ci-cd
      on_success: always
      on_failure: never
