# Project Layout

## Overview

This document outlines the project structure to facilitate code reuse, focusing on well-structured parts of the codebase. The project follows Go standard layout and Clean Architecture principles, with the main focus areas being:

- `/kktvapi/`
- `/pkg/`
- `/consoleapi/`

Legacy code outside these areas is intentionally excluded from this documentation.

## Key Directories & Dependencies

### `/kktvapi/`
Contains APIs for product clients.

### `/pkg/`
Houses general utility packages that do not contain KKTV business logic. The exception is `/pkg/model` which contains entity models.

### `/consoleapi/`
Follows Clean Architecture principles similar to `/kktvapi/`.

### `/kktvapi/pkg/`
Contains utility packages related to KKTV business logic. These could potentially be reused externally.

### `/kktvapi/internal/pkg`
Contains utility packages that are internal to `kktvapi` only, as opposed to `/kktvapi/pkg/` which might be reusable externally.

### `/pkg/` Subdirectories (General Utilities)

- `/pkg/amplitudelib`: Analytics event tracking integration with Amplitude.
- `/pkg/auth`: Authentication utilities and authorization mechanisms.
- `/pkg/aws`: AWS service integrations and utilities.
- `/pkg/billing`: Billing and payment processing abstractions.
- `/pkg/cache`: Redis cache abstraction layer providing common caching operations.
- `/pkg/clock`: Time operations abstraction layer for easier testing.
- `/pkg/database`: Database operation abstractions wrapping sqlx.
- `/pkg/datetimer`: Date and time manipulation utilities.
- `/pkg/encrypt`: Encryption utilities including AES encryption and password handling.
- `/pkg/httpreq`: HTTP request handling utilities for binding and validation.
- `/pkg/log`: Structured logging utilities based on zerolog.
- `/pkg/mail`: Email sending and template abstractions.
- `/pkg/render`: HTTP response rendering for JSON and text outputs.
- `/pkg/validator`: Structure validation based on go-playground/validator.
- `/pkg/key/redis_keys.go`: Redis key definitions for various services.

### `/kktvapi/pkg/` Subdirectories (KKTV-Specific Utilities)

- `/kktvapi/pkg/auditing`: Audit logging system for tracking changes to records, particularly user-related changes.
- `/kktvapi/pkg/auth`: Authentication utilities specific to KKTV, including management of authenticated applications.
- `/kktvapi/pkg/broadcasting`: Event broadcasting system for application-wide notifications.
- `/kktvapi/pkg/collection`: Content collection definitions and categorization by genre, country, etc.
- `/kktvapi/pkg/feature`: Feature flag management for conditional feature activation based on criteria like app version or user.
- `/kktvapi/pkg/kktverror`: KKTV-specific error handling and classification.
- `/kktvapi/pkg/message`: Communication services for sending emails and SMS messages.
- `/kktvapi/pkg/meta`: Content metadata management, particularly for airing/broadcasting schedule information.
- `/kktvapi/pkg/order`: Order and subscription management services for users.
- `/kktvapi/pkg/permission`: Access control and permission management based on membership status and role.
- `/kktvapi/pkg/request`: Request handling utilities specific to KKTV.
- `/kktvapi/pkg/serials`: Management of serialized content like TV series.
- `/kktvapi/pkg/wrapper`: Wrapper utilities for external services and APIs.

### `/kktvapi/internal/pkg/` Subdirectories (Internal Utilities for `kktvapi`)

- `/kktvapi/internal/pkg/announcement`: System announcement management for KKTV users.
- `/kktvapi/internal/pkg/apidoc`: API documentation generation and management utilities.
- `/kktvapi/internal/pkg/appauth`: Application-level authentication and authorization.
- `/kktvapi/internal/pkg/asset`: Digital asset management utilities for content resources.
- `/kktvapi/internal/pkg/auth`: Authentication infrastructure including token generation and validation.
- `/kktvapi/internal/pkg/billing`: Billing operations specific to kktvapi service.
- `/kktvapi/internal/pkg/coldstart`: System initialization and cold start utilities.
- `/kktvapi/internal/pkg/container`: Dependency injection container for managing service dependencies.
- `/kktvapi/internal/pkg/dbtest`: Database testing utilities for integration tests.
- `/kktvapi/internal/pkg/deeplink`: Deep linking functionality for app navigation.
- `/kktvapi/internal/pkg/ematic`: Integration with Ematic marketing service.
- `/kktvapi/internal/pkg/event`: Event tracking and handling within the kktvapi service.
- `/kktvapi/internal/pkg/image`: Image processing and manipulation utilities.
- `/kktvapi/internal/pkg/meta`: Metadata management specific to internal kktvapi operations.
- `/kktvapi/internal/pkg/middleware`: HTTP middleware components for request processing pipelines.
- `kktvapi/internal/pkg/middleware/key.go`: defination of middleware keys used in context.
- `/kktvapi/internal/pkg/oauth`: OAuth authentication protocol implementation.
- `/kktvapi/internal/pkg/onetimepassword`: One-time password generation and validation.
- `/kktvapi/internal/pkg/paidplan`: Paid subscription plan management utilities.
- `/kktvapi/internal/pkg/platform`: Platform-specific utilities for different client platforms.
- `/kktvapi/internal/pkg/product`: Product catalog and management functionality.
- `/kktvapi/internal/pkg/productpackage`: Product bundling and package management.
- `/kktvapi/internal/pkg/redeem`: Redemption code processing and validation.
- `/kktvapi/internal/pkg/refreshtoken`: Refresh token management for authentication.
- `/kktvapi/internal/pkg/repository`: Data access layer implementations using repository pattern.
- `/kktvapi/internal/pkg/request`: HTTP request processing utilities specific to internal operations.
- `/kktvapi/internal/pkg/rest`: REST API response formatting and standardization.
- `/kktvapi/internal/pkg/survey`: User survey management and processing.
- `/kktvapi/internal/pkg/token`: Token management for authentication and authorization.
- `/kktvapi/internal/pkg/user`: User account management utilities.
- `/kktvapi/internal/pkg/validation`: Input validation utilities for API requests.

## `/kktvapi/` Application Structure

### Top-Level Structure

- `/kktvapi/cmd/`: Application entry points and executable command-line tools. Contains the main application bootstrapping code and command implementations.
- `/kktvapi/config/`: Configuration files and templates for different environments and deployment scenarios.
- `/kktvapi/docs/`: API documentation, including specification files and generated documentation resources.
- `/kktvapi/internal/`: Private application code that cannot be imported by external packages or services. Contains the core business logic and API implementation details.
- `/kktvapi/pkg/`: Public libraries intended for reuse by other services. Contains utilities and abstractions that have applicability beyond just the KKTV API.
- `/kktvapi/web/`: Web-related resources, potentially including static assets, templates, and frontend code for web interfaces.

### `/kktvapi/internal/` Structure

- `/kktvapi/internal/apidoc/`: Internal tools and utilities for API documentation generation and management.
- `/kktvapi/internal/pkg/`: Shared internal libraries used across multiple API versions and modules (detailed in previous sections).
- `/kktvapi/internal/v0/`: The initial or legacy version of the API implementation.
- `/kktvapi/internal/v3/`: Version 3 of the API, containing endpoints, handlers, and business logic specific to this version.
- `/kktvapi/internal/v4/`: Version 4 of the API, representing the newer implementation with updated features and potentially improved architecture.
- `/kktvapi/internal/vendors/`: Third-party integrations and vendor-specific implementations, allowing for clean separation of external service connections.

### `/kktvapi/internal/v4/` Domain/Feature Modules

- `/kktvapi/internal/v4/auth/`: Handles authentication and authorization mechanisms for API access.
- `/kktvapi/internal/v4/billing/`: Manages billing operations, payment processing, and subscription management.
- `/kktvapi/internal/v4/coldstart/`: Handles system initialization and application startup processes.
- `/kktvapi/internal/v4/collection/`: Manages content collections and categorization of media content.
- `/kktvapi/internal/v4/family/`: Handles family sharing features and family account management.
- `/kktvapi/internal/v4/favorite/`: Manages user favorites and bookmarking functionality.
- `/kktvapi/internal/v4/library/`: Handles user content libraries and personalized content organization.
- `/kktvapi/internal/v4/mod/`: Provides moderation tools and content management functionality.
- `/kktvapi/internal/v4/oauth/`: Implements OAuth authentication protocols for third-party integrations.
- `/kktvapi/internal/v4/page/`: Manages page content and dynamic page composition for the application.
- `/kktvapi/internal/v4/presenter/`: Contains view presenters for formatting API responses.
- `/kktvapi/internal/v4/productpackage/`: Manages product bundles and package offerings.
- `/kktvapi/internal/v4/remoteconfig/`: Handles remote configuration and feature flag management.
- `/kktvapi/internal/v4/search/`: Provides search functionality and content discovery features.
- `/kktvapi/internal/v4/seo/`: Handles search engine optimization-related functionality.
- `/kktvapi/internal/v4/textcontent/`: Manages text-based content like descriptions and articles.
- `/kktvapi/internal/v4/title/`: Handles media titles and content metadata management.
- `/kktvapi/internal/v4/titlelist/`: Manages curated lists of titles and content recommendations.
- `/kktvapi/internal/v4/trial/`: Handles trial subscription management and trial-related functionality.
- `/kktvapi/internal/v4/user/`: Manages user accounts, profiles, and user-related operations.
- `/kktvapi/internal/v4/watchhistory/`: Tracks and manages user viewing history and progress.

## `/consoleapi/` Structure

### Top-Level Structure

- `/consoleapi/container.go`: Main dependency injection container that orchestrates the initialization of various services and repositories for the console API. It centralizes the configuration and creation of dependencies needed by the application's components.
- `/consoleapi/internal/`: Contains the core business logic implementation for the console API. This follows the common Go practice of using the "internal" directory for private code that cannot be imported by external packages or services.

### `/consoleapi/internal/` Structure

- `/consoleapi/internal/ads/`: Handles advertising-related functionality and management interfaces for the admin console.
- `/consoleapi/internal/errs/`: Provides error definitions, error handling utilities, and standardized error responses specific to the console API.
- `/consoleapi/internal/event/`: Manages event tracking, logging, and notification systems for administrative actions within the console.
- `/consoleapi/internal/pkg/`: Contains shared utilities and helper functions used across different modules of the console API.
- `/consoleapi/internal/productpackage/`: Manages product bundle configurations and package offerings through the admin console interface.
- `/consoleapi/internal/rest/`: Implements REST API handling, including response formatting, middleware, and routing for the console API endpoints.
- `/consoleapi/internal/title/`: Handles content title management, including creation, updates, and metadata management through administrative interfaces.
- `/consoleapi/internal/titlelist/`: Manages curated content lists and recommendations that can be configured through the admin console.
- `/consoleapi/internal/user/`: Handles administrative user management, permissions, and role-based access control for the console interface.